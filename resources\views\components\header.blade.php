    <!-- Header Start -->
	<header class="main-header" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
		<div class="header-sticky">
			<nav class="navbar navbar-expand-lg">
				<div class="container">
					<!-- Logo Start -->
					<a class="navbar-brand" href="./">
						<img src="{{ asset("assets/images/sharaf_logo.png") }}" width="181" height="59" alt="Logo">
						<!-- <img src="images/logo.svg" alt="Logo"> -->
					</a>
					<!-- Logo End -->

					<!-- Main Menu Start -->
					<div class="collapse navbar-collapse main-menu">
                        <div class="nav-menu-wrapper">
                            <ul class="navbar-nav {{ app()->getLocale() == 'ar' ? 'ms-auto' : 'mr-auto' }}" id="menu">
                                <li class="nav-item"><a class="nav-link" href="{{ url(app()->getLocale()) }}">{{ __('messages.Home') }}</a></li>
                                <li class="nav-item"><a class="nav-link" href="{{ url(app()->getLocale() . '/Services') }}">{{ __('messages.Services') }}</a></li>
                                <li class="nav-item"><a class="nav-link" href="{{ url(app()->getLocale() . '/Diseases-Conditions') }}">{{ __('messages.Diseases Conditions') }}</a></li>
                                <li class="nav-item"><a class="nav-link" href="{{ url(app()->getLocale() . '/Practitioners') }}">{{ __('messages.Practitioners') }}</a></li>
                                <li class="nav-item"><a class="nav-link" href="{{ url(app()->getLocale() . '/Galleries') }}">{{ __('messages.Galleries') }}</a></li>
                                <li class="nav-item"><a class="nav-link" href="{{ url(app()->getLocale() . '/About-Us') }}">{{ __('messages.About Us') }}</a></li>
                                <li class="nav-item"><a class="nav-link" href="{{ url(app()->getLocale() . '/Contact-Us') }}">{{ __('messages.Contact Us') }}</a></li>
                            </ul>
                        </div>
                        {{-- <!-- Let’s Start Button Start -->
                        <div class="header-btn d-inline-flex">
                            <a href="tel:+97317336601" class="btn-default"><span>+(973) 17336601</span></a>
                        </div>
                        <!-- Let’s Start Button End --> --}}
                        <!-- Patient Portal Button Start -->
                        <div class="header-btn d-inline-flex ms-3">
                            <a href="https://alsharafcenter.imedicalbh.com/portal/" target="_blank" class="portal-btn">
                                <span>
                                    <i class="fas fa-user-circle"></i>
                                    {{ __('messages.patient_portal') }}
                                </span>
                            </a>
                        </div>
                        <!-- Patient Portal Button End -->
					</div>
					<!-- Main Menu End -->
					<div class="navbar-toggle"></div>
				</div>
			</nav>
			<div class="responsive-menu"></div>
		</div>
	</header>
	<!-- Header End -->


<style>
    /* Adjust navbar items spacing */
    .navbar-nav .nav-item {
        margin: 0 5px; /* Reduce margin between items */
    }

    .nav-link {
        padding: 8px 10px !important; /* Reduce padding */
        font-size: 15px; /* Slightly reduce font size if needed */
    }

    /* Adjust container width for larger screens */
    @media (min-width: 1200px) {
        .container {
            max-width: 1460px;
        }
    }

    /* Responsive adjustments */
    @media (max-width: 1200px) {
        .nav-link {
            padding: 8px 8px !important;
            font-size: 14px;
        }
    }

    /* Keep your existing RTL styles */
    [dir="rtl"] .navbar-nav {
        padding-right: 0;
    }

    [dir="rtl"] .navbar-brand {
        margin-right: 0;
        margin-left: 1rem;
    }

    [dir="rtl"] .header-btn {
        margin-right: auto;
        margin-left: 0;
    }

    [dir="rtl"] .nav-link {
        font-family: var(--arabic-font);
    }

    @media (max-width: 991px) {
        [dir="rtl"] .navbar-collapse {
            text-align: right;
        }

        [dir="rtl"] .navbar-toggle {
            margin-left: 0;
            margin-right: auto;
        }
    }
</style>









