<?php

namespace App\Http\Controllers;

use App\Models\BodyPart;
use Illuminate\Http\Request;

class BodyPartsController extends Controller
{
    public function show($locale, $part)
    {
        try {
            $id = BodyPart::where('slug', $part)->firstOrFail()->id;
            $bodyPart = BodyPart::with('activeCommonCauses')->find($id);

            // Add this debug line
            \Log::info('Active Common Causes:', [
                'count' => $bodyPart->activeCommonCauses->count(),
                'data' => $bodyPart->activeCommonCauses->toArray()
            ]);

            return view('body-parts.show', compact('bodyPart'));
        } catch (\Exception $e) {
            \Log::error('Error in BodyPartsController@show', [
                'slug' => $part,
                'error' => $e->getMessage()
            ]);

            abort(404);
        }
    }
}



