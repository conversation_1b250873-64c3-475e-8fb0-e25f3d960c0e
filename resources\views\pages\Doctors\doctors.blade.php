@extends('layouts.app')  <!-- Extend the base layout -->


@section('content')
    <!-- Page Header Start -->
	<div class="page-header team-page-header bg-radius-section parallaxie" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
		<div class="container">
			<div class="row align-items-center">
				<div class="col-lg-12">
					<!-- Page Header Box Start -->
					<div class="page-header-box">
						<h1 class="{{ app()->getLocale() === 'ar' ? 'arabic-title' : 'text-anime-style-2' }}" data-cursor="-opaque">{{ __('messages.Doctors') }}</h1>
						<!-- Custom breadcrumb for RTL/LTR support -->
						@if(app()->getLocale() == 'ar')
							<!-- Arabic RTL breadcrumb -->
							<nav class="wow fadeInUp custom-rtl-breadcrumb">
								<div class="rtl-breadcrumb">
									<span style="color: #fff !important; font-size: 16px;">{{ __('messages.Doctors') }}</span>
									<span class="separator" style="color: rgba(255, 255, 255, 0.7) !important; font-size: 16px;">/</span>
									<a href="{{ url(app()->getLocale()) }}" style="color: rgba(255, 255, 255, 0.7) !important; text-decoration: none !important; font-size: 16px;">{{ __('messages.Home') }}</a>
								</div>
							</nav>
						@else
							<!-- English LTR breadcrumb -->
							<nav class="wow fadeInUp">
								<ol class="breadcrumb">
									<li class="breadcrumb-item"><a href="{{ url(app()->getLocale()) }}">{{ __('messages.Home') }}</a></li>
									<li class="breadcrumb-item active" aria-current="page">{{ __('messages.Doctors') }}</li>
								</ol>
							</nav>
						@endif
					</div>
					<!-- Page Header Box End -->
				</div>
			</div>
		</div>
	</div>
	<!-- Page Header End -->

        <!-- Page Team Start -->
        <div class="page-team bg-radius-section" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
            <div class="container">
                <div class="row">
                    @foreach($staff as $doctor)
                    <div class="col-lg-3 col-md-6">
                        <!-- Team Member Item Start -->
                        <div class="team-member-item wow fadeInUp">
                            <!-- Team Image Start -->
                            <div class="team-image">
                                <figure class="image-anime">
                                    @if($doctor->photo)
                                    <img src="{{ $doctor->photo }}"
                                         alt="{{ app()->getLocale() === 'ar' ? ($doctor->name_ar ?? $doctor->name_en) : $doctor->name_en }}"
                                         loading="lazy"
                                         onerror="this.onerror=null; this.src='https://ui-avatars.com/api/?background=random&name={{ urlencode(app()->getLocale() === 'ar' ? ($doctor->name_ar ?? $doctor->name_en) : $doctor->name_en) }}';">
                                @else
                                    <img src="https://ui-avatars.com/api/?background=random&name={{ urlencode(app()->getLocale() === 'ar' ? ($doctor->name_ar ?? $doctor->name_en) : $doctor->name_en) }}"
                                         alt="{{ app()->getLocale() === 'ar' ? ($doctor->name_ar ?? $doctor->name_en) : $doctor->name_en }}"
                                         loading="lazy">
                                @endif
                                </figure>

                                <!-- View Profile Button Start -->
                                <a href="{{ route('doctor.profile', ['locale' => app()->getLocale(), 'id' => $doctor->id]) }}" class="view-profile-btn {{ app()->getLocale() === 'ar' ? 'view-profile-btn-rtl' : '' }}">
                                    <span>{{ __('messages.View Profile') }}</span>
                                    <i class="fas {{ app()->getLocale() === 'ar' ? 'fa-arrow-left' : 'fa-arrow-right' }}"></i>
                                </a>
                                <!-- View Profile Button End -->

                                <!-- Team Social Icon Start -->
                                <div class="team-social-icon">
                                    <ul>
                                        @if($doctor->facebook_url)
                                            <li><a href="{{ $doctor->facebook_url }}" class="social-icon" target="_blank"><i class="fa-brands fa-facebook-f"></i></a></li>
                                        @else
                                            <li><a href="#" class="social-icon"><i class="fa-brands fa-facebook-f"></i></a></li>
                                        @endif
                                        @if($doctor->youtube_url)
                                            <li><a href="{{ $doctor->youtube_url }}" class="social-icon" target="_blank"><i class="fa-brands fa-youtube"></i></a></li>
                                        @else
                                            <li><a href="#" class="social-icon"><i class="fa-brands fa-youtube"></i></a></li>
                                        @endif
                                        @if($doctor->instagram_url)
                                            <li><a href="{{ $doctor->instagram_url }}" class="social-icon" target="_blank"><i class="fa-brands fa-instagram"></i></a></li>
                                        @else
                                            <li><a href="#" class="social-icon"><i class="fa-brands fa-instagram"></i></a></li>
                                        @endif
                                        @if($doctor->twitter_url)
                                            <li><a href="{{ $doctor->twitter_url }}" class="social-icon" target="_blank"><i class="fa-brands fa-x-twitter"></i></a></li>
                                        @else
                                            <li><a href="#" class="social-icon"><i class="fa-brands fa-x-twitter"></i></a></li>
                                        @endif
                                    </ul>
                                </div>
                                <!-- Team Social Icon End -->
                            </div>
                            <!-- Team Image End -->

                            <!-- Team Content Start -->
                            <div class="team-content">
                                <h3>{{ app()->getLocale() === 'ar' ? ($doctor->name_ar ?? $doctor->name_en) : $doctor->name_en }}</h3>
                                <p>{{ app()->getLocale() === 'ar' ? ($doctor->department->name_ar ?? $doctor->department->name_en ?? 'متخصص') : ($doctor->department->name_en ?? 'Specialist') }}</p>
                            </div>
                            <!-- Team Content End -->
                        </div>
                        <!-- Team Member Item End -->
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
        <!-- Page Team End -->
@endsection

@push('styles')
<style>
    /* RTL styles for doctors page */
    [dir="rtl"] .page-header-box h1,
    [dir="rtl"] .page-header-box .breadcrumb {
        text-align: right;
    }

    /* Fix for Arabic title rendering */
    .arabic-title {
        font-family: var(--arabic-font) !important;
        letter-spacing: 0 !important;
        word-spacing: normal !important;
        text-align: right !important;
        animation: none !important;
        display: inline-block !important;
        white-space: nowrap !important;
        direction: rtl !important;
    }

    /* Team content RTL styles */
    [dir="rtl"] .team-content h3,
    [dir="rtl"] .team-content p {
        text-align: right;
        font-family: var(--arabic-font);
    }

    /* View profile button RTL styles */
    [dir="rtl"] .view-profile-btn {
        flex-direction: row-reverse;
    }

    [dir="rtl"] .view-profile-btn i {
        transform: rotate(180deg);
        margin-right: 0;
        margin-left: 5px;
    }

    /* Breadcrumb RTL adjustments */
    [dir="rtl"] .breadcrumb-item+.breadcrumb-item::before {
        float: right;
        padding-right: 0;
        padding-left: 0.5rem;
    }

    [dir="rtl"] .breadcrumb-item {
        float: right;
    }
</style>
@endpush


