window.addEventListener('DOMContentLoaded', function() {
    if (typeof tinymce !== 'undefined') {
        tinymce.PluginManager.add('videoTemplate', function(editor) {
            editor.on('PreProcess', function(e) {
                var content = e.node;

                // Find all iframes
                var iframes = content.getElementsByTagName('iframe');
                for (var i = 0; i < iframes.length; i++) {
                    var iframe = iframes[i];
                    // Add video-embed class if not present
                    if (!iframe.hasAttribute('class')) {
                        iframe.setAttribute('class', 'video-embed');
                    }

                    // Wrap in responsive container
                    var wrapper = editor.dom.create('div', {
                        'class': 'video-container'
                    });
                    iframe.parentNode.insertBefore(wrapper, iframe);
                    wrapper.appendChild(iframe);
                }
            });

            // Clean up the HTML output
            editor.on('GetContent', function(e) {
                e.content = e.content.replace(/(<iframe[^>]*)(>)/g, '$1 class="video-embed"$2');
            });
        });
    }
});
