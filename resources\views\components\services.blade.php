@push('styles')
<style>
    /* RTL styles for services section */
    [dir="rtl"] .service-content h3,
    [dir="rtl"] .service-content p {
        text-align: right;
        font-family: var(--arabic-font);
    }

    [dir="rtl"] .service-btn {
        text-align: left;
    }

    [dir="rtl"] .service-item {
        direction: rtl;
    }

    [dir="rtl"] .section-title h3,
    [dir="rtl"] .section-title h2,
    [dir="rtl"] .section-title p {
        text-align: right;
        font-family: var(--arabic-font);
    }

    [dir="rtl"] .section-btn {
        text-align: left;
    }

    /* Fix for Arabic title rendering */
    [dir="rtl"] .text-anime-style-3 {
        font-family: var(--arabic-font) !important;
        letter-spacing: normal !important;
        word-spacing: normal !important;
        text-align: right !important;
    }
</style>
@endpush

    <!-- Our Services Section Start -->
    <div class="our-services bg-radius-section">
        <div class="container">
            <div class="row section-row align-items-center" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
                <div class="col-lg-6">
                    <!-- Section Title Start -->
                    <div class="section-title">
                        <h3 class="wow fadeInUp">{{ __('messages.services') }}</h3>
                        <h2 class="{{ app()->getLocale() === 'ar' ? 'arabic-title' : 'text-anime-style-3' }}" data-cursor="-opaque">{{ __('messages.comprehensive_care') }}</h2>
                        <p class="wow fadeInUp" data-wow-delay="0.25s">{{ __('messages.services_description') }}</p>
                    </div>
                    <!-- Section Title End -->
                </div>

                <div class="col-lg-6">
                    <!-- Section Btn Start -->
                    <div class="section-btn wow fadeInUp">
                        <a href="#" class="btn-default"><span>{{ __('messages.make_appointment') }}</span></a>
                    </div>
                    <!-- Section Btn End -->
                </div>
            </div>

            <div class="row">
                @php
                    $services = \App\Models\Service::where('is_active', true)
                        ->orderBy('display_order')
                        ->get();
                @endphp

                @foreach($services->skip(1) as $service)
                    <div class="col-lg-4 col-md-6">
                        <!-- Service Item Start -->
                        <div class="service-item wow fadeInUp" @if(!$loop->first) data-wow-delay="{{ $loop->index * 0.25 }}s" @endif>
                            <!-- Service Item Image Start -->
                            <div class="service-item-image">
                                <figure>
                                    <img src="{{ Storage::url($service->featured_image) }}" alt="{{ $service->{"title_" . app()->getLocale()} }}" loading="lazy" onerror="this.onerror=null; this.src='{{ asset('assets/images/service-img-1.jpg') }}'">
                                </figure>
                            </div>
                            <!-- Service Item Image End -->

                            <!-- Service Item Icon Start -->
                            <div class="icon-box">
                                <img src="{{ Storage::url($service->icon) }}" alt="{{ $service->{"title_" . app()->getLocale()} }} Icon">
                            </div>
                            <!-- Service Item Icon End -->

                            <!-- Service Body Start -->
                            <div class="service-body">
                                <!-- Service Content Start -->
                                <div class="service-content" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
                                    <h3>{{ $service->{"title_" . app()->getLocale()} }}</h3>
                                    <p>{{ Str::words(strip_tags($service->{"description_" . app()->getLocale()}), 10, '...') }}</p>
                                </div>
                                <!-- Service Content End -->

                                <!-- Service Btn Start -->
                                <div class="service-btn">
                                    <a href="{{ route('service.show', ['locale' => app()->getLocale(), 'slug' => $service->slug]) }}">
                                        <img src="{{ asset('assets/images/arrow-readmore-btn-left.svg') }}" alt="Read More">
                                    </a>
                                </div>
                                <!-- Service Btn End -->
                            </div>
                            <!-- Service Body End -->
                        </div>
                        <!-- Service Item End -->
                    </div>
                @endforeach

                <div class="col-lg-12">
                    <!-- More Service Btn Start -->
                    <div class="more-service-btn wow fadeInUp" data-wow-delay="1.5s">
                        <a href="{{ route('services', ['locale' => app()->getLocale()]) }}">
                            {{ __('messages.explore_services') }}
                            <img src="{{ asset('assets/images/arrow-long-white.svg') }}" alt="Arrow">
                        </a>
                    </div>
                    <!-- More Service Btn End -->
                </div>
            </div>
        </div>
    </div>
    <div class="bg-section bg-radius-section"></div>
    <!-- Our Services Section End -->





