<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1000" height="1000" viewBox="0 0 1000 1000" xml:space="preserve">
<desc>Created with Fabric.js 3.5.0</desc>
<defs>
</defs>
<rect x="0" y="0" width="100%" height="100%" fill="rgba(255,255,255,0)"/>
<g transform="matrix(-68.3423 0 0 68.3423 500.0002 500.0002)" id="927915">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; is-custom-font: none; font-file-url: none; fill: rgb(255,255,255); fill-rule: evenodd; opacity: 1;" vector-effect="non-scaling-stroke" transform=" translate(-7.4204, -7.0001)" d="M 6.99983 1.11403 L 6.40709 1.70677 L 8.63327 3.93295 L 10.8594 6.15912 L 5.81441 6.15923 L 0.769371 6.15934 L 0.76937 7.0001 L 0.76937 7.84085 L 5.81441 7.84096 L 10.8594 7.84107 L 8.63327 10.0672 L 6.40709 12.2934 L 6.99983 12.8862 L 7.59258 13.4789 L 10.832 10.2395 L 14.0714 7.0001 L 10.832 3.76069 L 7.59258 0.521288 L 6.99983 1.11403 Z" stroke-linecap="round"/>
</g>
</svg>