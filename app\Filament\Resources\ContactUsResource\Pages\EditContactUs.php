<?php

namespace App\Filament\Resources\ContactUsResource\Pages;

use App\Filament\Resources\ContactUsResource;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Support\Facades\Mail;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Log;

class EditContactUs extends EditRecord
{
    protected static string $resource = ContactUsResource::class;

    public function sendEmail(): void
    {
        try {
            // Get content based on selected language
            $emailContent = $this->data['reply_language'] === 'en'
                ? $this->data['email_reply_en']
                : "<div dir='rtl' style='text-align: right;'>{$this->data['email_reply_ar']}</div>";

            // Get subject based on selected language
            $subject = $this->data['reply_language'] === 'en'
                ? $this->data['email_subject_en']
                : $this->data['email_subject_ar'];

            // Debug log
            Log::info('Preparing to send email', [
                'to' => $this->record->email,
                'subject' => $subject,
                'content' => $emailContent
            ]);

            // Send the email
            Mail::html($emailContent, function ($message) use ($subject) {
                $message->to($this->record->email)
                       ->subject($subject)
                       ->from(config('mail.from.address'), config('mail.from.name'));
            });

            // Debug log
            Log::info('Email sent successfully');

            // Update the email sent timestamp
            $this->record->email_sent_at = now();
            $this->record->save();

            // Show success notification
            Notification::make()
                ->title('Email sent successfully')
                ->success()
                ->send();

        } catch (\Exception $e) {
            // Debug log
            Log::error('Failed to send email', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Show error notification
            Notification::make()
                ->title('Failed to send email')
                ->danger()
                ->body($e->getMessage())
                ->send();
        }
    }

    protected function getHeaderActions(): array
    {
        return [];
    }
}



