    <!-- Our Testimonials Section Start -->
    <div class="our-testimonial bg-radius-section" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
        <div class="container">
            <div class="row">
                <div class="col-lg-5">
                    <div class="testimonial-content">
                        <!-- Section Title Start -->
                        <div class="section-title">
                            <h3 class="wow fadeInUp">{{ __('messages.patient testimonials') }}</h3>
                            <h2 class="{{ app()->getLocale() === 'ar' ? 'arabic-title' : 'text-anime-style-3' }}" data-cursor="-opaque">{{ __('messages.Our Patient Success Stories') }}</h2>
                            <p class="wow fadeInUp" data-wow-delay="0.25s">{{ __('messages.testimonials_description') }}</p>
                        </div>
                        <!-- Section Title End -->
                    </div>
                </div>

                <div class="col-lg-7">
                    <!-- Testimonial Slider Start -->
                    <div class="testimonial-slider">
                        <div class="swiper">
                            <div class="swiper-wrapper" data-cursor-text="Drag">
                                <!-- Testimonial Slide Start -->
                                <div class="swiper-slide">
                                    <div class="testimonial-item">
                                        <div class="testimonial-header">
                                            <div class="testimonial-rating">
                                                <i class="fa-solid fa-star"></i>
                                                <i class="fa-solid fa-star"></i>
                                                <i class="fa-solid fa-star"></i>
                                                <i class="fa-solid fa-star"></i>
                                                <i class="fa-solid fa-star"></i>
                                            </div>
                                            <div class="testimonial-content">
                                                <p>{{ __('messages.testimonial_1') }}</p>
                                            </div>
                                        </div>
                                        <div class="testimonial-body">
                                            <div class="author-image">
                                                <figure class="image-anime">
                                                    <img src="{{ asset("assets/images/author-1.jpg") }}" alt="Patient Testimonial">
                                                </figure>
                                            </div>
                                            <div class="author-content">
                                                <h3>{{ __('messages.ahmed_m') }}</h3>
                                                <p>{{ __('messages.business executive') }}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Testimonial Slide End -->

                                <!-- Testimonial Slide Start -->
                                <div class="swiper-slide">
                                    <div class="testimonial-item">
                                        <div class="testimonial-header">
                                            <div class="testimonial-rating">
                                                <i class="fa-solid fa-star"></i>
                                                <i class="fa-solid fa-star"></i>
                                                <i class="fa-solid fa-star"></i>
                                                <i class="fa-solid fa-star"></i>
                                                <i class="fa-solid fa-star"></i>
                                            </div>
                                            <div class="testimonial-content">
                                                <p>{{ __('messages.testimonial_2') }}</p>
                                            </div>
                                        </div>
                                        <div class="testimonial-body">
                                            <div class="author-image">
                                                <figure class="image-anime">
                                                    <img src="{{ asset("assets/images/author-2.jpg") }}" alt="Patient Testimonial">
                                                </figure>
                                            </div>
                                            <div class="author-content">
                                                <h3>{{ __('messages.fatima_r') }}</h3>
                                                <p>{{ __('messages.retired educator') }}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Testimonial Slide End -->

                                <!-- Testimonial Slide Start -->
                                <div class="swiper-slide">
                                    <div class="testimonial-item">
                                        <div class="testimonial-header">
                                            <div class="testimonial-rating">
                                                <i class="fa-solid fa-star"></i>
                                                <i class="fa-solid fa-star"></i>
                                                <i class="fa-solid fa-star"></i>
                                                <i class="fa-solid fa-star"></i>
                                                <i class="fa-solid fa-star"></i>
                                            </div>
                                            <div class="testimonial-content">
                                                <p>{{ __('messages.testimonial_3') }}</p>
                                            </div>
                                        </div>
                                        <div class="testimonial-body">
                                            <div class="author-image">
                                                <figure class="image-anime">
                                                    <img src="{{ asset("assets/images/author-3.jpg") }}" alt="Patient Testimonial">
                                                </figure>
                                            </div>
                                            <div class="author-content">
                                                <h3>{{ __('messages.khalid_s') }}</h3>
                                                <p>{{ __('messages.professional athlete') }}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Testimonial Slide End -->

                                <!-- Testimonial Slide Start -->
                                <div class="swiper-slide">
                                    <div class="testimonial-item">
                                        <div class="testimonial-header">
                                            <div class="testimonial-rating">
                                                <i class="fa-solid fa-star"></i>
                                                <i class="fa-solid fa-star"></i>
                                                <i class="fa-solid fa-star"></i>
                                                <i class="fa-solid fa-star"></i>
                                                <i class="fa-solid fa-star"></i>
                                            </div>
                                            <div class="testimonial-content">
                                                <p>{{ __('messages.testimonial_4') }}</p>
                                            </div>
                                        </div>
                                        <div class="testimonial-body">
                                            <div class="author-image">
                                                <figure class="image-anime">
                                                    <img src="{{ asset("assets/images/author-4.jpg") }}" alt="Patient Testimonial">
                                                </figure>
                                            </div>
                                            <div class="author-content">
                                                <h3>{{ __('messages.mariam_h') }}</h3>
                                                <p>{{ __('messages.corporate manager') }}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Testimonial Slide End -->
                            </div>
                            <div class="testimonial-btn">
                                @if(app()->getLocale() == 'ar')
                                    <div class="testimonial-button-prev rtl-prev-btn" style="transform: rotate(180deg);"></div>
                                    <div class="testimonial-button-next rtl-next-btn" style="transform: rotate(180deg); margin-right: 20px;"></div>
                                @else
                                    <div class="testimonial-button-prev"></div>
                                    <div class="testimonial-button-next"></div>
                                @endif
                            </div>
                        </div>
                    </div>
                    <!-- Testimonial Slider End -->
                </div>
            </div>
        </div>
    </div>
    <!-- Our Testimonials Section End -->

    <!-- Google Reviews Section Start -->
    <div class="google-reviews bg-radius-section mt-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 text-center mb-4">
                    <div class="section-title">
                        <h3 class="wow fadeInUp">{{ __('messages.google reviews') }}</h3>
                        <h2 class="{{ app()->getLocale() === 'ar' ? 'arabic-title' : 'text-anime-style-3' }}" data-cursor="-opaque">{{ __('messages.What Our Patients Say on Google') }}</h2>
                    </div>
                </div>
                <div class="col-lg-12">
                    <div class="google-reviews-container">
                        <!-- Google Reviews Widget -->
                        <div class="google-reviews-widget">
                            <div id="google-reviews"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Google Reviews Section End -->



@push('styles')
<style>
    /* RTL styles for testimonials section */
    [dir="rtl"] .testimonial-content .section-title h3,
    [dir="rtl"] .testimonial-content .section-title h2,
    [dir="rtl"] .testimonial-content .section-title p {
        text-align: right;
        font-family: var(--arabic-font);
    }

    [dir="rtl"] .testimonial-content {
        text-align: right;
    }

    [dir="rtl"] .testimonial-item .testimonial-header .testimonial-content p {
        text-align: right;
        font-family: var(--arabic-font);
    }

    [dir="rtl"] .testimonial-item .testimonial-body {
        flex-direction: row-reverse;
    }

    [dir="rtl"] .testimonial-item .testimonial-body .author-content {
        text-align: right;
    }

    [dir="rtl"] .testimonial-item .testimonial-body .author-content h3,
    [dir="rtl"] .testimonial-item .testimonial-body .author-content p {
        font-family: var(--arabic-font);
    }

    /* Custom navigation buttons for RTL */
    .rtl-prev-btn::before {
        transform: rotate(180deg) !important;
    }

    .rtl-next-btn::before {
        transform: rotate(0deg) !important;
    }

    /* Position adjustments for RTL navigation */
    [dir="rtl"] .testimonial-btn {
        display: flex;
        flex-direction: row-reverse;
    }

    /* Google Reviews styles */
    .google-reviews {
        padding: 80px 0;
        background-color: #f8f9fa;
    }

    .google-reviews-container {
        margin-top: 40px;
    }

    .google-review-item {
        background-color: #fff;
        border-radius: 15px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
        padding: 25px;
        height: 100%;
    }

    .google-reviews-swiper .swiper-button-next,
    .google-reviews-swiper .swiper-button-prev {
        color: var(--primary-color);
    }

    /* RTL support for Google reviews */
    [dir="rtl"] .google-review-item .testimonial-header .testimonial-content p {
        text-align: right;
        font-family: var(--arabic-font);
    }

    [dir="rtl"] .google-review-item .testimonial-body {
        flex-direction: row-reverse;
    }

    [dir="rtl"] .google-review-item .testimonial-body .author-content {
        text-align: right;
    }
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Swiper for testimonials
    const testimonialSwiper = new Swiper('.testimonial-slider .swiper', {
        slidesPerView: 1,
        spaceBetween: 30,
        loop: true,
        speed: 1000,
        autoplay: {
            delay: 5000,
            disableOnInteraction: false,
        },
        navigation: {
            nextEl: '.testimonial-button-next',
            prevEl: '.testimonial-button-prev',
        },
    });

    // Load Google Reviews
    loadGoogleReviews();
});

function loadGoogleReviews() {
    // Get Place ID from configuration
    const placeId = '{{ config('services.google.place_id') }}';

    // Create script element to load Google Places API
    const script = document.createElement('script');
    script.src = `https://maps.googleapis.com/maps/api/js?key={{ config('services.google.maps_api_key') }}&libraries=places&callback=initGoogleReviews`;
    script.async = true;
    script.defer = true;
    document.head.appendChild(script);

    // Define the callback function
    window.initGoogleReviews = function() {
        const service = new google.maps.places.PlacesService(document.createElement('div'));
        service.getDetails({
            placeId: placeId,
            fields: ['review']
        }, displayReviews);
    };

    function displayReviews(place, status) {
        if (status === google.maps.places.PlacesServiceStatus.OK && place.reviews) {
            const reviewsContainer = document.getElementById('google-reviews');

            // Create a slider for Google reviews
            const sliderContainer = document.createElement('div');
            sliderContainer.className = 'swiper google-reviews-swiper';

            const swiperWrapper = document.createElement('div');
            swiperWrapper.className = 'swiper-wrapper';

            place.reviews.forEach(review => {
                const slide = document.createElement('div');
                slide.className = 'swiper-slide';

                const reviewItem = document.createElement('div');
                reviewItem.className = 'testimonial-item google-review-item';

                // Create review content
                reviewItem.innerHTML = `
                    <div class="testimonial-header">
                        <div class="testimonial-rating">
                            ${getStarRating(review.rating)}
                        </div>
                        <div class="testimonial-content">
                            <p>${review.text}</p>
                        </div>
                    </div>
                    <div class="testimonial-body">
                        <div class="author-image">
                            <figure class="image-anime">
                                <img src="${review.profile_photo_url || '{{ asset("assets/images/default-avatar.jpg") }}'}" alt="${review.author_name}">
                            </figure>
                        </div>
                        <div class="author-content">
                            <h3>${review.author_name}</h3>
                            <p>${new Date(review.time * 1000).toLocaleDateString()}</p>
                        </div>
                    </div>
                `;

                slide.appendChild(reviewItem);
                swiperWrapper.appendChild(slide);
            });

            sliderContainer.appendChild(swiperWrapper);

            // Add navigation
            const prevBtn = document.createElement('div');
            prevBtn.className = 'swiper-button-prev';

            const nextBtn = document.createElement('div');
            nextBtn.className = 'swiper-button-next';

            sliderContainer.appendChild(prevBtn);
            sliderContainer.appendChild(nextBtn);

            reviewsContainer.appendChild(sliderContainer);

            // Initialize the Google reviews slider
            new Swiper('.google-reviews-swiper', {
                slidesPerView: 1,
                spaceBetween: 30,
                loop: true,
                autoplay: {
                    delay: 4000,
                    disableOnInteraction: false,
                },
                navigation: {
                    nextEl: '.google-reviews-swiper .swiper-button-next',
                    prevEl: '.google-reviews-swiper .swiper-button-prev',
                },
                breakpoints: {
                    768: {
                        slidesPerView: 2,
                    },
                    992: {
                        slidesPerView: 3,
                    }
                }
            });
        }
    }

    function getStarRating(rating) {
        let stars = '';
        for (let i = 0; i < 5; i++) {
            if (i < rating) {
                stars += '<i class="fa-solid fa-star"></i>';
            } else {
                stars += '<i class="fa-regular fa-star"></i>';
            }
        }
        return stars;
    }
}
</script>
@endpush



