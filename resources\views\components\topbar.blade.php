    <!-- Topbar Section Start -->
    <div class="topbar" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
        <div class="container">
            <div class="row align-items-center">
                <!-- Contact Info Column -->
                <div class="col-lg-9 col-md-8">
                    <div class="topbar-contact-info">
                        <ul>
                            <li>
                                <a href="tel:+97317336601">
                                    <img src="{{ asset('assets/images/icon-phone.svg') }}" alt="phone icon">
                                    <span>+(973) 17336601</span>
                                </a>
                            </li>
                            <li>
                                <a href="mailto:<EMAIL>">
                                    <img src="{{ asset('assets/images/icon-mail.svg') }}" alt="email icon">
                                    <span><EMAIL></span>
                                </a>
                            </li>
                            <li class="language-switcher-mobile" style="margin-left: auto; margin-right: auto;">
                                @php
                                    $currentPath = str_replace(app()->getLocale(), '', request()->path());
                                    $currentPath = ltrim($currentPath, '/');
                                    $otherLocale = app()->getLocale() == 'ar' ? 'en' : 'ar';
                                    $otherLocaleName = $otherLocale == 'ar' ? 'العربية' : 'English';
                                @endphp
                                <a href="{{ url("/$otherLocale/$currentPath") }}" class="lang-link">
                                    <span>{{ $otherLocaleName }}</span>
                                    <i class="fa-solid fa-globe"></i>
                                </a>
                            </li>
                            {{-- <li class="hide-mobile">
                                <a href="https://maps.app.goo.gl/5UqShr1wWxkvMbfm8" target="_blank">
                                    <img src="{{ asset('assets/images/icon-location.svg') }}" alt="location icon">
                                    <span>{{ __('messages.Address') }}</span>
                                </a>
                            </li> --}}
                        </ul>
                    </div>
                    <!-- Topbar Contact Information End -->
                </div>

                <!-- Social Links & Language Column -->
                <div class="col-lg-3 col-md-4">
                    <div class="topbar-social-links">
                        <ul>
                            <li>
                                <a href="https://www.facebook.com/profile.php?id=100063836869090" target="_blank" aria-label="Facebook">
                                    <i class="fa-brands fa-facebook-f"></i>
                                </a>
                            </li>
                            <li>
                                <a href="https://www.instagram.com/alsharafcenter/" target="_blank" aria-label="Instagram">
                                    <i class="fa-brands fa-instagram"></i>
                                </a>
                            </li>
                            <li>
                                <a href="https://www.youtube.com/@alsharafcenter6864" target="_blank" aria-label="YouTube">
                                    <i class="fa-brands fa-youtube"></i>
                                </a>
                            </li>
                            <li class="language-switcher">
                                @php
                                    $currentPath = str_replace(app()->getLocale(), '', request()->path());
                                    $currentPath = ltrim($currentPath, '/');
                                    $otherLocale = app()->getLocale() == 'ar' ? 'en' : 'ar';
                                    $otherLocaleName = $otherLocale == 'ar' ? 'العربية' : 'English';
                                @endphp
                                <a href="{{ url("/$otherLocale/$currentPath") }}" class="lang-link">
                                    <span>{{ $otherLocaleName }}</span>
                                    <i class="fa-solid fa-globe"></i>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Topbar Section End -->

    <style>
        /* Base styles */
        .topbar {
            padding: 15px 0 65px;
            background-color: var(--primary-color);
            width: 100%;
        }

        /* Contact info styles */
        .topbar-contact-info {
            text-align: left;
        }

        .topbar-contact-info ul {
            display: flex;
            flex-wrap: wrap;
            list-style: none;
            padding: 0;
            margin: 0;
            gap: 30px;
        }

        .topbar-contact-info ul li a {
            color: var(--white-color);
            display: flex;
            align-items: center;
        }

        .topbar-contact-info ul li a img {
            max-width: 20px;
            margin-right: 10px;
        }

        /* Social links styles */
        .topbar-social-links {
            text-align: right;
        }

        .topbar-social-links ul {
            list-style: none;
            padding: 0;
            margin: 0;
            display: flex;
            justify-content: flex-end;
            gap: 20px;
        }

        .topbar-social-links ul li a {
            color: var(--white-color);
            transition: all 0.3s ease-in-out;
        }

        .topbar-social-links ul li a:hover {
            opacity: 0.8;
        }

        .topbar-social-links ul li a i {
            font-size: 20px;
        }

        /* Language switcher styles */
        .language-switcher .lang-link {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        /* RTL Specific Styles */
        [dir="rtl"] .topbar-contact-info {
            text-align: right;
        }

        [dir="rtl"] .topbar-social-links {
            text-align: left;
        }

        [dir="rtl"] .topbar-contact-info ul li a img {
            margin-right: 0;
            margin-left: 10px;
        }

        /* Language switcher mobile styles */
        .language-switcher-mobile {
            display: none; /* Hidden by default on desktop */
            margin-left: 60px !important;
            margin-right: 0 !important;
        }

        .language-switcher-mobile .lang-link {
            display: flex;
            align-items: center;
            gap: 8px;
            color: var(--white-color);
        }

        .language-switcher-mobile .lang-link i {
            font-size: 16px; /* Match icon size with other icons */
        }

        /* RTL Specific Styles */
        [dir="rtl"] .language-switcher-mobile {
            margin-right: 60px !important;
            margin-left: 0 !important;
        }

        @media (max-width: 767px) {
            .language-switcher-mobile {
                margin-left: 40px !important;
            }

            [dir="rtl"] .language-switcher-mobile {
                margin-right: 40px !important;
            }
        }

        /* Responsive styles */
        @media (max-width: 991px) {
            .topbar {
                padding: 15px 0 45px;
                margin-bottom: 30px; /* Added margin for tablets */
            }

            .topbar-social-links ul {
                justify-content: center;
            }

            .topbar-contact-info ul {
                justify-content: space-between;
                align-items: center;
                flex-wrap: wrap;
                gap: 20px;
                width: 100%;
            }

            .topbar-contact-info ul li {
                display: flex;
                align-items: center;
            }

            .topbar-contact-info ul li a {
                display: flex;
                align-items: center;
                height: 100%;
            }

            .language-switcher-mobile {
                display: block;
                order: 3; /* Ensures it comes after phone and email */
            }

            .language-switcher { /* Hide desktop language switcher */
                display: none !important;
            }
        }

        @media (max-width: 767px) {
            .topbar {
                padding: 15px 0 45px;
                margin-bottom: 20px; /* Added margin for mobile */
            }

            .hide-mobile {
                display: none;
            }

            .topbar-contact-info ul {
                gap: 15px;
            }

            .topbar-contact-info ul li a {
                font-size: 14px; /* Smaller font size for mobile */
            }

            .topbar-contact-info ul li a img {
                max-width: 16px;
                margin-right: 5px;
            }

            [dir="rtl"] .topbar-contact-info ul li a img {
                margin-right: 0;
                margin-left: 5px;
            }

            .language-switcher-mobile .lang-link {
                padding: 0;
                background-color: transparent;
            }
        }
    </style>










