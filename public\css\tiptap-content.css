/* TipTap Content Video Responsiveness */
.tiptap-content {
    width: 100%;
    max-width: 100%;
    overflow: hidden; /* Prevent content from spilling out */
}

/* Direct video elements */
.tiptap-content video {
    width: 100%;
    max-width: 100%;
    height: auto;
    display: block;
    margin: 1rem auto;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Video Container for Responsive Embeds */
.tiptap-video-container {
    width: 100%;
    max-width: 100%;
    margin: 1rem 0;
    position: relative;
    display: block;
}

/* For iframe videos */
.tiptap-video-container iframe {
    width: 100%;
    max-width: 100%;
    height: auto;
    aspect-ratio: 16/9;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Ensure proper sizing in editor */
.tiptap-editor .tiptap-video-container,
.tiptap-editor video {
    max-width: 100%;
    margin: 1rem 0;
}

