@extends('layouts.app')  <!-- Extend the base layout -->


@section('content')
    <!-- Page Header Start -->
	{{-- <div class="page-header practitioners-header bg-radius-section parallaxie">
		<div class="container">
			<div class="row align-items-center">
				<div class="col-lg-12">
					<div class="page-header-box text-center">
						<h1 class="{{ app()->getLocale() === 'ar' ? 'arabic-title' : 'text-anime-style-2' }}" data-cursor="-opaque">{{ __('messages.Our Medical Team') }}</h1>
						<nav class="wow fadeInUp">
							<ol class="breadcrumb justify-content-center">
								<li class="breadcrumb-item"><a href="./">{{ __('messages.Home') }}</a></li>
								<li class="breadcrumb-item active" aria-current="page">{{ __('messages.Practitioners') }}</li>
							</ol>
						</nav>
					</div>
				</div>
			</div>
		</div>
	</div> --}}
    <div class="page-header practitioners-page-header bg-radius-section parallaxie" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
		<div class="container">
			<div class="row align-items-center">
				<div class="col-lg-12">
					<!-- Page Header Box Start -->
					<div class="page-header-box">
						<h1 class="{{ app()->getLocale() === 'ar' ? 'arabic-title' : 'text-anime-style-2' }}" data-cursor="-opaque">{{ __('messages.Our Medical Team') }}</h1>
						<!-- Custom breadcrumb for RTL/LTR support -->
						@if(app()->getLocale() == 'ar')
							<!-- Arabic RTL breadcrumb -->
							<nav class="wow fadeInUp custom-rtl-breadcrumb">
								<div class="rtl-breadcrumb">
									<a href="{{ url(app()->getLocale()) }}" style="color: rgba(255, 255, 255, 0.7) !important; text-decoration: none !important; font-size: 16px;">{{ __('messages.Home') }}</a>
									<span class="separator" style="color: rgba(255, 255, 255, 0.7) !important; font-size: 16px;">/</span>
									<span style="color: #fff !important; font-size: 16px;">{{ __('messages.Our Medical Team') }}</span>
								</div>
							</nav>
						@else
							<!-- English LTR breadcrumb -->
							<nav class="wow fadeInUp">
								<ol class="breadcrumb">
									<li class="breadcrumb-item"><a href="{{ url(app()->getLocale()) }}">{{ __('messages.Home') }}</a></li>
									<li class="breadcrumb-item active" aria-current="page">{{ __('messages.Our Medical Team') }}</li>
								</ol>
							</nav>
						@endif
					</div>
					<!-- Page Header Box End -->
				</div>
			</div>
		</div>
	</div>
	<!-- Page Header End -->

	<!-- Practitioners Section Start -->
	<section class="practitioners-section" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
		<div class="container">
			<!-- Doctors Section -->
			<div class="practitioner-row row align-items-center mb-5 wow fadeInUp">
				<div class="col-lg-6 {{ app()->getLocale() == 'ar' ? 'ps-lg-5' : 'pe-lg-5' }}">
					<div class="practitioner-image">
						<img
							src="{{ asset('assets/images/doctors-team.jpg') }}"
							alt="Our Doctors"
							class="img-fluid rounded-4 shadow-lg"
							loading="lazy"
							width="600"
							height="400"
							srcset="{{ asset('assets/images/doctors-team.jpg') }} 1x,
									{{ asset('assets/images/<EMAIL>') }} 2x"
							decoding="async"
							fetchpriority="high"
						>
						<div class="image-overlay"></div>
					</div>
				</div>
				<div class="col-lg-6 {{ app()->getLocale() == 'ar' ? 'pe-lg-5' : 'ps-lg-5' }}">
					<div class="practitioner-content">
						<span class="subtitle {{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ __('messages.Expert Care') }}</span>
						<h2 class="title {{ app()->getLocale() === 'ar' ? 'arabic-title' : '' }}">{{ __('messages.Our Specialized Doctors') }}</h2>
						<p class="description {{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">
							{{ __('messages.doctors_description') }}
						</p>
						<a href="{{ route('doctors', ['locale' => app()->getLocale()]) }}" class="btn-custom">
							<span>{{ __('messages.View Our Doctors') }}</span>
							<i class="fas {{ app()->getLocale() === 'ar' ? 'fa-arrow-left' : 'fa-arrow-right' }}"></i>
						</a>
					</div>
				</div>
			</div>

			<!-- Physiotherapists Section -->
			<div class="practitioner-row row align-items-center {{ app()->getLocale() == 'ar' ? 'flex-row-reverse' : 'flex-lg-row-reverse' }} wow fadeInUp">
				<div class="col-lg-6 {{ app()->getLocale() == 'ar' ? 'pe-lg-5' : 'ps-lg-5' }}">
					<div class="practitioner-image">
						<img
							src="{{ asset('assets/images/physiotherapists-team.jpg') }}"
							alt="Team of professional physiotherapists at Al Sharaf Medical Center providing rehabilitation services"
							class="img-fluid rounded-4 shadow-lg"
							loading="lazy"
							width="600"
							height="400"
							srcset="{{ asset('assets/images/physiotherapists-team.jpg') }} 1x,
									{{ asset('assets/images/<EMAIL>') }} 2x"
							decoding="async"
							fetchpriority="high"
						>
						<div class="image-overlay"></div>
					</div>
				</div>
				<div class="col-lg-6 {{ app()->getLocale() == 'ar' ? 'ps-lg-5' : 'pe-lg-5' }}">
					<div class="practitioner-content">
						<span class="subtitle {{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ __('messages.Rehabilitation Experts') }}</span>
						<h2 class="title {{ app()->getLocale() === 'ar' ? 'arabic-title' : '' }}">{{ __('messages.Physiotherapists') }}</h2>
						<p class="description {{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">
							{{ __('messages.physiotherapists_description') }}
						</p>
						<a href="{{ route('physiotherapists', ['locale' => app()->getLocale()]) }}" class="btn-custom">
							<span>{{ __('messages.View Our Physiotherapists') }}</span>
							<i class="fas {{ app()->getLocale() === 'ar' ? 'fa-arrow-left' : 'fa-arrow-right' }}"></i>
						</a>
					</div>
				</div>
			</div>
		</div>
	</section>
	<!-- Practitioners Section End -->

	<style>
		/* Modern Typography */
		.practitioners-section {
			padding: 100px 0;
			background: linear-gradient(180deg, rgba(var(--primary-color-rgb), 0.03) 0%, rgba(var(--primary-color-rgb), 0) 100%);
		}

		.subtitle {
			font-family: 'DM Sans', sans-serif;
			font-size: 1.1rem;
			color: var(--primary-color);
			text-transform: uppercase;
			letter-spacing: 2px;
			font-weight: 600;
			display: block;
			margin-bottom: 15px;
		}

		.title {
			font-family: 'DM Sans', sans-serif;
			font-size: 2.5rem;
			color: var(--heading-color);
			font-weight: 700;
			margin-bottom: 20px;
			line-height: 1.2;
		}

		.description {
			font-family: 'Inter', sans-serif;
			font-size: 1.1rem;
			line-height: 1.8;
			color: var(--text-color);
			margin-bottom: 30px;
		}

		/* Image Styling */
		.practitioner-image {
			position: relative;
			overflow: hidden;
			border-radius: 20px;
			transition: all 0.3s ease;
		}

		.practitioner-image img {
			width: 100%;
			transition: transform 0.5s ease;
		}

		.practitioner-image:hover img {
			transform: scale(1.05);
		}

		.image-overlay {
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background: linear-gradient(180deg, rgba(var(--primary-color-rgb), 0) 0%, rgba(var(--primary-color-rgb), 0.1) 100%);
			pointer-events: none;
		}

		/* Button Styling */
		.btn-custom {
			display: inline-flex;
			align-items: center;
			padding: 15px 30px;
			background: var(--primary-color);
			color: var(--white-color);
			border-radius: 50px;
			font-family: 'DM Sans', sans-serif;
			font-weight: 600;
			text-decoration: none;
			transition: all 0.3s ease;
			box-shadow: 0 5px 15px rgba(var(--primary-color-rgb), 0.2);
		}

		.btn-custom span {
			margin-right: 10px;
		}

		.btn-custom:hover {
			transform: translateY(-3px);
			box-shadow: 0 8px 25px rgba(var(--primary-color-rgb), 0.3);
		}

		/* RTL Styles */
		[dir="rtl"] .subtitle,
		[dir="rtl"] .title,
		[dir="rtl"] .description {
			text-align: right;
		}

		[dir="rtl"] .btn-custom span {
			margin-right: 0;
			margin-left: 10px;
		}

		[dir="rtl"] .practitioner-content {
			text-align: right;
		}

		/* Arabic content styling */
		.arabic-content {
			font-family: var(--arabic-font) !important;
			letter-spacing: 0 !important;
			word-spacing: normal !important;
			text-align: right !important;
			direction: rtl !important;
		}

		/* Arabic title styling */
		.arabic-title {
			font-family: var(--arabic-font) !important;
			letter-spacing: 0 !important;
			word-spacing: normal !important;
			text-align: right !important;
			direction: rtl !important;
			display: block !important;
		}

		/* Section Spacing */
		.practitioner-row {
			padding: 30px 0;
		}

		.practitioner-content {
			padding: 30px;
		}

		/* Responsive Design */
		@media (max-width: 991px) {
			.practitioners-section {
				padding: 60px 0;
			}

			.title {
				font-size: 2rem;
			}

			.practitioner-row {
				padding: 15px 0;
			}

			.practitioner-content {
				padding: 30px 15px;
				text-align: center;
			}
		}

		@media (max-width: 768px) {
			.title {
				font-size: 1.8rem;
			}

			.description {
				font-size: 1rem;
			}

			.practitioner-image {
				margin-bottom: 30px;
			}
		}

		/* Animation Enhancement */
		.wow {
			visibility: hidden;
		}

		.fadeInUp {
			animation-name: fadeInUp;
			animation-duration: 1s;
		}

		@keyframes fadeInUp {
			from {
				opacity: 0;
				transform: translateY(20px);
			}
			to {
				opacity: 1;
				transform: translateY(0);
			}
		}
	</style>
@endsection







