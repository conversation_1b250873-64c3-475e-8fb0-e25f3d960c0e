<?php
    $featuredStaff = \App\Models\Staff::with('department')
        ->where('is_active', true)
        ->orderBy('display_order')
        ->take(4)
        ->get();
?>

<!-- Therapist Team Section Start -->
<div class="therapist-team bg-radius-section" dir="<?php echo e(app()->getLocale() == 'ar' ? 'rtl' : 'ltr'); ?>">
        <div class="container">
            <div class="row section-row align-items-center">
                <div class="col-lg-8">
                    <!-- Section Title Start -->
                    <div class="section-title">
                        <h3 class="wow fadeInUp"><?php echo e(__('messages.orthopedic_team')); ?></h3>
                        <h2 class="<?php echo e(app()->getLocale() === 'ar' ? 'arabic-title' : 'text-anime-style-3'); ?>" data-cursor="-opaque"><?php echo e(__('messages.dedicated_team')); ?></h2>
                    </div>
                    <!-- Section Title End -->
                </div>

                <div class="col-lg-4">
                    <!-- Section Btn Start -->
                    <div class="section-btn wow fadeInUp">
                        <a href="<?php echo e(route('doctors', app()->getLocale())); ?>" class="btn-default"><span><?php echo e(__('messages.view_all_team')); ?></span></a>
                    </div>
                    <!-- Section Btn End -->
                </div>
            </div>

            <div class="row">
                <?php $__currentLoopData = $featuredStaff; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $doctor): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="col-lg-3 col-md-6">
                    <!-- Team Member Item Start -->
                    <div class="team-member-item wow fadeInUp" <?php if($index > 0): ?> data-wow-delay="<?php echo e($index * 0.25); ?>s" <?php endif; ?>>
                        <!-- Team Image Start -->
                        <div class="team-image">
                            <figure class="image-anime">
                                <?php if($doctor->photo): ?>
                                    <img src="<?php echo e($doctor->photo); ?>"
                                         alt="<?php echo e(app()->getLocale() == 'ar' ? ($doctor->name_ar ?: $doctor->name_en) : $doctor->name_en); ?>"
                                         loading="lazy"
                                         onerror="this.onerror=null; this.src='https://ui-avatars.com/api/?background=random&name=<?php echo e(urlencode($doctor->name_en)); ?>';">
                                <?php else: ?>
                                    <img src="https://ui-avatars.com/api/?background=random&name=<?php echo e(urlencode($doctor->name_en)); ?>"
                                         alt="<?php echo e(app()->getLocale() == 'ar' ? ($doctor->name_ar ?: $doctor->name_en) : $doctor->name_en); ?>"
                                         loading="lazy">
                                <?php endif; ?>
                            </figure>

                            <!-- View Profile Button Start -->
                            <a href="<?php echo e(route('doctor.profile', ['locale' => app()->getLocale(), 'id' => $doctor->id])); ?>" class="view-profile-btn">
                                <span><?php echo e(__('messages.view_profile')); ?></span>
                                <i class="fas fa-arrow-right"></i>
                            </a>
                            <!-- View Profile Button End -->

                            <!-- Team Social Icon Start -->
                            <div class="team-social-icon">
                                <ul>
                                    <?php if($doctor->facebook_url): ?>
                                    <li><a href="<?php echo e($doctor->facebook_url); ?>" class="social-icon" target="_blank"><i class="fa-brands fa-facebook-f"></i></a></li>
                                    <?php else: ?>
                                        <li><a href="#" class="social-icon"><i class="fa-brands fa-facebook-f"></i></a></li>
                                    <?php endif; ?>
                                    <?php if($doctor->youtube_url): ?>
                                        <li><a href="<?php echo e($doctor->youtube_url); ?>" class="social-icon" target="_blank"><i class="fa-brands fa-youtube"></i></a></li>
                                    <?php else: ?>
                                        <li><a href="#" class="social-icon"><i class="fa-brands fa-youtube"></i></a></li>
                                    <?php endif; ?>
                                    <?php if($doctor->instagram_url): ?>
                                        <li><a href="<?php echo e($doctor->instagram_url); ?>" class="social-icon" target="_blank"><i class="fa-brands fa-instagram"></i></a></li>
                                    <?php else: ?>
                                        <li><a href="#" class="social-icon"><i class="fa-brands fa-instagram"></i></a></li>
                                    <?php endif; ?>
                                    <?php if($doctor->twitter_url): ?>
                                        <li><a href="<?php echo e($doctor->twitter_url); ?>" class="social-icon" target="_blank"><i class="fa-brands fa-x-twitter"></i></a></li>
                                    <?php else: ?>
                                        <li><a href="#" class="social-icon"><i class="fa-brands fa-x-twitter"></i></a></li>
                                    <?php endif; ?>
                                </ul>
                            </div>
                            <!-- Team Social Icon End -->
                        </div>
                        <!-- Team Image End -->

                        <!-- Team Content Start -->
                        <div class="team-content">
                            <h3><?php echo e(app()->getLocale() == 'ar' ? ($doctor->name_ar ?: $doctor->name_en) : $doctor->name_en); ?></h3>
                            <p><?php echo e(app()->getLocale() == 'ar' ?
                                ($doctor->title_ar ?: ($doctor->department->name_ar ?? $doctor->title_en ?? $doctor->department->name_en ?? __('messages.specialist'))) :
                                ($doctor->title_en ?? $doctor->department->name_en ?? __('messages.specialist'))); ?></p>
                        </div>
                        <!-- Team Content End -->
                    </div>
                    <!-- Team Member Item End -->
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
</div>
<!-- Therapist Team Section End -->

<?php $__env->startPush('styles'); ?>
<style>
    /* RTL styles for doctors section */
    [dir="rtl"] .team-content h3,
    [dir="rtl"] .team-content p,
    [dir="rtl"] .section-title h3,
    [dir="rtl"] .section-title h2 {
        text-align: right;
        font-family: var(--arabic-font);
    }

    [dir="rtl"] .section-btn {
        text-align: left;
    }

    /* Fix for Arabic title rendering */
    .arabic-title {
        font-family: var(--arabic-font) !important;
        letter-spacing: 0 !important;
        word-spacing: normal !important;
        text-align: right !important;
        animation: none !important;
        display: inline-block !important;
        white-space: nowrap !important;
        direction: rtl !important;
    }

    /* Adjust view profile button for RTL */
    [dir="rtl"] .view-profile-btn {
        flex-direction: row-reverse;
    }

    [dir="rtl"] .view-profile-btn i {
        transform: rotate(180deg);
        margin-right: 0;
        margin-left: 5px;
    }
</style>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\xampp\htdocs\Carefirst\alsharaf_orthopedics\resources\views/components/doctors.blade.php ENDPATH**/ ?>