document.addEventListener('DOMContentLoaded', function() {
    if (typeof tinymce !== 'undefined') {
        tinymce.PluginManager.add('videoTemplate', function(editor) {
            // Add custom button to insert video
            editor.ui.registry.addButton('insertvideo', {
                text: 'Insert Video',
                onAction: function() {
                    editor.windowManager.open({
                        title: 'Insert Video',
                        body: {
                            type: 'panel',
                            items: [{
                                type: 'urlinput',
                                name: 'url',
                                label: 'Video URL'
                            }]
                        },
                        onSubmit: function(api) {
                            const data = api.getData();
                            const videoHtml = `<video controls width="560" height="315">
                                <source src="${data.url}" type="video/mp4">
                                Your browser does not support the video tag.
                            </video>`;
                            editor.insertContent(videoHtml);
                            api.close();
                        },
                        buttons: [
                            {
                                type: 'submit',
                                text: 'Insert'
                            },
                            {
                                type: 'cancel',
                                text: 'Close'
                            }
                        ]
                    });
                }
            });

            // Handle video tag preservation
            editor.on('BeforeSetContent', function(e) {
                if (e.content.includes('<video')) {
                    e.content = e.content.replace(/&lt;video/g, '<video');
                    e.content = e.content.replace(/&lt;\/video&gt;/g, '</video>');
                    e.content = e.content.replace(/&lt;source/g, '<source');
                    e.content = e.content.replace(/&lt;\/source&gt;/g, '</source>');
                }
            });

            editor.on('GetContent', function(e) {
                if (e.content.includes('<video')) {
                    e.content = e.content.replace(/<p><video/g, '<video');
                    e.content = e.content.replace(/<\/video><\/p>/g, '</video>');
                    e.content = e.content.replace(/<p><source/g, '<source');
                    e.content = e.content.replace(/<\/source><\/p>/g, '</source>');
                }
            });

            return {
                getMetadata: function() {
                    return {
                        name: 'Video Template Plugin',
                        url: 'https://example.com/video-plugin'
                    };
                }
            };
        });
    }
});
