<?php

namespace App\Models;

use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\FileUpload;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Storage;

class Gallery extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'title_en',
        'title_ar',
        'description_en',
        'description_ar',
        'type',
        'media_url',
        'media_file',
        'image',
        'thumbnail',
        'category',
        'display_order',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'display_order' => 'integer',
    ];

    protected function setImageAttribute($value)
    {
        if ($value === null) {
            return;  // Don't update if null
        }
        if (is_array($value)) {
            $value = $value[0] ?? null;
        }
        $this->attributes['image'] = $value ? basename($value) : null;
    }

    protected function setMediaFileAttribute($value)
    {
        if ($value === null) {
            return;  // Don't update if null
        }
        if (is_array($value)) {
            $value = $value[0] ?? null;
        }
        $this->attributes['media_file'] = $value ? basename($value) : null;
    }

    protected function setThumbnailAttribute($value)
    {
        if ($value === null) {
            return;  // Don't update if null
        }
        if (is_array($value)) {
            $value = $value[0] ?? null;
        }
        $this->attributes['thumbnail'] = $value ? basename($value) : null;
    }

    protected function setMediaUrlAttribute($value)
    {
        if ($value) {
            $value = ltrim($value, 'https://');
        }
        $this->attributes['media_url'] = $value;
    }

    public static function getFormSchema(): array
    {
        return [
            Tabs::make('Gallery')
                ->tabs([
                    Tabs\Tab::make('Basic Information')
                        ->icon('heroicon-o-information-circle')
                        ->schema([
                            Grid::make()
                                ->schema([
                                    TextInput::make('title_en')
                                        ->label('Title (English)')
                                        ->required()
                                        ->maxLength(255)
                                        ->columnSpan(1),

                                    TextInput::make('title_ar')
                                        ->label('Title (Arabic)')
                                        ->required()
                                        ->maxLength(255)
                                        ->extraAttributes([
                                            'dir' => 'rtl',
                                            'style' => 'text-align: right'
                                        ])
                                        ->columnSpan(1),

                                    RichEditor::make('description_en')
                                        ->label('Description (English)')
                                        ->toolbarButtons([
                                            'bold',
                                            'italic',
                                            'link',
                                            'orderedList',
                                            'unorderedList',
                                        ])
                                        ->columnSpan(1),

                                    RichEditor::make('description_ar')
                                        ->label('Description (Arabic)')
                                        ->toolbarButtons([
                                            'bold',
                                            'italic',
                                            'link',
                                            'orderedList',
                                            'unorderedList',
                                        ])
                                        ->extraAttributes([
                                            'dir' => 'rtl',
                                            'style' => 'text-align: right'
                                        ])
                                        ->columnSpan(1),
                                ])
                                ->columns(2)
                                ->columnSpan('full'),
                        ])
                        ->columnSpan('full'),

                    Tabs\Tab::make('Media')
                        ->icon('heroicon-o-photo')
                        ->schema([
                            Grid::make()
                                ->schema([
                                    Select::make('type')
                                        ->label('Media Type')
                                        ->options([
                                            'image' => 'Image',
                                            'video' => 'Video',
                                        ])
                                        ->required()
                                        ->reactive()
                                        ->default('image')
                                        ->columnSpan(2),

                                    FileUpload::make('image')
                                        ->label('Upload Image')
                                        ->image()
                                        ->imageEditor()
                                        ->visible(fn ($get) => $get('type') === 'image')
                                        ->directory('gallery/images')
                                        ->disk('public')
                                        ->preserveFilenames()
                                        ->maxSize(5120)
                                        ->columnSpan(2)
                                        ->dehydrated(fn ($state) => filled($state))
                                        ->required(fn (string $context): bool => $context === 'create')
                                        ->downloadable()
                                        ->openable()
                                        ->previewable(),

                                    FileUpload::make('media_file')
                                        ->label('Upload Video File')
                                        ->visible(fn ($get) => $get('type') === 'video')
                                        ->directory('gallery/videos')
                                        ->disk('public')
                                        ->preserveFilenames()
                                        ->maxSize(102400)
                                        ->acceptedFileTypes(['video/mp4'])
                                        ->columnSpan(1)
                                        ->dehydrated(fn ($state) => filled($state))
                                        ->required(fn (string $context): bool => $context === 'create')
                                        ->downloadable()
                                        ->openable()
                                        ->previewable(),

                                    FileUpload::make('thumbnail')
                                        ->label('Video Thumbnail')
                                        ->visible(fn ($get) => $get('type') === 'video')
                                        ->image()
                                        ->directory('gallery/thumbnails')
                                        ->disk('public')
                                        ->preserveFilenames()
                                        ->columnSpan(1)
                                        ->dehydrated(fn ($state) => filled($state))
                                        ->required(fn (string $context): bool => $context === 'create')
                                        ->downloadable()
                                        ->openable()
                                        ->previewable(),

                                    TextInput::make('media_url')
                                        ->label('Video URL')
                                        ->visible(fn ($get) => $get('type') === 'video')
                                        ->url()
                                        ->prefix('https://')
                                        ->placeholder('youtube.com/watch?v=...')
                                        ->columnSpan(2),
                                ])
                                ->columns(2)
                                ->columnSpan('full'),
                        ])
                        ->columnSpan('full'),

                    Tabs\Tab::make('Settings')
                        ->icon('heroicon-o-cog')
                        ->schema([
                            Grid::make()
                                ->schema([
                                    TextInput::make('category')
                                        ->label('Category')
                                        ->maxLength(255)
                                        ->columnSpan(1),

                                    TextInput::make('display_order')
                                        ->label('Display Order')
                                        ->numeric()
                                        ->default(0)
                                        ->columnSpan(1),

                                    Toggle::make('is_active')
                                        ->label('Active')
                                        ->default(true)
                                        ->columnSpan(1),
                                ])
                                ->columns(2)
                                ->columnSpan('full'),
                        ])
                        ->columnSpan('full'),
                ])
                ->columnSpan('full')
                ->persistTabInQueryString()
                ->contained(false),
        ];
    }

    // Get the full URL for media files
    public function getMediaUrl()
    {
        if ($this->type === 'video' && $this->media_url) {
            return $this->media_url;
        }

        if ($this->type === 'video' && $this->media_file) {
            return Storage::url('gallery/videos/' . $this->media_file);
        }

        if ($this->type === 'image' && $this->image) {
            return Storage::url('gallery/images/' . $this->image);
        }

        return null;
    }

    // Get thumbnail URL
    public function getThumbnailUrl()
    {
        // If custom thumbnail is set, return it
        if ($this->thumbnail) {
            return Storage::url('gallery/thumbnails/' . $this->thumbnail);
        }

        // For YouTube videos
        if ($this->media_url && str_contains($this->media_url, 'youtube.com')) {
            preg_match('/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/', $this->media_url, $matches);
            if (isset($matches[1])) {
                return 'https://img.youtube.com/vi/' . $matches[1] . '/maxresdefault.jpg';
            }
        }

        // For Vimeo videos
        if ($this->media_url && str_contains($this->media_url, 'vimeo.com')) {
            preg_match('/vimeo\.com\/([0-9]+)/', $this->media_url, $matches);
            if (isset($matches[1])) {
                $vimeo_id = $matches[1];
                $vimeo_data = @file_get_contents("https://vimeo.com/api/v2/video/$vimeo_id.json");
                if ($vimeo_data) {
                    $vimeo_data = json_decode($vimeo_data);
                    if (isset($vimeo_data[0]->thumbnail_large)) {
                        return $vimeo_data[0]->thumbnail_large;
                    }
                }
            }
        }

        // If no thumbnail is found, return UI Avatars URL
        return 'https://ui-avatars.com/api/?background=random&name=' . urlencode($this->title_en);
    }

    public function getVideoUrl()
    {
        if ($this->type !== 'video') {
            return null;
        }

        if ($this->media_url) {
            return $this->media_url;
        }

        if ($this->media_file) {
            return Storage::url('gallery/videos/' . $this->media_file);
        }

        return null;
    }

    public function getVideoType()
    {
        if ($this->media_url) {
            if (str_contains($this->media_url, 'youtube.com')) {
                return 'youtube';
            }
            if (str_contains($this->media_url, 'vimeo.com')) {
                return 'vimeo';
            }
        }
        return 'html5video';
    }

    public function getDuration()
    {
        // This is a placeholder. You might want to implement actual video duration detection
        return '--:--';
    }
}



















