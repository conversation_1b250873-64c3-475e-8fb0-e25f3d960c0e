<div>
    @if($getRecord() && $getRecord()->videos)
        <div class="space-y-2">
            @foreach($getRecord()->videos as $video)
                @php
                    $path = class_basename(get_class($getRecord())) === 'Service'
                        ? 'services/videos/'
                        : 'common-causes/videos/';
                    $cleanPath = str_replace($path, '', $video);
                    $videoUrl = url('/storage/' . $path . $cleanPath);
                @endphp
                <div class="flex items-center gap-2">
                    <div class="flex-1">
                        <input
                            type="text"
                            value="{{ $videoUrl }}"
                            readonly
                            class="block w-full disabled:bg-gray-50 disabled:text-gray-500 dark:disabled:bg-gray-700 dark:disabled:text-gray-400 border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 focus:border-primary-500 focus:ring-primary-500 rounded-lg shadow-sm dark:text-white sm:text-sm"
                        >
                    </div>
                    <button
                        type="button"
                        class="fi-btn fi-btn-size-sm relative grid-flow-col items-center justify-center font-semibold outline-none transition duration-75 focus:ring-2 rounded-lg fi-btn-color-primary bg-primary-600 text-white hover:bg-primary-500 focus:ring-primary-500/50 dark:bg-primary-500 dark:hover:bg-primary-400 dark:focus:ring-primary-400/50 fi-ac-btn-action px-3 py-2"
                        x-data
                        x-on:click="
                            navigator.clipboard.writeText('{{ $videoUrl }}');
                            $el.innerHTML = 'Copied!';
                            setTimeout(() => {
                                $el.innerHTML = 'Copy';
                            }, 2000);
                        "
                    >
                        Copy
                    </button>
                </div>
            @endforeach
        </div>
    @endif
</div>


