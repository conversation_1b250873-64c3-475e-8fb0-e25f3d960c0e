<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\RichEditor;

class HeroSection extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'page_identifier',
        'page_name',
        'title_en',
        'title_ar',
        'description_en',
        'description_ar',
        'background_type',
        'background_images',
        'background_videos',
        'youtube_url',
        'slider_interval',
        'slider_autoplay',
        'button1_text_en',
        'button1_text_ar',
        'button1_url',
        'button2_text_en',
        'button2_text_ar',
        'button2_url',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'slider_autoplay' => 'boolean',
        'background_images' => 'array',
        'background_videos' => 'array',
    ];

    public static function getFormSchema() : array
    {
        return [Tabs::make('Hero Section')
            ->tabs([
                Tabs\Tab::make('Basic Information')
                    ->schema([
                        Grid::make()
                            ->schema([
                                TextInput::make('page_name')
                                    ->label('Page Name')
                                    ->required()
                                    ->maxLength(255)
                                    ->columnSpan(1),

                                TextInput::make('page_identifier')
                                    ->label('Page Identifier')
                                    ->required()
                                    ->maxLength(255)
                                    ->helperText('Unique identifier for the page (e.g., home, about, gallery)')
                                    ->columnSpan(1),
                            ])
                            ->columns(2),

                        Grid::make()
                            ->schema([
                                TextInput::make('title_en')
                                    ->label('Title (English)')
                                    ->required()
                                    ->maxLength(255)
                                    ->columnSpan(1),

                                TextInput::make('title_ar')
                                    ->label('Title (Arabic)')
                                    ->required()
                                    ->maxLength(255)
                                    ->extraAttributes([
                                        'dir' => 'rtl',
                                        'style' => 'text-align: right'
                                    ])
                                    ->columnSpan(1),
                            ])
                            ->columns(2),

                        Grid::make()
                            ->schema([
                                RichEditor::make('description_en')
                                    ->label('Description (English)')
                                    ->toolbarButtons([
                                        'bold',
                                        'italic',
                                        'underline',
                                        'strike',
                                        'link',
                                        'orderedList',
                                        'unorderedList',
                                        'redo',
                                        'undo'
                                    ])
                                    ->columnSpan(1),

                                RichEditor::make('description_ar')
                                    ->label('Description (Arabic)')
                                    ->toolbarButtons([
                                        'bold',
                                        'italic',
                                        'underline',
                                        'strike',
                                        'link',
                                        'orderedList',
                                        'unorderedList',
                                        'redo',
                                        'undo'
                                    ])
                                    ->extraAttributes([
                                        'dir' => 'rtl',
                                        'style' => 'text-align: right'
                                    ])
                                    ->columnSpan(1),
                            ])
                            ->columns(2),
                    ]),

                Tabs\Tab::make('Background')
                    ->schema([
                        Select::make('background_type')
                            ->label('Background Type')
                            ->options([
                                'slider' => 'Image/Video Slider',
                                'youtube' => 'YouTube Video',
                            ])
                            ->required()
                            ->reactive()
                            ->default('slider')
                            ->name('background_type'), // Add name attribute

                        Grid::make()
                            ->schema([
                                FileUpload::make('background_images')
                                    ->label('Background Images')
                                    ->image()
                                    ->multiple()
                                    ->maxSize(5120)
                                    ->directory('hero-sections/images')
                                    ->enableReordering()
                                    ->visible(fn ($get) => $get('background_type') === 'slider')
                                    ->columnSpan(1)
                                    ->name('background_images') // Add name attribute
                                    ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/webp'])
                                    ->helperText('Upload images (JPG, PNG, WebP)'),

                                FileUpload::make('background_videos')
                                    ->label('Background Videos')
                                    ->multiple()
                                    ->acceptedFileTypes(['video/mp4', 'video/webm'])
                                    ->maxSize(51200)
                                    ->directory('hero-sections/videos')
                                    ->enableReordering()
                                    ->visible(fn ($get) => $get('background_type') === 'slider')
                                    ->columnSpan(1)
                                    ->name('background_videos') // Add name attribute
                                    ->helperText('Upload videos (MP4, WebM)'),
                            ])
                            ->columns(2),

                        TextInput::make('youtube_url')
                            ->label('YouTube Video URL')
                            ->url()
                            ->visible(fn ($get) => $get('background_type') === 'youtube')
                            ->name('youtube_url') // Add name attribute
                            ->rules(['url', 'nullable']),

                        Grid::make()
                            ->schema([
                                TextInput::make('slider_interval')
                                    ->label('Slider Interval (ms)')
                                    ->numeric()
                                    ->default(5000)
                                    ->visible(fn ($get) => $get('background_type') === 'slider')
                                    ->helperText('Time in milliseconds between slides (default: 5000ms = 5s)')
                                    ->columnSpan(1)
                                    ->name('slider_interval') // Add name attribute
                                    ->rules(['numeric', 'min:1000']),

                                Toggle::make('slider_autoplay')
                                    ->label('Autoplay Slider')
                                    ->default(true)
                                    ->visible(fn ($get) => $get('background_type') === 'slider')
                                    ->columnSpan(1)
                                    ->name('slider_autoplay'), // Add name attribute
                            ])
                            ->columns(2),
                    ])
                    ->reactive(),

                // Tabs\Tab::make('Buttons')
                //     ->schema([
                //         Grid::make()
                //             ->schema([
                //                 TextInput::make('button1_text_en')  // Remove data. prefix
                //                     ->label('Button 1 Text (English)')
                //                     ->maxLength(255),

                //                 TextInput::make('button1_text_ar')
                //                     ->label('Button 1 Text (Arabic)')
                //                     ->maxLength(255)
                //                     ->extraAttributes([
                //                         'dir' => 'rtl',
                //                         'style' => 'text-align: right'
                //                     ]),

                //                 TextInput::make('button1_url')
                //                     ->label('Button 1 URL')
                //                     ->type('url')
                //                     ->name('button1_url')  // Add explicit name
                //                     ->rules(['url', 'nullable']),
                //             ])
                //             ->columns(3),

                //         Grid::make()
                //             ->schema([
                //                 TextInput::make('button2_text_en')
                //                     ->label('Button 2 Text (English)')
                //                     ->maxLength(255),

                //                 TextInput::make('button2_text_ar')
                //                     ->label('Button 2 Text (Arabic)')
                //                     ->maxLength(255)
                //                     ->extraAttributes([
                //                         'dir' => 'rtl',
                //                         'style' => 'text-align: right'
                //                     ]),

                //                 TextInput::make('button2_url')
                //                     ->label('Button 2 URL')
                //                     ->type('url')
                //                     ->name('button2_url')  // Add explicit name
                //                     ->rules(['url', 'nullable']),
                //             ])
                //             ->columns(3),
                //     ]),

                Tabs\Tab::make('Settings')
                    ->schema([
                        Toggle::make('is_active')
                            ->label('Active')
                            ->default(true),
                    ]),
            ])
            ->columnSpanFull()
            ->persistTabInQueryString()
        ];
    }

    public static function validationRules(): array
    {
        return [
            'background_type' => ['required', 'in:slider,youtube'],
            'background_images' => ['nullable', 'array'],
            'background_images.*' => ['nullable', 'file', 'image', 'max:5120'],
            'background_videos' => ['nullable', 'array'],
            'background_videos.*' => ['nullable', 'file', 'mimetypes:video/mp4,video/webm', 'max:51200'],
            'youtube_url' => ['nullable', 'url'],
            'slider_interval' => ['nullable', 'numeric', 'min:1000'],
            'slider_autoplay' => ['nullable', 'boolean'],
            'button1_text_en' => ['nullable', 'string', 'max:255'],
            'button1_text_ar' => ['nullable', 'string', 'max:255'],
            'button1_url' => ['nullable', 'url'],
            'button2_text_en' => ['nullable', 'string', 'max:255'],
            'button2_text_ar' => ['nullable', 'string', 'max:255'],
            'button2_url' => ['nullable', 'url'],
        ];
    }
}










