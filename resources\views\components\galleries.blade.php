@php
    $galleries = \App\Models\Gallery::where('is_active', true)
        ->orderBy('display_order')
         ->limit(6)  // Limit to latest 6 items for better performance
        ->get();
@endphp

<!-- Galleries Section Start -->
<div class="our-gallery bg-radius-section">
    <div class="container">
        <!-- Section Title Start -->
        <div class="section-title mb-4">
            <div class="row align-items-center">
                <div class="col-lg-6 {{ app()->getLocale() === 'ar' ? 'order-lg-2' : '' }}">
                    <div class="{{ app()->getLocale() === 'ar' ? 'text-end' : '' }}">
                        <span class="subtitle wow fadeInUp">{{ __('messages.Our Gallery') }}</span>
                        <h2 class="{{ app()->getLocale() === 'ar' ? 'arabic-title' : 'text-anime-style-2' }}" data-cursor="-opaque">{{ __('messages.Latest Media') }}</h2>
                    </div>
                </div>
                <div class="col-lg-6 {{ app()->getLocale() === 'ar' ? 'order-lg-1' : '' }}">
                    <div class="section-right-btn {{ app()->getLocale() === 'ar' ? 'text-lg-start' : 'text-lg-end' }}">
                        <a href="{{ url(app()->getLocale() . '/Galleries') }}" class="btn-default">
                            <span>{{ __('messages.View All Gallery') }}</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <!-- Section Title End -->

        <!-- Gallery Slider Start -->
        <div class="row">
            <div class="col-12">
                <div class="gallery-slider">
                    <div class="swiper gallery-swiper">
                        <div class="swiper-wrapper">
                            @forelse($galleries as $gallery)
                                <div class="swiper-slide">
                                    <div class="page-gallery-card"> <!-- Changed to use same class as pages -->
                                        @if($gallery->type === 'image')
                                            <a href="{{ asset($gallery->getMediaUrl()) }}"
                                               data-fancybox="gallery-component"
                                               class="gallery-link image-anime"
                                               data-caption="{{ app()->getLocale() === 'en' ? $gallery->title_en : $gallery->title_ar }}"
                                               data-cursor-text="{{ __('messages.View Image') }}">
                                                <div class="page-gallery-img-wrapper">
                                                    <!-- Type Badge -->
                                                    <div class="gallery-type-badge">
                                                        <i class="fas fa-image me-2"></i>{{ __('messages.Image') }}
                                                    </div>

                                                    <img src="{{ asset($gallery->getMediaUrl()) }}"
                                                         alt="{{ app()->getLocale() === 'en' ? $gallery->title_en : $gallery->title_ar }}"
                                                         class="page-gallery-img"
                                                         loading="lazy"
                                                         onerror="this.onerror=null; this.src='https://ui-avatars.com/api/?background=random&name={{ urlencode($gallery->title_en) }}'">

                                                    <div class="page-gallery-overlay">
                                                        <i class="fas fa-search-plus page-gallery-icon"></i>
                                                    </div>
                                                </div>
                                            </a>
                                        @else
                                            <a href="{{ $gallery->getVideoUrl() }}"
                                               data-fancybox="gallery-component"
                                               class="gallery-link image-anime"
                                               data-type="{{ $gallery->getVideoType() }}"
                                               data-caption="{{ app()->getLocale() === 'en' ? $gallery->title_en : $gallery->title_ar }}"
                                               data-cursor-text="{{ __('messages.Play Video') }}">
                                                <div class="page-gallery-img-wrapper">
                                                    <!-- Type Badge -->
                                                    <div class="gallery-type-badge">
                                                        <i class="fas fa-video me-2"></i>{{ __('messages.Video') }}
                                                    </div>

                                                    <img src="{{ $gallery->getThumbnailUrl() ?
                                                              asset($gallery->getThumbnailUrl()) :
                                                              'https://ui-avatars.com/api/?background=random&name=' . urlencode($gallery->title_en) }}"
                                                         alt="{{ app()->getLocale() === 'en' ? $gallery->title_en : $gallery->title_ar }}"
                                                         class="page-gallery-img"
                                                         loading="lazy"
                                                         onerror="this.onerror=null; this.src='https://ui-avatars.com/api/?background=random&name={{ urlencode($gallery->title_en) }}'">

                                                    <!-- Video Duration -->
                                                    @if($gallery->duration)
                                                        <div class="video-duration">
                                                            <i class="fas fa-clock me-1"></i>{{ $gallery->duration }}
                                                        </div>
                                                    @endif

                                                    <div class="page-gallery-overlay">
                                                        <i class="fas fa-play page-gallery-icon"></i>
                                                    </div>
                                                </div>
                                            </a>
                                        @endif
                                        <div class="page-gallery-caption">
                                            <h5 class="page-gallery-title">
                                                {{ app()->getLocale() === 'en' ? $gallery->title_en : $gallery->title_ar }}
                                            </h5>
                                        </div>
                                    </div>
                                </div>
                            @empty
                                <div class="col-12 text-center">
                                    <p>{{ __('messages.No Gallery Items') }}</p>
                                </div>
                            @endforelse
                        </div>
                        <div class="swiper-pagination"></div>
                        <div class="swiper-button-next"></div>
                        <div class="swiper-button-prev"></div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Gallery Slider End -->
    </div>
</div>
<!-- Galleries Section End -->

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Swiper
    const gallerySwiper = new Swiper('.gallery-swiper', {
        slidesPerView: 1,
        spaceBetween: 30,
        loop: true,
        speed: 1000,
        autoplay: {
            delay: 5000,
            disableOnInteraction: false,
            pauseOnMouseEnter: true
        },
        pagination: {
            el: '.swiper-pagination',
            clickable: true,
            dynamicBullets: true
        },
        navigation: {
            nextEl: '.swiper-button-next',
            prevEl: '.swiper-button-prev'
        },
        breakpoints: {
            576: {
                slidesPerView: 2,
                spaceBetween: 20
            },
            992: {
                slidesPerView: 3,
                spaceBetween: 30
            }
        }
    });

    // Initialize Fancybox
    Fancybox.bind("[data-fancybox='gallery-component']", {
        // Image options
        Image: {
            zoom: false,  // Disable initial zoom
            wheel: true,
            click: "toggleZoom",
            doubleClick: "toggleZoom",
            wheelLimit: 10,
            zoomFriction: 0.88,
            infinite: true,
        },

        Images: {
            Panzoom: {
                maxScale: 5,
                minScale: 1,  // Changed from 0.5 to 1
                initialScale: 1,  // Ensure initial scale is 1
            },
        },

        // Video specific options
        Video: {
            ratio: 16/9,
            autoplay: false,
            click: "play",
            controls: true,
            volume: 1,
        },

        Youtube: {
            ratio: 16/9,
            autoplay: false,
            controls: 1,
            showinfo: 0,
            rel: 0,
            modestbranding: 1,
        },

        Vimeo: {
            ratio: 16/9,
            autoplay: false,
            controls: true,
            transparent: false,
            dnt: true,
        },

        // Slideshow options
        Slideshow: {
            autostart: false,
            interval: 4000,
            repeat: true,
            pauseOnHover: false,
        },

        // Thumbnails configuration
        Thumbs: {
            type: "classic",
            showOnStart: true,
            key: "t",
            minCount: 1,
            autoStart: true,
        },

        // Toolbar configuration
        Toolbar: {
            display: {
                left: ["infobar", "prev"],
                middle: [
                    "slideshow",
                    "zoomIn",
                    "zoomOut",
                    "toggle1to1",
                    "toggleZoom",
                    "rotateCCW",
                    "rotateCW",
                    "flipX",
                    "flipY",
                    "thumbs",
                    "fullscreen",
                ],
                right: ["next", "close"],
            },
            items: {
                thumbs: {
                    tpl: '<button class="f-button" title="Toggle thumbnails" data-fancybox-toggle-thumbs><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path fill="none" d="M0 0h24v24H0z"/><path d="M4 4h4v4H4V4zm6 0h4v4h-4V4zm6 0h4v4h-4V4zM4 10h4v4H4v-4zm6 0h4v4h-4v-4zm6 0h4v4h-4v-4zM4 16h4v4H4v-4zm6 0h4v4h-4v-4zm6 0h4v4h-4v-4z" fill="currentColor"/></svg></button>',
                },
            },
        },

        // Gesture options
        gesture: {
            enabled: true,
            friction: 0.88,
            minScale: 1,
            maxScale: 5,
        },

        // Keyboard shortcuts
        keyboard: {
            Escape: "close",
            Delete: "close",
            Backspace: "close",
            PageUp: "next",
            PageDown: "prev",
            ArrowUp: "prev",
            ArrowDown: "next",
            ArrowRight: "next",
            ArrowLeft: "prev",
            "+"    : "zoomIn",
            "-"    : "zoomOut",
            "0"    : "toggleZoom",
            "1"    : "toggle1to1",
            "f"    : "fullscreen",
            "t"    : "Thumbs.toggle",
            "s"    : "Slideshow.toggle",
            "r"    : "rotateCW",
        },

        // Event handlers
        on: {
            "init": (fancybox) => {
                if (fancybox.Panzoom) {
                    fancybox.Panzoom.reset();
                }

                const clickedElement = fancybox.options.$trigger;
                if (clickedElement) {
                    const gallery = document.querySelectorAll('[data-fancybox="gallery-component"]');
                    const index = Array.from(gallery).indexOf(clickedElement);
                    if (index >= 0) {
                        fancybox.jumpTo(index);
                    }
                }
            },
            "reveal": (fancybox, slide) => {
                if (slide.$content) {
                    slide.$content.style.transform = '';
                    if (slide.Panzoom) {
                        slide.Panzoom.reset();
                    }
                }
            },
            "done": (fancybox, slide) => {
                if (slide.Panzoom) {
                    slide.Panzoom.reset();
                }
            },
            "closing": (fancybox) => {
                if (fancybox.Slideshow) {
                    fancybox.Slideshow.stop();
                }
            }
        },
    });

    // Add click handler for thumbnail toggle
    $(document).on('click', '[data-fancybox-toggle-thumbs]', function() {
        const fancybox = Fancybox.getInstance();
        if (fancybox && fancybox.Thumbs) {
            fancybox.Thumbs.toggle();
        }
    });
});
</script>
@endpush

@push('styles')
<style>
    /* RTL styles for galleries section */
    [dir="rtl"] .section-title h2,
    [dir="rtl"] .section-title .subtitle {
        text-align: right;
        font-family: var(--arabic-font);
    }

    /* Fix for Arabic title rendering - using the same approach as in doctors section */
    .arabic-title {
        font-family: var(--arabic-font) !important;
        letter-spacing: 0 !important;
        word-spacing: normal !important;
        text-align: right !important;
        animation: none !important;
        display: inline-block !important;
        white-space: nowrap !important;
        direction: rtl !important;
    }

    /* RTL specific layout adjustments */
    [dir="rtl"] .section-right-btn.text-lg-start {
        text-align: left !important;
    }

    [dir="rtl"] .page-gallery-caption h5 {
        text-align: right;
        font-family: var(--arabic-font);
    }

    [dir="rtl"] .gallery-type-badge {
        font-family: var(--arabic-font);
    }

    [dir="rtl"] .video-duration {
        font-family: var(--arabic-font);
    }

    /* Adjust navigation buttons for RTL */
    [dir="rtl"] .swiper-button-next,
    [dir="rtl"] .swiper-button-prev {
        transform: scaleX(-1);
    }
</style>
@endpush







