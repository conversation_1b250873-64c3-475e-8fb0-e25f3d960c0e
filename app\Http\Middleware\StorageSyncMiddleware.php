<?php

namespace App\Http\Middleware;

use Closure;
use App\Services\StorageSyncService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class StorageSyncMiddleware
{
    protected $storageSync;

    public function __construct(StorageSyncService $storageSync)
    {
        $this->storageSync = $storageSync;
    }

    public function handle($request, Closure $next)
    {
        // Store the current files before the request
        $beforeFiles = Storage::disk('public')->allFiles();

        $response = $next($request);

        // Check for file uploads in various ways
        if ($this->shouldSyncFiles($request)) {
            try {
                // Get files after the request
                $afterFiles = Storage::disk('public')->allFiles();

                // Find new files
                $newFiles = array_diff($afterFiles, $beforeFiles);

                foreach ($newFiles as $file) {
                    $sourcePath = storage_path('app/public/' . $file);
                    $success = $this->storageSync->syncFile($sourcePath);

                    Log::info('File sync attempt', [
                        'file' => $file,
                        'success' => $success,
                        'source_exists' => file_exists($sourcePath)
                    ]);
                }
            } catch (\Exception $e) {
                Log::error('Storage sync failed in middleware', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
            }
        }

        return $response;
    }

    protected function shouldSyncFiles($request)
    {
        // Check various conditions that might indicate file uploads
        return $request->hasFile('file') ||
               $request->hasFile('files') ||
               $request->hasFile('image') ||
               $request->hasFile('images') ||
               $request->hasFile('video') ||
               $request->hasFile('videos') ||
               $request->hasFile('media') ||
               str_contains($request->path(), 'upload') ||
               $request->isMethod('POST') ||
               $request->isMethod('PUT');
    }
}
