<?php

namespace App\Providers;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Vite;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Model::unguard();

        // // Get the root directory (one level up from Laravel folder)
        // $rootPath = dirname(base_path());

        // // Debug information with timestamps
        // $manifestPath = $rootPath . '/build/.vite/manifest.json';
        // $contents = file_exists($manifestPath) ? file_get_contents($manifestPath) : 'File not found';
        // $timestamp = date('Y-m-d H:i:s');

        // file_put_contents(
        //     storage_path('logs/vite-debug.log'),
        //     "[{$timestamp}]\n" .
        //     "Manifest path: " . $manifestPath . "\n" .
        //     "Exists: " . (file_exists($manifestPath) ? 'Yes' : 'No') . "\n" .
        //     "Public path: " . $rootPath . "\n" .
        //     "Manifest contents: " . $contents . "\n" .
        //     "Current working directory: " . getcwd() . "\n" .
        //     "----------------------------------------\n",
        //     FILE_APPEND
        // );

        // // Configure Vite to look in the correct location
        // Vite::useHotFile($rootPath . '/hot')
        //     ->useBuildDirectory('../build')  // Go up one level from Laravel folder
        //     ->useManifestFilename('.vite/manifest.json');
    }
}





