<?php

namespace App\Filament\Resources\BodyPartResource\Pages;

use App\Filament\Resources\BodyPartResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Support\Concerns\EvaluatesClosures;

class EditBodyPart extends EditRecord
{
    use EvaluatesClosures;

    protected static string $resource = BodyPartResource::class;

    public function getTitle(): string
    {
        return "Edit {$this->record->name_en} Part";
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}


