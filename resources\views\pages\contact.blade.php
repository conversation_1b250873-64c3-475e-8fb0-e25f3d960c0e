@extends('layouts.app')  <!-- Extend the base layout -->


@section('content')
    <!-- Page Header Start -->
	{{-- <div class="page-header contact-page-header bg-radius-section parallaxie" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
		<div class="container">
			<div class="row align-items-center">
				<div class="col-lg-12">
					<!-- Page Header Box Start -->
					<div class="page-header-box">
						<h1 class="{{ app()->getLocale() === 'ar' ? 'arabic-title' : 'text-anime-style-2' }}" data-cursor="-opaque">{{ __('messages.Contact Us') }}</h1>
						<nav class="wow fadeInUp">
							<ol class="breadcrumb">
								<li class="breadcrumb-item"><a href="./">{{ __('messages.Home') }}</a></li>
								<li class="breadcrumb-item active" aria-current="page">{{ __('messages.contact_us') }}</li>
							</ol>
						</nav>
					</div>
					<!-- Page Header Box End -->
				</div>
			</div>
		</div>
	</div> --}}
    	<div class="page-header contact-page-header bg-radius-section parallaxie" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
		<div class="container">
			<div class="row align-items-center">
				<div class="col-lg-12">
					<!-- Page Header Box Start -->
					<div class="page-header-box">
						<h1 class="{{ app()->getLocale() === 'ar' ? 'arabic-title' : 'text-anime-style-2' }}" data-cursor="-opaque">{{ __('messages.Contact Us') }}</h1>
						<!-- Custom breadcrumb for RTL/LTR support -->
						@if(app()->getLocale() == 'ar')
							<!-- Arabic RTL breadcrumb -->
							<nav class="wow fadeInUp custom-rtl-breadcrumb">
								<div class="rtl-breadcrumb">
                                    <a href="{{ url(app()->getLocale()) }}" style="color: rgba(255, 255, 255, 0.7) !important; text-decoration: none !important; font-size: 16px;">{{ __('messages.Home') }}</a>
									<span class="separator" style="color: rgba(255, 255, 255, 0.7) !important; font-size: 16px;">/</span>
									<span style="color: #fff !important; font-size: 16px;">{{ __('messages.Contact Us') }}</span>
								</div>
							</nav>
						@else
							<!-- English LTR breadcrumb -->
							<nav class="wow fadeInUp">
								<ol class="breadcrumb">
									<li class="breadcrumb-item"><a href="{{ url(app()->getLocale()) }}">{{ __('messages.Home') }}</a></li>
									<li class="breadcrumb-item active" aria-current="page">{{ __('messages.Contact Us') }}</li>
								</ol>
							</nav>
						@endif
					</div>
					<!-- Page Header Box End -->
				</div>
			</div>
		</div>
	</div>
	<!-- Page Header End -->


    <!-- Page Contact Us Start -->
    <div class="page-contact-us bg-radius-section" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <!-- Contact Information Start -->
                    <div class="contact-information">
                        <!-- Section Title Start -->
                        <div class="section-title">
                            <h3 class="wow fadeInUp">{{ __('messages.Contact Us') }}</h3>
                            <h2 class="{{ app()->getLocale() === 'ar' ? 'arabic-title' : 'text-anime-style-3' }}" data-cursor="-opaque">{{ __('messages.get_expert_care') }}</h2>
                            <p class="wow fadeInUp" data-wow-delay="0.25s">{{ __('messages.contact_description') }}</p>
                        </div>
                        <!-- Section Title End -->

                        <!-- Contact Information List Start -->
                        <div class="contact-info-list wow fadeInUp" data-wow-delay="0.5s">
                            <p><EMAIL></p>
                            <p>{{ __('messages.phone_number') }}</p>
                            <a href="#" class="support-btn">{{ __('messages.schedule_consultation') }}</a>
                        </div>
                        <!-- Contact Information List End -->

                        <div class="contact-information-box">
                            <div class="contact-info-item wow fadeInUp" data-wow-delay="0.75s">
                                <div class="icon-box">
                                    <img src="{{ asset('assets/images/icon-location-blue.svg') }}" alt="location">
                                </div>
                                <div class="contact-info-content">
                                    <h3>{{ __('messages.bahrain_location') }}</h3>
                                    <p>{{ __('messages.Address') }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Contact Information End -->
                </div>

                <div class="col-lg-6">
                    <!-- Contact Form Start -->
                    <div class="contact-us-form">
                        <!-- Section Title Start -->
                        <div class="section-title">
                            <h2 class="{{ app()->getLocale() === 'ar' ? 'arabic-title' : 'text-anime-style-3' }}" data-cursor="-opaque">{{ __('messages.request_appointment') }}</h2>
                            <p class="wow fadeInUp" data-wow-delay="0.25s">{{ __('messages.appointment_description') }}</p>
                        </div>
                        <!-- Section Title End -->

                        <form id="contactForm" action="{{ route('contact.store') }}" method="POST" class="wow fadeInUp" data-wow-delay="0.5s">
                            @csrf
                            <div class="row">
                                <div class="form-group col-md-6 mb-4">
                                    <input type="text" name="fname" class="form-control @error('fname') is-invalid @enderror"
                                        id="fname" placeholder="{{ __('messages.first_name') }}" required value="{{ old('fname') }}">
                                    <div class="help-block with-errors"></div>
                                    @error('fname')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="form-group col-md-6 mb-4">
                                    <input type="text" name="lname" class="form-control @error('lname') is-invalid @enderror"
                                        id="lname" placeholder="{{ __('messages.last_name') }}" required value="{{ old('lname') }}">
                                    <div class="help-block with-errors"></div>
                                    @error('lname')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="form-group col-md-12 mb-4">
                                    <input type="email" name="email" class="form-control @error('email') is-invalid @enderror"
                                        id="email" placeholder="{{ __('messages.email') }}" required value="{{ old('email') }}">
                                    <div class="help-block with-errors"></div>
                                    @error('email')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="form-group col-md-12 mb-4">
                                    <input type="text" name="phone" class="form-control @error('phone') is-invalid @enderror"
                                        id="phone" placeholder="{{ __('messages.phone') }}" required value="{{ old('phone') }}">
                                    <div class="help-block with-errors"></div>
                                    @error('phone')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="form-group col-md-12 mb-4">
                                    <input type="text" name="subject" class="form-control @error('subject') is-invalid @enderror"
                                        id="subject" placeholder="{{ __('messages.subject_placeholder') }}" required value="{{ old('subject') }}">
                                    <div class="help-block with-errors"></div>
                                    @error('subject')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="form-group col-md-12 mb-4">
                                    <textarea name="message" class="form-control @error('message') is-invalid @enderror"
                                        id="message" rows="5" placeholder="{{ __('messages.message') }}" required>{{ old('message') }}</textarea>
                                    <div class="help-block with-errors"></div>
                                    @error('message')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-lg-12">
                                    <div class="contact-form-btn">
                                        <button type="submit" class="btn-default" id="submitButton">
                                            <span>{{ __('messages.submit_now') }}</span>
                                        </button>
                                        <div id="msgSubmit" class="h3 hidden"></div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <!-- Contact Form End -->
                </div>
            </div>
        </div>
    </div>
    <!-- Page Contact Us End -->

    <!-- Google Map Start -->
	<div class="google-map bg-radius-section">
        <div class="container-fluid">
            <div class="row">
                <div class="col-lg-12">
                    <!-- Google Map Iframe Start -->
                    <div class="google-map-iframe">
                        <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d7159.41161838363!2d50.55957614036861!3d26.206246683249493!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3e49afa54f269b4d%3A0xc3f488f7baa00684!2sAl%20Sharaf%20Orthopaedic%20%26%20spine%20Center!5e0!3m2!1sen!2sbh!4v1717257080382!5m2!1sen!2sbh" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>
                    </div>
                    <!-- Google Map Iframe End -->
                </div>
            </div>
        </div>
    </div>
	<!-- Google Map End -->

    <!-- Contact Faqs Section Start -->
    <div class="contact-faqs bg-radius-section" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
        <div class="container">
            <div class="row">
                <div class="col-lg-6">
                    <!-- Contact Faqs Content Start -->
                    <div class="contact-faqs-content">
                        <!-- Section Title Start -->
                        <div class="section-title">
                            <h3 class="wow fadeInUp">{{ __('messages.faqs') }}</h3>
                            <h2 class="{{ app()->getLocale() === 'ar' ? 'arabic-title' : 'text-anime-style-3' }}" data-cursor="-opaque">{{ __('messages.common_questions') }}</h2>
                            <p class="wow fadeInUp" data-wow-delay="0.25s">{{ __('messages.faq_description') }}</p>
                        </div>
                        <!-- Section Title End -->

                        <!-- Section Btn Start -->
                        <div class="section-btn">
                            <a href="#" class="btn-default"><span>{{ __('messages.schedule_consultation') }}</span></a>
                        </div>
                        <!-- Section Btn End -->
                    </div>
                    <!-- Contact Faqs Content End -->
                </div>

                <div class="col-lg-6">
                    <!-- About Faqs Section Start -->
                    <div class="about-faq-section">
                        <!-- FAQ Accordion Start -->
                        <div class="faq-accordion" id="accordion">
                            <!-- FAQ Item Start -->
                            <div class="accordion-item wow fadeInUp">
                                <h2 class="accordion-header" id="heading1">
                                    <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapse1" aria-expanded="true" aria-controls="collapse1">
                                        {{ __('messages.faq_question_1') }}
                                    </button>
                                </h2>
                                <div id="collapse1" class="accordion-collapse collapse show" aria-labelledby="heading1" data-bs-parent="#accordion">
                                    <div class="accordion-body">
                                        <p>{{ __('messages.faq_answer_1') }}</p>
                                    </div>
                                </div>
                            </div>
                            <!-- FAQ Item End -->

                            <!-- FAQ Item Start -->
                            <div class="accordion-item wow fadeInUp" data-wow-delay="0.2s">
                                <h2 class="accordion-header" id="heading2">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse2" aria-expanded="false" aria-controls="collapse2">
                                        {{ __('messages.faq_question_2') }}
                                    </button>
                                </h2>
                                <div id="collapse2" class="accordion-collapse collapse" aria-labelledby="heading2" data-bs-parent="#accordion">
                                    <div class="accordion-body">
                                        <p>{{ __('messages.faq_answer_2') }}</p>
                                    </div>
                                </div>
                            </div>
                            <!-- FAQ Item End -->

                            <!-- FAQ Item Start -->
                            <div class="accordion-item wow fadeInUp" data-wow-delay="0.4s">
                                <h2 class="accordion-header" id="heading3">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse3" aria-expanded="false" aria-controls="collapse3">
                                        {{ __('messages.faq_question_3') }}
                                    </button>
                                </h2>
                                <div id="collapse3" class="accordion-collapse collapse" aria-labelledby="heading3" data-bs-parent="#accordion">
                                    <div class="accordion-body">
                                        <p>{{ __('messages.faq_answer_3') }}</p>
                                    </div>
                                </div>
                            </div>
                            <!-- FAQ Item End -->

                            <!-- FAQ Item Start -->
                            <div class="accordion-item wow fadeInUp" data-wow-delay="0.6s">
                                <h2 class="accordion-header" id="heading4">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse4" aria-expanded="false" aria-controls="collapse4">
                                        {{ __('messages.faq_question_4') }}
                                    </button>
                                </h2>
                                <div id="collapse4" class="accordion-collapse collapse" aria-labelledby="heading4" data-bs-parent="#accordion">
                                    <div class="accordion-body">
                                        <p>{{ __('messages.faq_answer_4') }}</p>
                                    </div>
                                </div>
                            </div>
                            <!-- FAQ Item End -->

                            <!-- FAQ Item Start -->
                            <div class="accordion-item wow fadeInUp" data-wow-delay="0.8s">
                                <h2 class="accordion-header" id="heading5">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse5" aria-expanded="false" aria-controls="collapse5">
                                        {{ __('messages.faq_question_5') }}
                                    </button>
                                </h2>
                                <div id="collapse5" class="accordion-collapse collapse" aria-labelledby="heading5" data-bs-parent="#accordion">
                                    <div class="accordion-body">
                                        <p>{{ __('messages.faq_answer_5') }}</p>
                                    </div>
                                </div>
                            </div>
                            <!-- FAQ Item End -->
                        </div>
                        <!-- FAQ Accordion End -->
                    </div>
                    <!-- About Faqs Section End -->
                </div>
            </div>
        </div>
    </div>
    <!-- Contact Faqs Section End -->
@endsection

@push('scripts')
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script>
        $(document).ready(function() {
            // Remove any existing event listeners
            $("#contactForm").off('submit');

            $("#contactForm").on('submit', function(e) {
                // console.log('Form submission triggered');
                e.preventDefault();
                e.stopPropagation();  // Add this to prevent event bubbling

                let submitButton = $("#submitButton");

                // Prevent multiple submissions
                if (submitButton.prop('disabled')) {
                    // console.log('Submission blocked - button disabled');
                    return false;
                }

                submitButton.prop('disabled', true);
                submitButton.find('span').text('Sending...');

                // console.log('Making AJAX request...');

                $.ajax({
                    type: "POST",
                    url: $(this).attr('action'),
                    data: $(this).serialize(),
                    success: function(response) {
                        // console.log('Success response:', response);
                        if (response.success) {
                            $("#msgSubmit")
                                .removeClass('hidden text-danger')
                                .addClass('text-success')
                                .text(response.message);
                            $("#contactForm")[0].reset();
                        } else {
                            $("#msgSubmit")
                                .removeClass('hidden text-success')
                                .addClass('text-danger')
                                .text('Something went wrong. Please try again.');
                        }
                    },
                    error: function(xhr) {
                        // console.log('Error response:', xhr);
                        let errors = xhr.responseJSON.errors;
                        let errorMessage = 'Please check the form and try again.';

                        if (errors) {
                            errorMessage = Object.values(errors)[0][0];
                        }

                        $("#msgSubmit")
                            .removeClass('hidden text-success')
                            .addClass('text-danger')
                            .text(errorMessage);
                    },
                    complete: function() {
                        // console.log('Request completed');
                        submitButton.prop('disabled', false);
                        submitButton.find('span').text('submit now');
                    }
                });

                return false; // Ensure the form doesn't submit normally
            });

            // Remove the data-toggle="validator" attribute from the form
            $("#contactForm").removeAttr('data-toggle');
        });
    </script>
@endpush

@push('styles')
<style>
    /* RTL styles for contact page */
    [dir="rtl"] .page-header-box h1,
    [dir="rtl"] .page-header-box .breadcrumb {
        text-align: right;
        font-family: var(--arabic-font);
    }

    [dir="rtl"] .contact-information .section-title h3,
    [dir="rtl"] .contact-information .section-title h2,
    [dir="rtl"] .contact-information .section-title p,
    [dir="rtl"] .contact-us-form .section-title h2,
    [dir="rtl"] .contact-us-form .section-title p,
    [dir="rtl"] .contact-faqs-content .section-title h3,
    [dir="rtl"] .contact-faqs-content .section-title h2,
    [dir="rtl"] .contact-faqs-content .section-title p {
        text-align: right;
        font-family: var(--arabic-font);
    }

    [dir="rtl"] .contact-info-list,
    [dir="rtl"] .section-btn {
        text-align: right;
    }

    [dir="rtl"] .contact-info-item {
        flex-direction: row-reverse;
    }

    [dir="rtl"] .contact-info-item .icon-box {
        margin-right: 0;
        margin-left: 15px;
    }

    [dir="rtl"] .contact-info-content {
        text-align: right;
    }

    [dir="rtl"] .contact-form-btn {
        text-align: right;
    }

    [dir="rtl"] input,
    [dir="rtl"] textarea {
        text-align: right;
        direction: rtl;
    }

    [dir="rtl"] .accordion-button {
        text-align: right;
        flex-direction: row-reverse;
    }

    [dir="rtl"] .accordion-button::after {
        margin-right: auto;
        margin-left: 0;
    }

    [dir="rtl"] .accordion-body p {
        text-align: right;
    }
</style>
@endpush




