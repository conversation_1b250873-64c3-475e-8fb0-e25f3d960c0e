<?php

namespace App\Models;

use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Actions;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\RichEditor;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ContactUs extends Model
{
    use SoftDeletes;

    protected $table = 'contact_us';

    protected $fillable = [
        'first_name',
        'last_name',
        'email',
        'phone',
        'message',
        'subject',
        'preferred_contact_method',
        'status',
        'is_urgent',
        'admin_notes',
        'responded_at',
        'ip_address',
        'user_agent',
        'email_reply_en',
        'email_reply_ar',
        'email_subject_en',
        'email_subject_ar',
        'email_sent_at',
    ];

    protected $casts = [
        'is_urgent' => 'boolean',
        'responded_at' => 'datetime',
        'email_sent_at' => 'datetime',
    ];

    public static function getStatuses(): array
    {
        return [
            'new' => 'New',
            'in_progress' => 'In Progress',
            'completed' => 'Completed',
            'spam' => 'Spam',
        ];
    }

    public static function getContactMethods(): array
    {
        return [
            'email' => 'Email',
            'phone' => 'Phone',
            'both' => 'Both',
        ];
    }

    public static function getFormSchema(): array
    {
        return [
            Tabs::make('Contact Us')
                ->tabs([
                    Tabs\Tab::make('Contact Information')
                        ->icon('heroicon-o-user')
                        ->schema([
                            Grid::make()
                                ->schema([
                                    TextInput::make('first_name')
                                        ->disabled()
                                        ->columnSpan(1),
                                    TextInput::make('last_name')
                                        ->disabled()
                                        ->columnSpan(1),
                                    TextInput::make('email')
                                        ->disabled()
                                        ->columnSpan(1),
                                    TextInput::make('phone')
                                        ->disabled()
                                        ->columnSpan(1),
                                    TextInput::make('subject')
                                        ->disabled()
                                        ->columnSpan(1),
                                    Select::make('preferred_contact_method')
                                        ->options(self::getContactMethods())
                                        ->disabled()
                                        ->columnSpan(1),
                                ])
                                ->columns(2),
                        ]),

                    Tabs\Tab::make('Message')
                        ->icon('heroicon-o-chat-bubble-left-right')
                        ->schema([
                            Grid::make()
                                ->schema([
                                    Textarea::make('message')
                                        ->disabled()
                                        ->rows(4)
                                        ->columnSpan('full'),
                                ])
                                ->columns(1),
                        ]),

                    Tabs\Tab::make('Administrative')
                        ->icon('heroicon-o-cog')
                        ->schema([
                            Grid::make()
                                ->schema([
                                    Select::make('status')
                                        ->options(self::getStatuses())
                                        ->required()
                                        ->columnSpan(1),
                                    Toggle::make('is_urgent')
                                        ->label('Urgent Matter')
                                        ->helperText('Mark as urgent for immediate medical attention or time-sensitive inquiries')
                                        ->required()
                                        ->columnSpan(1),
                                    Textarea::make('admin_notes')
                                        ->label('Administrative Notes')
                                        ->helperText('Add internal notes about urgency, required actions, or follow-up details')
                                        ->rows(3)
                                        ->columnSpan('full'),
                                    DateTimePicker::make('responded_at')
                                        ->columnSpan(1),
                                ])
                                ->columns(2),
                        ]),

                    Tabs\Tab::make('System Info')
                        ->icon('heroicon-o-computer-desktop')
                        ->schema([
                            Grid::make()
                                ->schema([
                                    TextInput::make('ip_address')
                                        ->disabled()
                                        ->columnSpan(1),
                                    TextInput::make('user_agent')
                                        ->disabled()
                                        ->columnSpan(1),
                                ])
                                ->columns(2),
                        ]),

                    // Tabs\Tab::make('Email Reply')
                    //     ->icon('heroicon-o-envelope')
                    //     ->schema([
                    //         Select::make('reply_language')
                    //             ->label('Reply Language')
                    //             ->options([
                    //                 'en' => 'English',
                    //                 'ar' => 'Arabic'
                    //             ])
                    //             ->default('en')
                    //             ->reactive()
                    //             ->required()
                    //             ->columnSpan('full'),

                    //         Grid::make()
                    //             ->schema([
                    //                 TextInput::make('email_subject_en')
                    //                     ->label('Subject (English)')
                    //                     ->default(function (Model $record) {
                    //                         return 'Re: ' . ($record->subject ?? 'Your Inquiry');
                    //                     })
                    //                     ->visible(fn (\Filament\Forms\Get $get) => $get('reply_language') === 'en')
                    //                     ->required()
                    //                     ->columnSpan('full'),

                    //                 TextInput::make('email_subject_ar')
                    //                     ->label('Subject (Arabic)')
                    //                     ->default(function (Model $record) {
                    //                         return 'رد: ' . ($record->subject ?? 'استفسارك');
                    //                     })
                    //                     ->visible(fn (\Filament\Forms\Get $get) => $get('reply_language') === 'ar')
                    //                     ->required()
                    //                     ->extraAttributes([
                    //                         'dir' => 'rtl',
                    //                         'style' => 'text-align: right'
                    //                     ])
                    //                     ->columnSpan('full'),

                    //                 RichEditor::make('email_reply_en')
                    //                     ->label('Reply Message (English)')
                    //                     ->visible(fn (\Filament\Forms\Get $get) => $get('reply_language') === 'en')
                    //                     ->required()
                    //                     ->toolbarButtons([
                    //                         'bold',
                    //                         'italic',
                    //                         'underline',
                    //                         'strike',
                    //                         'link',
                    //                         'orderedList',
                    //                         'unorderedList',
                    //                         'redo',
                    //                         'undo',
                    //                     ])
                    //                     ->default(function (Model $record) {
                    //                         return "Dear {$record->first_name},<br><br><br><br>Best regards,<br>Your Name";
                    //                     })
                    //                     ->columnSpan('full'),

                    //                 RichEditor::make('email_reply_ar')
                    //                     ->label('Reply Message (Arabic)')
                    //                     ->visible(fn (\Filament\Forms\Get $get) => $get('reply_language') === 'ar')
                    //                     ->required()
                    //                     ->toolbarButtons([
                    //                         'bold',
                    //                         'italic',
                    //                         'underline',
                    //                         'strike',
                    //                         'link',
                    //                         'orderedList',
                    //                         'unorderedList',
                    //                         'redo',
                    //                         'undo',
                    //                     ])
                    //                     ->default(function (Model $record) {
                    //                         return "عزيزي/عزيزتي {$record->first_name}،<br><br><br><br>مع أطيب التحيات،<br>اسمك";
                    //                     })
                    //                     ->extraAttributes([
                    //                         'dir' => 'rtl',
                    //                         'style' => 'text-align: right'
                    //                     ])
                    //                     ->columnSpan('full'),

                    //                 Actions::make([
                    //                     Action::make('send_email')
                    //                         ->label('Send Email')
                    //                         ->color('primary')
                    //                         ->icon('heroicon-o-paper-airplane')
                    //                         ->requiresConfirmation()
                    //                         ->modalHeading('Send Email Reply')
                    //                         ->modalSubheading('Are you sure you want to send this email?')
                    //                         ->modalButton('Yes, send email')
                    //                         ->action(function (Model $record, \Filament\Forms\Set $set) {
                    //                             // Get the current Livewire component (EditContactUs page)
                    //                             $livewire = \Livewire\Livewire::current();

                    //                             // Call the sendEmail method
                    //                             $livewire->sendEmail();
                    //                         })
                    //                         ->visible(fn (Model $record) => !$record->email_sent_at),
                    //                 ])
                    //                 ->columnSpan('full'),

                    //                 DateTimePicker::make('email_sent_at')
                    //                     ->label('Email Sent At')
                    //                     ->disabled()
                    //                     ->columnSpan('full'),
                    //             ])
                    //             ->columns(1),
                    //     ]),
                ])
                ->columnSpanFull()
                ->persistTabInQueryString()
        ];
    }
}


















