    /* Back to top button styles */
    .back-to-top {
        position: fixed;
        right: 50px;
        bottom: 50px;
        height: 60px; /* Increased size for visibility */
        width: 60px;  /* Increased size for visibility */
        cursor: pointer;
        display: block;
        border-radius: 50%; /* Circular shape */
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2); /* Added shadow for depth */
        z-index: 10000;
        opacity: 0;
        visibility: hidden;
        transform: translateY(15px);
        transition: all 200ms ease-in-out;
        background-color: #002b45; /* Dark blue background to match theme */
        border: none;
        outline: none;
    }

    /* Active state when visible */
    .back-to-top.active {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
    }

    /* Circle progress stroke */
    .back-to-top svg path {
        fill: none;
    }

    .back-to-top svg.back-to-top-circle path {
        stroke: #00bfff; /* Bright blue for the progress bar */
        stroke-width: 4;
        box-sizing: border-box;
        transition: all 200ms ease-in-out;
    }

    /* Arrow icon in the middle */
    .back-to-top::after {
        position: absolute;
        font-family: 'Font Awesome 5 Free';
        font-weight: 900;
        content: '\f062'; /* Upwards arrow */
        text-align: center;
        line-height: 60px;
        font-size: 24px; /* Bigger arrow */
        color: #ffffff; /* White color for visibility */
        left: 0;
        top: 0;
        height: 60px;
        width: 60px;
        cursor: pointer;
        display: block;
        z-index: 1;
        transition: all 200ms ease-in-out;
    }

    /* Hover glow effect on arrow */
    .back-to-top::before {
        position: absolute;
        font-family: 'Font Awesome 5 Free';
        font-weight: 900;
        content: '\f062'; /* Upwards arrow */
        text-align: center;
        line-height: 60px;
        font-size: 24px;
        opacity: 0;
        background-image: linear-gradient(298deg, #00bfff, #0088cc); /* Gradient effect from light to dark blue */
        background-clip: text;
        color: transparent;
        left: 0;
        top: 0;
        height: 60px;
        width: 60px;
        cursor: pointer;
        display: block;
        z-index: 2;
        transition: all 200ms ease-in-out;
    }

    /* Hover effect for glowing arrow icon */
    .back-to-top:hover::before {
        opacity: 1;
    }

    /* Mobile Responsiveness */
    @media (max-width: 768px) {
        .back-to-top {
            right: 30px;
            bottom: 30px;
            height: 50px; /* Slightly smaller size for mobile */
            width: 50px;
        }

        .back-to-top svg path {
            stroke-width: 3;
        }

        .back-to-top::after {
            font-size: 20px; /* Slightly smaller font size */
            line-height: 50px;
            height: 50px;
            width: 50px;
        }

        .back-to-top::before {
            font-size: 20px;
            line-height: 50px;
            height: 50px;
            width: 50px;
        }
    }

    /* Extra small screens */
    @media (max-width: 480px) {
        .back-to-top {
            right: 20px;
            bottom: 20px;
            height: 45px; /* Even smaller size for small devices */
            width: 45px;
        }

        .back-to-top svg path {
            stroke-width: 3;
        }

        .back-to-top::after {
            font-size: 18px;
            line-height: 45px;
            height: 45px;
            width: 45px;
        }

        .back-to-top::before {
            font-size: 18px;
            line-height: 45px;
            height: 45px;
            width: 45px;
        }
    }
