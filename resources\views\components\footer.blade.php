    <!-- Footer Start -->
    <footer class="main-footer bg-radius-section" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <!-- About Footer Start -->
                    <div class="about-footer">
                        <div class="row align-items-center">
                            <div class="col-lg-4">
                                <!-- Footer Logo Start -->
                                <div class="footer-logo d-flex align-items-center justify-content-center h-100">
                                    <img src="{{ asset("assets/images/sharaf_logo.png") }}" alt="Logo">
                                </div>
                                <!-- Footer Logo End -->
                            </div>

                            <div class="col-lg-8">
                                <!-- About Footer Content Start -->
                                <div class="about-footer-content">
                                    <h3 class="footer-title animate-text">
                                        {{ __('messages.footer_title') }}
                                    </h3>

                                    <p class="footer-description">
                                        {{ __('messages.footer_description') }}
                                    </p>
                                </div>
                                <!-- About Footer Content End -->
                            </div>
                        </div>
                    </div>
                    <!-- About Footer End -->

                    <!-- About Footer List Start -->
                    <div class="about-footer-list">
                        <!-- Footer Quick Links Start -->
                        <div class="footer-links quick-links">
                            <h3>{{ __('messages.quick_links') }}</h3>
                            <ul>
                                <li><a href="#">{{ __('messages.about_us') }}</a></li>
                                <li><a href="#">{{ __('messages.services') }}</a></li>
                                <li><a href="#">{{ __('messages.contact_us') }}</a></li>
                            </ul>
                        </div>
                        <!-- Footer Quick Links End -->

                        <!-- Footer Quick Links Start -->
                        <div class="footer-links service-links">
                            <h3>{{ __('messages.more_services') }}</h3>
                            <ul>
                                <li><a href="#">{{ __('messages.spine_back_pain') }}</a></li>
                                <li><a href="#">{{ __('messages.non_surgical') }}</a></li>
                                <li><a href="#">{{ __('messages.physical_therapy') }}</a></li>
                            </ul>
                        </div>
                        <!-- Footer Quick Links End -->

                        <!-- Footer Quick Links Start -->
                        <div class="footer-links social-links">
                            <h3>{{ __('messages.social_media') }}</h3>
                            <ul>
                                <li><a href="https://www.facebook.com/profile.php?id=100063836869090" target="_blank"><i class="fa-brands fa-facebook-f"></i></a></li>
                                <li><a href="#"><i class="fa-brands fa-twitter"></i></a></li>
                                <li><a href="#"><i class="fa-brands fa-linkedin-in"></i></a></li>
                                <li><a href="https://www.instagram.com/alsharafcenter/" target="_blank"><i class="fa-brands fa-instagram"></i></a></li>
                            </ul>
                        </div>
                        <!-- Footer Quick Links End -->

                        <!-- Footer Quick Links Start -->
                        <div class="footer-links working-links">
                            <h3>{{ __('messages.working_hours') }}</h3>
                            <ul>
                                <li>{{ __('messages.sat_to_thu') }}</li>
                                <li>{{ __('messages.friday_closed') }}</li>
                            </ul>
                        </div>
                        <!-- Footer Quick Links End -->

                        <!-- Footer Contact Details Start -->
                        <div class="footer-links footer-contact-details">
                            <h3>{{ __('messages.help_support') }}</h3>
                            <div class="footer-contact-box">
                                <!-- Footer Info Box Start -->
                                <div class="footer-info-box">
                                    <div class="icon-box">
                                        <img src="{{ asset("assets/images/icon-phone.svg") }}" alt="">
                                    </div>
                                    <div class="footer-info-box-content">
                                        <a href="tel:+97317336601">
                                            <p>{{ __('messages.phone_number') }}</p>
                                        </a>
                                    </div>
                                </div>
                                <!-- Footer Info Box End -->

                                <!-- Footer Info Box Start -->
                                <div class="footer-info-box">
                                    <div class="icon-box">
                                        <img src="{{ asset("assets/images/icon-mail.svg") }}" alt="">
                                    </div>
                                    <div class="footer-info-box-content">
                                        <a href="mailto:<EMAIL>">
                                            <p><EMAIL></p>
                                        </a>
                                    </div>
                                </div>
                                <!-- Footer Info Box End -->
                            </div>
                        </div>
                        <!-- Footer Contact Details End -->

                        <!-- Footer Copyright Section Start -->
                        <div class="footer-links terms-condition-links">
                            <div class="footer-copyright-text">
                                <p>{!! __('messages.copyright', ['year' => date('Y'), 'title' => __('meta.default_title')]) !!}</p>
                            </div>
                            <div class="footer-terms-condition">
                                <ul>
                                    <li><a href="#">{{ __('messages.privacy_policy') }}</a></li>
                                    <li><a href="#">{{ __('messages.terms_conditions') }}</a></li>
                                </ul>
                            </div>
                        </div>
                        <!-- Footer Copyright Section End -->
                    </div>
                    <!-- About Footer List End -->
                </div>
            </div>
        </div>
    </footer>
    <!-- Footer End -->

    <!-- Jquery Library File -->
    <script src="{{ asset("assets/js/jquery-3.7.1.min.js") }}"></script>
    <!-- Bootstrap js file -->
    <script src="{{ asset("assets/js/bootstrap.min.js") }}"></script>
    <!-- Validator js file -->
    <script src="{{ asset("assets/js/validator.min.js") }}"></script>
    <!-- SlickNav js file -->
    <script src="{{ asset("assets/js/jquery.slicknav.js") }}"></script>
    <!-- Swiper js file -->
    <script src="{{ asset("assets/js/swiper-bundle.min.js") }}"></script>
    <!-- Counter js file -->
    <script src="{{ asset("assets/js/jquery.waypoints.min.js") }}"></script>
    <script src="{{ asset("assets/js/jquery.counterup.min.js") }}"></script>
    <!-- Magnific js file -->
    <script src="{{ asset("assets/js/jquery.magnific-popup.min.js") }}"></script>
    <!-- SmoothScroll - REMOVED due to scroll issues -->
    <!-- <script src="{{ asset("assets/js/SmoothScroll.js") }}"></script> -->
    <!-- Parallax js -->
    <script src="{{ asset("assets/js/parallaxie.js") }}"></script>
    <!-- MagicCursor js file -->
    <script src="{{ asset("assets/js/gsap.min.js") }}"></script>
    <script src="{{ asset("assets/js/magiccursor.js") }}"></script>
    <!-- Text Effect js file -->
    <script src="{{ asset("assets/js/SplitText.js") }}"></script>
    <script src="{{ asset("assets/js/ScrollTrigger.min.js") }}"></script>
    <!-- YTPlayer js File -->
    <script src="{{ asset("assets/js/jquery.mb.YTPlayer.min.js") }}"></script>
    <!-- Wow js file -->
    <script src="{{ asset("assets/js/wow.js") }}"></script>
    <!-- Main Custom js file -->
    <script src="{{ asset("assets/js/function.js") }}"></script>
    <script>
        // JavaScript to add and remove sticky class
        window.addEventListener('scroll', function () {
            const header = document.querySelector('.header-sticky');
            if (window.scrollY > 100) {
                header.classList.add('active');
            } else {
                header.classList.remove('active');
            }
        });
    </script>
    <style>
        .about-footer-content {
            padding: 30px 0;
            position: relative;
            overflow: hidden;
        }

        .footer-title {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 25px;
            color: var(--white-color);
            text-transform: capitalize;
            position: relative;
            padding-bottom: 15px;
        }

        .footer-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 60px;
            height: 3px;
            background: var(--secondary-color);
            border-radius: 2px;
        }

        [dir="rtl"] .footer-title::after {
            left: auto;
            right: 0;
        }


        .divider {
            color: rgba(255, 255, 255, 0.3);
            margin: 0 15px;
        }

        .footer-description {
            margin-top: 20px;
            line-height: 1.8;
            color: rgba(255, 255, 255, 0.9);
            font-size: 16px;
            max-width: 800px;
        }



        /* Animation */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-text {
            animation: fadeInUp 0.6s ease-out forwards;
        }

        /* RTL specific styles */
        [dir="rtl"] .footer-title {
            font-family: var(--arabic-font);
        }


        [dir="rtl"] .footer-description {
            font-family: var(--arabic-font);
            line-height: 1.8;
        }

        /* Responsive styles */
        @media (max-width: 991px) {
            .footer-title {
                font-size: 28px;
            }
        }

        @media (max-width: 768px) {
            .about-footer-content {
                padding: 20px 0;
            }

            .footer-title {
                font-size: 24px;
                text-align: center;
            }

            .footer-title::after {
                left: 50%;
                transform: translateX(-50%);
            }

            [dir="rtl"] .footer-title::after {
                right: 50%;
                transform: translateX(50%);
            }


            .divider {
                display: none;
            }

            .footer-description {
                text-align: center;
                font-size: 15px;
                padding: 0 15px;
            }
        }

        @media (max-width: 480px) {
            .footer-title {
                font-size: 22px;
            }
        }
    </style>

    <style>
        .footer-logo {
            min-height: 200px; /* Adjust this value based on your content height */
            margin: 0;
        }

        .footer-logo img {
            width: 100%;
            max-width: 220px;
            object-fit: contain;
        }

        @media (max-width: 991px) {
            .footer-logo {
                min-height: auto;
                margin-bottom: 30px;
            }
        }
    </style>

    <style>
        /* RTL Styles for Footer Links and Content */
        [dir="rtl"] .about-footer-list {
            text-align: right;
        }

        [dir="rtl"] .footer-links h3,
        [dir="rtl"] .footer-links ul li a,
        [dir="rtl"] .footer-links ul li {
            font-family: var(--arabic-font);
            text-align: right;
        }

        [dir="rtl"] .footer-links ul {
            padding-right: 20px; /* Add padding for bullets */
            padding-left: 0;
            margin: 0;
        }

        [dir="rtl"] .footer-links ul li {
            position: relative;
            padding-right: 15px; /* Space for bullet */
            text-align: right;
        }

        [dir="rtl"] .footer-links ul li::before {
            /* content: "•"; */
            position: absolute;
            right: 0;
            color: var(--white-color);
        }

        /* Special handling for social links - no bullets */
        [dir="rtl"] .footer-links.social-links ul {
            padding-right: 0;
            display: flex;
            justify-content: flex-end;
            gap: 15px;
        }

        [dir="rtl"] .footer-links.social-links ul li {
            padding-right: 0;
        }

        [dir="rtl"] .footer-links.social-links ul li::before {
            display: none;
        }

        /* Working hours alignment */
        [dir="rtl"] .footer-links.working-links ul li {
            text-align: right;
        }

        /* Contact details alignment */
        [dir="rtl"] .footer-links.footer-contact-details {
            text-align: right;
        }

        [dir="rtl"] .footer-contact-box {
            justify-content: flex-start;
        }

        [dir="rtl"] .footer-info-box {
            margin-left: 25px;
            margin-right: 0;
        }

        [dir="rtl"] .footer-info-box:last-child {
            margin-left: 0;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            [dir="rtl"] .footer-links ul,
            [dir="rtl"] .footer-links.social-links ul {
                justify-content: center;
            }

            [dir="rtl"] .footer-links ul li,
            [dir="rtl"] .footer-contact-box {
                text-align: center;
                justify-content: center;
            }

            [dir="rtl"] .footer-info-box {
                margin-left: 50%; /* Reduced margin for mobile */
                margin-right: 0;
            }

            [dir="rtl"] .footer-info-box:last-child {
                margin-left: 20%; /* Remove margin for last item on mobile */
            }


        }
    </style>

    <style>
        /* RTL styles for social links section */
        [dir="rtl"] .footer-links.social-links {
            text-align: right;
        }

        [dir="rtl"] .footer-links.social-links h3 {
            text-align: right;
        }

        [dir="rtl"] .footer-links.social-links ul {
            justify-content: flex-start;  /* Align items to the right */
            padding-right: 0;
        }

        /* Ensure consistent spacing */
        [dir="rtl"] .footer-links.social-links ul li {
            margin-left: 15px;
            margin-right: 0;
        }

        [dir="rtl"] .footer-links.social-links ul li:last-child {
            margin-left: 0;
        }
    </style>

    <style>
        /* General styles */
        .footer-info-box {
            display: flex;
            align-items: center;
        }

        .icon-box {
            margin-right: 15px; /* Default gap for LTR */
        }

        .footer-info-box-content {
            flex: 1;
        }

        /* RTL specific styles */
        [dir="rtl"] .icon-box {
            margin-right: 0;
            margin-left: 25px; /* Increased gap for RTL */
        }

        /* Responsive styles */
        @media (max-width: 768px) {
            [dir="rtl"] .icon-box {
                margin-left: 15px; /* Smaller gap for mobile */
            }
        }
    </style>



