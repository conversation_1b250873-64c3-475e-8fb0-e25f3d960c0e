<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class CommonCause extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'body_part_id',
        'title_en',
        'title_ar',
        'description_en',
        'description_ar',
        'image',
        'is_active',
        'display_order',
        'meta_title_en',
        'meta_title_ar',
        'meta_description_en',
        'meta_description_ar',
        'attachments',
        'videos',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'display_order' => 'integer',
        'attachments' => 'array',
        'videos' => 'array'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    /**
     * Get the body part that this common cause belongs to.
     */
    public function bodyPart()
    {
        return $this->belongsTo(BodyPart::class);
    }

    /**
     * Get the image URL attribute.
     */
    public function getImageUrlAttribute()
    {
        return $this->image ? Storage::disk('public')->url($this->image) : null;
    }

    /**
     * Get the common cause's title based on current locale.
     */
    public function getTitleAttribute()
    {
        $locale = app()->getLocale();
        return $locale === 'ar' ? $this->title_ar : $this->title_en;
    }

    /**
     * Get the common cause's description based on current locale.
     */
    public function getDescriptionAttribute()
    {
        $locale = app()->getLocale();
        return $locale === 'ar' ? $this->description_ar : $this->description_en;
    }

    /**
     * Get the common cause's meta title based on current locale.
     */
    public function getMetaTitleAttribute()
    {
        $locale = app()->getLocale();
        return $locale === 'ar' ? $this->meta_title_ar : $this->meta_title_en;
    }

    /**
     * Get the common cause's meta description based on current locale.
     */
    public function getMetaDescriptionAttribute()
    {
        $locale = app()->getLocale();
        return $locale === 'ar' ? $this->meta_description_ar : $this->meta_description_en;
    }

    public function getVideoUrlsAttribute()
    {
        return array_map(function($video) {
            // Remove any duplicate paths and ensure clean path
            $cleanPath = str_replace('common-causes/videos/', '', $video);
            return url('/storage/common-causes/videos/' . $cleanPath);
        }, $this->videos ?? []);
    }

    /**
     * Scope a query to only include active causes.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to order causes by display order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('display_order');
    }

    public function getAttachmentUrls()
    {
        if (!$this->attachments) return [];
        return $this->attachments; // Since we're now storing full URLs, we can return them directly
    }

    protected static function boot()
    {
        parent::boot();

        static::saving(function ($model) {
            $attachments = [];

            // Extract image URLs from content
            $patterns = [
                '/<img[^>]*src=["\']([^"\']*)["\'][^>]*>/i',
            ];

            foreach ($patterns as $pattern) {
                // Extract from English description
                if (preg_match_all($pattern, $model->description_en, $matches)) {
                    foreach ($matches[1] as $match) {
                        // If it's already a full URL, add it
                        if (str_starts_with($match, 'http')) {
                            $attachments[] = $match;
                        } else {
                            // Convert relative path to full URL
                            $path = preg_replace(
                                [
                                    '#^/storage/#',
                                    '#^storage/#'
                                ],
                                '',
                                $match
                            );
                            $attachments[] = url('/storage/' . $path);
                        }
                    }
                }

                // Extract from Arabic description
                if ($model->description_ar && preg_match_all($pattern, $model->description_ar, $matches)) {
                    foreach ($matches[1] as $match) {
                        // If it's already a full URL, add it
                        if (str_starts_with($match, 'http')) {
                            $attachments[] = $match;
                        } else {
                            // Convert relative path to full URL
                            $path = preg_replace(
                                [
                                    '#^/storage/#',
                                    '#^storage/#'
                                ],
                                '',
                                $match
                            );
                            $attachments[] = url('/storage/' . $path);
                        }
                    }
                }
            }

            // Clean up content by ensuring proper URL format
            $model->description_en = preg_replace(
                [
                    '#(src=["\'](https?://[^/]+))storage/#',
                    '#(src=["\']/?)public/storage/#',
                    '#(src=["\']https?://[^/]+)/public/storage/#'
                ],
                '$1/storage/',
                $model->description_en
            );

            if ($model->description_ar) {
                $model->description_ar = preg_replace(
                    [
                        '#(src=["\'](https?://[^/]+))storage/#',
                        '#(src=["\']/?)public/storage/#',
                        '#(src=["\']https?://[^/]+)/public/storage/#'
                    ],
                    '$1/storage/',
                    $model->description_ar
                );
            }

            $model->attachments = array_values(array_unique(array_filter($attachments)));
        });
    }

        /**
     * Get processed description with proper media URLs
     */
    public function getProcessedDescription($locale = null)
    {
        $locale = $locale ?? app()->getLocale();
        $field = "description_" . $locale;

        if (empty($this->$field)) return null;

        return preg_replace_callback(
            [
                '#(src=["\'](https?://[^/]+))storage/#',  // Fix missing slash after domain
                '#(src=["\']/?)public/storage/#',         // Remove 'public' from path
                '#(src=["\']https?://[^/]+)/public/storage/#' // Remove 'public' from absolute URLs
            ],
            function ($matches) {
                return $matches[1] . '/storage/';
            },
            $this->$field
        );
    }

    public function testImageExtraction($content)
    {
        $patterns = [
            '/<img[^>]*src=["\']([^"\']*common-causes\/media\/[^"\']*)["\'][^>]*>/i',
            '/href=["\']([^"\']*common-causes\/media\/[^"\']*)["\']/',
        ];

        $attachments = [];
        foreach ($patterns as $pattern) {
            if (preg_match_all($pattern, $content, $matches)) {
                Log::info('Matches found:', ['matches' => $matches]);
                $attachments = array_merge($attachments, $matches[1]);
            }
        }

        return $attachments;
    }

    public function getProcessedContent($content)
    {
        if (!$content) return '';

        return preg_replace_callback(
            '/<img[^>]*src=["\']([^"\']*)["\'][^>]*>/i',
            function($matches) {
                $url = $matches[1];
                if (strpos($url, '/storage/') === false) {
                    return str_replace($url, Storage::disk('public')->url($url), $matches[0]);
                }
                return $matches[0];
            },
            $content
        );
    }
}










