<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('services', function (Blueprint $table) {
            $table->id();

            // Basic Info
            $table->string('title_en');
            $table->string('title_ar')->nullable();
            $table->string('slug')->unique();

            // Content
            $table->longText('description_en');
            $table->longText('description_ar')->nullable();
            $table->longText('details_en')->nullable();
            $table->longText('details_ar')->nullable();

            // Media
            $table->string('icon')->nullable();
            $table->string('featured_image')->nullable();
            $table->json('attachments')->nullable();
            $table->json('gallery_images')->nullable();
            $table->json('videos')->nullable();

            // SEO
            $table->string('meta_title_en')->nullable();
            $table->string('meta_title_ar')->nullable();
            $table->text('meta_description_en')->nullable();
            $table->text('meta_description_ar')->nullable();

            // Status and Order
            $table->boolean('is_active')->default(true);
            $table->integer('display_order')->default(0);

            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('services');
    }
};

