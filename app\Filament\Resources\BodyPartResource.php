<?php

namespace App\Filament\Resources;

use App\Filament\Resources\BodyPartResource\Pages\ListBodyParts;
use App\Filament\Resources\BodyPartResource\Pages\CreateBodyPart;
use App\Filament\Resources\BodyPartResource\Pages\EditBodyPart;
use App\Models\BodyPart;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Table;

class BodyPartResource extends Resource
{
    protected static ?string $model = BodyPart::class;

    // Change the label in navigation menu
    protected static ?string $navigationLabel = 'Parts';

    // Change the singular label
    protected static ?string $modelLabel = 'Part';

    // Change the plural label
    protected static ?string $pluralModelLabel = 'Parts';

    protected static ?string $navigationIcon = 'fas-bone';
    // OR if you prefer the regular (outlined) style:
    // protected static ?string $navigationIcon = 'fontawesome-regular-bone';

    public static function form(Form $form): Form
    {
        // return $form
        //     ->schema([
        //         Forms\Components\TextInput::make('name_en')
        //             ->maxLength(255)
        //             ->default(null),
        //         Forms\Components\TextInput::make('name_ar')
        //             ->maxLength(255)
        //             ->default(null),
        //         Forms\Components\RichEditor::make('description_en')
        //             ->columnSpanFull(),
        //         Forms\Components\RichEditor::make('description_ar')
        //             ->columnSpanFull(),
        //     ]);
        return $form
        ->schema(BodyPart::getFormSchema());
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name_en')
                    ->searchable(),
                Tables\Columns\TextColumn::make('name_ar')
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListBodyParts::route('/'),
            'create' => CreateBodyPart::route('/create'),
            'edit' => EditBodyPart::route('/{record}/edit'),
        ];
    }
}













