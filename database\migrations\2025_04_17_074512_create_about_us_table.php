<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('about_us', function (Blueprint $table) {
            $table->id();

            // Mission & Vision
            $table->longText('mission_en');
            $table->longText('mission_ar');
            $table->longText('vision_en');
            $table->longText('vision_ar');

            // Media
            $table->string('featured_image')->nullable();
            $table->string('mission_icon')->nullable();
            $table->string('vision_icon')->nullable();
            $table->string('video_url')->nullable();

            // SEO
            $table->string('meta_title_en')->nullable();
            $table->string('meta_title_ar')->nullable();
            $table->text('meta_description_en')->nullable();
            $table->text('meta_description_ar')->nullable();

            // Settings
            $table->boolean('is_active')->default(true);

            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('about_us');
    }
};
