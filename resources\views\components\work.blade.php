    <!-- How It Work Section Start -->
    <div class="how-it-work bg-radius-section" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6 order-lg-1 order-2">
                    <!-- How Work Images Start -->
                    <div class="how-work-images">
                        <div class="row no-gutters">
                            <div class="col-4">
                                <!-- How Work Image Box Start -->
                                <div class="how-work-image-box-1">
                                    <div class="how-work-img-1">
                                        <figure class="image-anime reveal">
                                            <img src="{{ asset("assets/images/how-work-img-1.jpg") }}" alt="Initial Consultation">
                                        </figure>
                                    </div>
                                    <div class="how-work-img-3">
                                        <figure class="image-anime reveal">
                                            <img src="{{ asset("assets/images/how-work-img-3.jpg") }}" alt="Treatment Process">
                                        </figure>
                                    </div>
                                </div>
                                <!-- How Work Image Box End -->
                            </div>

                            <div class="col-8">
                                <!-- How Work Image Box Start -->
                                <div class="how-work-image-box-2">
                                    <div class="how-work-img-2">
                                        <figure class="image-anime reveal">
                                            <img src="{{ asset("assets/images/how-work-img-2.jpg") }}" alt="Diagnostic Process">
                                        </figure>
                                    </div>
                                    <div class="how-work-img-4">
                                        <figure class="image-anime reveal">
                                            <img src="{{ asset("assets/images/how-work-img-4.jpg") }}" alt="Recovery and Rehabilitation">
                                        </figure>
                                    </div>
                                </div>
                                <!-- How Work Image Box End -->
                            </div>
                        </div>
                    </div>
                    <!-- How Work Images End -->
                </div>

                <div class="col-lg-6 order-lg-2 order-1">
                    <!-- How Work Content Start -->
                    <div class="how-work-content">
                        <!-- Section Title Start -->
                        <div class="section-title">
                            <h3 class="wow fadeInUp">{{ __('messages.patient journey') }}</h3>
                            <h2 class="{{ app()->getLocale() === 'ar' ? 'arabic-title' : 'text-anime-style-3' }}" data-cursor="-opaque">{{ __('messages.Your Path to Recovery') }}</h2>
                        </div>
                        <!-- Section Title End -->

                        <!-- How Work Accordion Start -->
                        <div class="how-work-accordion" id="accordion">
                            <!-- Step 1: Initial Consultation -->
                            <div class="accordion-item wow fadeInUp" style="{{ app()->getLocale() == 'ar' ? 'overflow: visible !important; border-radius: 10px !important;' : '' }}">
                                <h2 class="accordion-header" id="heading1" style="{{ app()->getLocale() == 'ar' ? 'overflow: visible !important;' : '' }}">
                                    <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                        data-bs-target="#collapse1" aria-expanded="true" aria-controls="collapse1"
                                        style="{{ app()->getLocale() == 'ar' ? 'padding: 15px 90px 15px 50px !important; text-align: right; font-family: var(--arabic-font); overflow: visible !important; border-top-left-radius: 10px !important; border-top-right-radius: 10px !important;' : '' }}">
                                        {{ __('messages.initial consultation') }}
                                    </button>
                                    <div class="icon-box" style="{{ app()->getLocale() == 'ar' ? 'position: absolute !important; left: auto !important; right: 15px !important; top: 50% !important; transform: translateY(-50%) !important; background-color: var(--secondary-color) !important; border-radius: 50% !important; width: 43px !important; height: 43px !important; display: flex !important; align-items: center !important; justify-content: center !important; z-index: 10 !important; overflow: visible !important; margin: 0 !important;' : '' }}">
                                        <img src="{{ asset("assets/images/icon-how-it-work-1.svg") }}" alt="Consultation Icon" style="{{ app()->getLocale() == 'ar' ? 'max-width: 36px !important;' : '' }}">
                                    </div>
                                </h2>
                                <div id="collapse1" class="accordion-collapse collapse show" aria-labelledby="heading1"
                                    data-bs-parent="#accordion">
                                    <div class="accordion-body" style="{{ app()->getLocale() == 'ar' ? 'border-bottom-left-radius: 10px !important; border-bottom-right-radius: 10px !important;' : '' }}">
                                        <p style="{{ app()->getLocale() == 'ar' ? 'text-align: right; font-family: var(--arabic-font);' : '' }}">{{ __('messages.Meet with our orthopedic specialists for a comprehensive evaluation of your condition. We\'ll review your medical history, perform necessary examinations, and discuss your symptoms in detail.') }}</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Step 2: Diagnostic Process -->
                            <div class="accordion-item wow fadeInUp" data-wow-delay="0.25s" style="{{ app()->getLocale() == 'ar' ? 'overflow: visible !important; border-radius: 10px !important;' : '' }}">
                                <h2 class="accordion-header" id="heading2" style="{{ app()->getLocale() == 'ar' ? 'overflow: visible !important;' : '' }}">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                        data-bs-target="#collapse2" aria-expanded="false" aria-controls="collapse2"
                                        style="{{ app()->getLocale() == 'ar' ? 'padding: 15px 90px 15px 50px !important; text-align: right; font-family: var(--arabic-font); overflow: visible !important; border-top-left-radius: 10px !important; border-top-right-radius: 10px !important;' : '' }}">
                                        {{ __('messages.diagnosis & imaging') }}
                                    </button>
                                    <div class="icon-box" style="{{ app()->getLocale() == 'ar' ? 'position: absolute !important; left: auto !important; right: 15px !important; top: 50% !important; transform: translateY(-50%) !important; background-color: var(--secondary-color) !important; border-radius: 50% !important; width: 43px !important; height: 43px !important; display: flex !important; align-items: center !important; justify-content: center !important; z-index: 10 !important; overflow: visible !important; margin: 0 !important;' : '' }}">
                                        <img src="{{ asset("assets/images/icon-how-it-work-2.svg") }}" alt="Diagnosis Icon" style="{{ app()->getLocale() == 'ar' ? 'max-width: 36px !important;' : '' }}">
                                    </div>
                                </h2>
                                <div id="collapse2" class="accordion-collapse collapse" aria-labelledby="heading2"
                                    data-bs-parent="#accordion">
                                    <div class="accordion-body" style="{{ app()->getLocale() == 'ar' ? 'border-bottom-left-radius: 10px !important; border-bottom-right-radius: 10px !important;' : '' }}">
                                        <p style="{{ app()->getLocale() == 'ar' ? 'text-align: right; font-family: var(--arabic-font);' : '' }}">{{ __('messages.Using advanced diagnostic technology, we\'ll identify the root cause of your condition. This may include X-rays, MRI, CT scans, or other specialized tests to provide a precise diagnosis.') }}</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Step 3: Treatment Plan -->
                            <div class="accordion-item wow fadeInUp" data-wow-delay="0.5s" style="{{ app()->getLocale() == 'ar' ? 'overflow: visible !important; border-radius: 10px !important;' : '' }}">
                                <h2 class="accordion-header" id="heading3" style="{{ app()->getLocale() == 'ar' ? 'overflow: visible !important;' : '' }}">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                        data-bs-target="#collapse3" aria-expanded="false" aria-controls="collapse3"
                                        style="{{ app()->getLocale() == 'ar' ? 'padding: 15px 90px 15px 50px !important; text-align: right; font-family: var(--arabic-font); overflow: visible !important; border-top-left-radius: 10px !important; border-top-right-radius: 10px !important;' : '' }}">
                                        {{ __('messages.treatment_plan') }}
                                    </button>
                                    <div class="icon-box" style="{{ app()->getLocale() == 'ar' ? 'position: absolute !important; left: auto !important; right: 15px !important; top: 50% !important; transform: translateY(-50%) !important; background-color: var(--secondary-color) !important; border-radius: 50% !important; width: 43px !important; height: 43px !important; display: flex !important; align-items: center !important; justify-content: center !important; z-index: 10 !important; overflow: visible !important; margin: 0 !important;' : '' }}">
                                        <img src="{{ asset("assets/images/icon-how-it-work-3.svg") }}" alt="Treatment Icon" style="{{ app()->getLocale() == 'ar' ? 'max-width: 36px !important;' : '' }}">
                                    </div>
                                </h2>
                                <div id="collapse3" class="accordion-collapse collapse" aria-labelledby="heading3"
                                    data-bs-parent="#accordion">
                                    <div class="accordion-body" style="{{ app()->getLocale() == 'ar' ? 'border-bottom-left-radius: 10px !important; border-bottom-right-radius: 10px !important;' : '' }}">
                                        <p style="{{ app()->getLocale() == 'ar' ? 'text-align: right; font-family: var(--arabic-font);' : '' }}">{{ __('messages.We\'ll create a customized treatment plan that may include surgical or non-surgical options, depending on your condition. Our team will explain all available options and help you make informed decisions about your care.') }}</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Step 4: Recovery & Rehabilitation -->
                            <div class="accordion-item wow fadeInUp" data-wow-delay="0.75s" style="{{ app()->getLocale() == 'ar' ? 'overflow: visible !important; border-radius: 10px !important;' : '' }}">
                                <h2 class="accordion-header" id="heading4" style="{{ app()->getLocale() == 'ar' ? 'overflow: visible !important;' : '' }}">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                        data-bs-target="#collapse4" aria-expanded="false" aria-controls="collapse4"
                                        style="{{ app()->getLocale() == 'ar' ? 'padding: 15px 90px 15px 50px !important; text-align: right; font-family: var(--arabic-font); overflow: visible !important; border-top-left-radius: 10px !important; border-top-right-radius: 10px !important;' : '' }}">
                                        {{ __('messages.recovery & rehabilitation') }}
                                    </button>
                                    <div class="icon-box" style="{{ app()->getLocale() == 'ar' ? 'position: absolute !important; left: auto !important; right: 15px !important; top: 50% !important; transform: translateY(-50%) !important; background-color: var(--secondary-color) !important; border-radius: 50% !important; width: 43px !important; height: 43px !important; display: flex !important; align-items: center !important; justify-content: center !important; z-index: 10 !important; overflow: visible !important; margin: 0 !important;' : '' }}">
                                        <img src="{{ asset("assets/images/icon-how-it-work-4.svg") }}" alt="Rehabilitation Icon" style="{{ app()->getLocale() == 'ar' ? 'max-width: 36px !important;' : '' }}">
                                    </div>
                                </h2>
                                <div id="collapse4" class="accordion-collapse collapse" aria-labelledby="heading4"
                                    data-bs-parent="#accordion">
                                    <div class="accordion-body" style="{{ app()->getLocale() == 'ar' ? 'border-bottom-left-radius: 10px !important; border-bottom-right-radius: 10px !important;' : '' }}">
                                        <p style="{{ app()->getLocale() == 'ar' ? 'text-align: right; font-family: var(--arabic-font);' : '' }}">{{ __('messages.Following treatment, our rehabilitation specialists will guide you through a comprehensive recovery program designed to restore function, improve mobility, and prevent future injuries.') }}</p>
                                    </div>
                                </div>
                            </div>
                            <!-- FAQ Accordion End -->
                        </div>
                        <!-- How Work Content End -->
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- How It Work Section End -->

@push('styles')
<style>
    /* RTL styles for work section */
    [dir="rtl"] .how-work-content .section-title h3,
    [dir="rtl"] .how-work-content .section-title h2 {
        text-align: right;
        font-family: var(--arabic-font);
    }

    /* Override accordion styles for RTL */
    [dir="rtl"] .how-work-accordion .accordion-header .accordion-button {
        text-align: right;
        font-family: var(--arabic-font);
        flex-direction: row-reverse;
        padding: 15px 80px 15px 50px !important; /* Adjust padding to accommodate icon on right */
        position: relative !important;
        overflow: visible !important;
    }

    /* Move dropdown arrow to left side */
    [dir="rtl"] .how-work-accordion .accordion-item .accordion-button::after,
    [dir="rtl"] .how-work-accordion .accordion-item .accordion-button.collapsed::after {
        right: auto !important;
        left: 20px !important;
        transform: translate(0, -50%) !important;
    }

    [dir="rtl"] .how-work-accordion .accordion-item .accordion-button.collapsed::after {
        transform: translate(0, -50%) rotate(-180deg) !important;
    }

    [dir="rtl"] .accordion-body p {
        text-align: right;
        font-family: var(--arabic-font);
    }

    /* Complete RTL override for icon-box */
    [dir="rtl"] .how-work-accordion .accordion-item .icon-box {
        position: absolute !important;
        left: auto !important;
        right: 15px !important;
        top: 50% !important;
        transform: translateY(-50%) !important;
        background-color: var(--secondary-color) !important;
        border-radius: 50% !important;
        width: 43px !important;
        height: 43px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        z-index: 10 !important;
        overflow: visible !important;
        margin: 0 !important;
    }

    /* Ensure accordion items have proper spacing and positioning */
    [dir="rtl"] .how-work-accordion .accordion-item {
        position: relative !important;
        margin-bottom: 20px !important;
        overflow: visible !important;
        border-radius: 10px !important;
    }

    /* Responsive adjustments */
    @media (max-width: 767px) {
        [dir="rtl"] .how-work-accordion .accordion-item .icon-box {
            width: 50px !important;
            height: 50px !important;
            right: 10px !important;
        }

        [dir="rtl"] .how-work-accordion .accordion-header .accordion-button {
            padding: 15px 70px 15px 40px !important;
        }

        [dir="rtl"] .how-work-accordion .accordion-item .icon-box img {
            max-width: 30px !important;
        }
    }

    /* Fix for Arabic title rendering */
    .arabic-title {
        font-family: var(--arabic-font) !important;
        letter-spacing: 0 !important;
        word-spacing: normal !important;
        text-align: right !important;
        animation: none !important;
        display: inline-block !important;
        white-space: nowrap !important;
        direction: rtl !important;
    }

    /* Ensure expanded state maintains correct styling */
    [dir="rtl"] .how-work-accordion .accordion-button:not(.collapsed) {
        padding: 15px 80px 15px 50px !important;
    }
</style>
@endpush














