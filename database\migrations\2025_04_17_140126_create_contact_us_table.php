<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('contact_us', function (Blueprint $table) {
            $table->id();

            // Contact Information
            $table->string('first_name');
            $table->string('last_name');
            $table->string('email')->nullable();
            $table->string('phone')->nullable();
            $table->text('message')->nullable();
            $table->string('subject')->nullable();
            $table->string('preferred_contact_method')->default('email');
            $table->string('status')->default('new'); // new, in_progress, completed, spam
            $table->boolean('is_urgent')->default(false);
            $table->text('admin_notes')->nullable();
            $table->timestamp('responded_at')->nullable();
            $table->string('ip_address')->nullable();
            $table->string('user_agent')->nullable();


            $table->text('email_reply_en')->nullable();
            $table->text('email_reply_ar')->nullable();
            $table->string('email_subject_en')->nullable();
            $table->string('email_subject_ar')->nullable();
            $table->timestamp('email_sent_at')->nullable();

            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('contact_us');
    }
};
