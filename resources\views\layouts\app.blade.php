<!DOCTYPE html>
<html lang="zxx">
<head>
	<!-- Meta -->
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta name="description" content="">
	<meta name="keywords" content="">
	<meta name="author" content="Awaiken">
	<!-- Page Title -->
    <title>@yield('title', __('meta.default_title'))</title>
	<!-- Favicon Icon -->
	<link rel="shortcut icon" type="image/x-icon" href="{{ asset("assets/images/favicon.png") }}">
	<!-- Google Fonts Css-->
	<link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Marcellus&family=Sora:wght@100..800&display=swap" rel="stylesheet">
	<!-- Bootstrap Css -->
	<link href="{{ asset("assets/css/bootstrap.min.css") }}" rel="stylesheet" media="screen">
	<!-- SlickNav Css -->
	<link href="{{ asset("assets/css/slicknav.min.css") }}" rel="stylesheet">
	<!-- Swiper Css -->
	<link rel="stylesheet" href="{{ asset("assets/css/swiper-bundle.min.css") }}">
	<!-- Font Awesome Icon Css-->
	<link href="{{ asset("assets/css/all.css") }}" rel="stylesheet" media="screen">
	<!-- Animated Css -->
	<link href="{{ asset("assets/css/animate.css") }}" rel="stylesheet">
	<!-- Magnific Popup Core Css File -->
	<link rel="stylesheet" href="{{ asset("assets/css/magnific-popup.css") }}">
	<!-- Mouse Cursor Css File -->
	<link rel="stylesheet" href="{{ asset("assets/css/mousecursor.css") }}">
	<!-- Main Custom Css -->
	<link href="{{ asset("assets/css/custom.css") }}" rel="stylesheet" media="screen">
	<!-- Scroll Top Css -->
	<link href="{{ asset("assets/css/scrollTop.css") }}" rel="stylesheet" media="screen">
	<!-- WhatsApp Css -->
	<link href="{{ asset("assets/css/whatsApp.css") }}" rel="stylesheet" media="screen">
    <!-- Map Location Css -->
	<link href="{{ asset("assets/css/mapLocation.css") }}" rel="stylesheet" media="screen">
	<!-- Add in head section, after your other CSS files -->
	<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fancyapps/ui@5.0/dist/fancybox/fancybox.css"/>
	@php
		$currentPath = str_replace(app()->getLocale(), '', request()->path());
		$currentPath = ltrim($currentPath, '/');
	@endphp
    <!-- For SEO - Language Alternates -->
    <link rel="canonical" href="{{ url(app()->getLocale() . '/' . $currentPath) }}" />
    <link rel="alternate" hreflang="x-default" href="{{ url('en/' . $currentPath) }}" />

	<!-- Alternate Language Links -->
	<link rel="alternate" hreflang="en" href="{{ url('en/' . $currentPath) }}" />
	<link rel="alternate" hreflang="ar" href="{{ url('ar/' . $currentPath) }}" />

    <!-- For Arabic regions specifically -->
    <link rel="alternate" hreflang="ar-BH" href="{{ url('ar/' . $currentPath) }}" />
    <link rel="alternate" hreflang="ar-SA" href="{{ url('ar/' . $currentPath) }}" />
    <link rel="alternate" hreflang="ar-AE" href="{{ url('ar/' . $currentPath) }}" />
    <link rel="alternate" hreflang="ar-KW" href="{{ url('ar/' . $currentPath) }}" />
    <link rel="alternate" hreflang="ar-OM" href="{{ url('ar/' . $currentPath) }}" />
    <link rel="alternate" hreflang="ar-QA" href="{{ url('ar/' . $currentPath) }}" />

	<!-- Language Meta Tags -->
	<meta property="og:locale" content="{{ app()->getLocale() }}" />
	<meta property="og:locale:alternate" content="{{ app()->getLocale() === 'en' ? 'ar' : 'en' }}" />

	<!-- Direction Meta -->
	<meta name="direction" content="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}" />

	<!-- Additional SEO Meta Tags -->
	<meta name="language" content="{{ app()->getLocale() }}" />
	<meta property="og:title" content="@yield('title', __('meta.default_title'))" />
	<meta property="og:description" content="@yield('meta_description', __('meta.default_description'))" />
	<meta property="og:url" content="{{ url(app()->getLocale() . '/' . $currentPath) }}" />

	<!-- Add this before closing </head> tag -->
	<script type="application/ld+json">
        {
        "@context": "https://schema.org",
        "@type": "MedicalBusiness",
        "name": "{{ __('meta.default_title') }}",
        "description": "{{ __('meta.default_description') }}",
        "address": {
            "@type": "PostalAddress",
            "addressLocality": "Tubli",
            "addressRegion": "Bahrain",
            "streetAddress": "{{ __('messages.Address') }}",
            "addressCountry": "BH"
        },
        "geo": {
            "@type": "GeoCoordinates",
            "latitude": "26.210336",
            "longitude": "50.559072"
        },
        "telephone": "+***********",
        "email": "<EMAIL>",
        "url": "{{ url('/') }}",
        "sameAs": [
            "https://www.facebook.com/profile.php?id=100063836869090",
            "https://www.instagram.com/alsharafcenter/",
            "https://www.youtube.com/@alsharafcenter6864"
        ],
        "openingHoursSpecification": [
            {
            "@type": "OpeningHoursSpecification",
            "dayOfWeek": ["Monday", "Tuesday", "Wednesday", "Thursday"],
            "opens": "09:00",
            "closes": "18:00"
            },
            {
            "@type": "OpeningHoursSpecification",
            "dayOfWeek": ["Saturday", "Sunday"],
            "opens": "09:00",
            "closes": "18:00"
            }
        ],
        "medicalSpecialty": [
            "Orthopedics",
            "Spine Surgery",
            "Physical Therapy"
        ],
        "image": [
            "{{ url('/assets/images/logo.png') }}"
        ],
        "priceRange": "$$",
        "@id": "{{ url('/') }}/#organization"
        }
    </script>
	{{-- <script src="https://cdn.tiny.cloud/1/{{ config('services.tinymce.api_key') }}/tinymce/6/tinymce.min.js" referrerpolicy="origin"></script> --}}
	<meta name="csrf-token" content="{{ csrf_token() }}">
	@stack('scripts')
	<link href="{{ asset('css/tiptap-content.css') }}" rel="stylesheet">
</head>
<body>
    @include('components.preloader')  <!-- Preloader Section -->

    @include('components.topbar') <!-- Topbar Section -->

    @include('components.header')  <!-- Header Section -->

    <main>
        @yield('content')  <!-- Page-specific content will go here -->
    </main>

    @include('components.footer')  <!-- Footer Section -->

    @include('components.scrolltop')    <!-- Scroll Top Section -->

    @include('components.map_location')    <!-- Map Location Section -->

    @include('components.whatsApp')    <!-- WhatsApp Section -->


    @stack('scripts')

    <!-- Add before closing body tag -->
    <script src="https://cdn.jsdelivr.net/npm/@fancyapps/ui@5.0/dist/fancybox/fancybox.umd.js"></script>
</body>
</html>





















