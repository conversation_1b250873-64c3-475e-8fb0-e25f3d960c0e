<?php

namespace Database\Seeders;

use App\Models\HeroSection;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class HeroSectionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $heroSections = [
            [
                'page_identifier' => 'home',
                'page_name' => 'Home Page',
                'title_en' => 'Welcome to Al Sharaf Orthopedic Center',
                'title_ar' => 'مرحباً بكم في مركز الشرف للعظام',
                'description_en' => '<p>Specialized in orthopedic and spine care with state-of-the-art facilities and expert medical staff</p>',
                'description_ar' => '<p>متخصصون في رعاية العظام والعمود الفقري مع مرافق حديثة وطاقم طبي خبير</p>',
                'background_type' => 'slider',
                'background_images' => ['hero-home-1.jpg', 'hero-home-2.jpg', 'hero-home-3.jpg'],
                'background_videos' => ['hero-home-video.mp4'],
                'slider_interval' => 5000,
                'slider_autoplay' => true,
                'button1_text_en' => 'Our Services',
                'button1_text_ar' => 'خدماتنا',
                'button1_url' => '/services',
                'button2_text_en' => 'Book Appointment',
                'button2_text_ar' => 'احجز موعد',
                'button2_url' => '/appointments',
                'is_active' => true,
            ],
            [
                'page_identifier' => 'about',
                'page_name' => 'About Us',
                'title_en' => 'About Al Sharaf Center',
                'title_ar' => 'عن مركز الشرف',
                'description_en' => '<p>Leading healthcare provider in Bahrain specializing in orthopedic and spine care</p>',
                'description_ar' => '<p>مزود رعاية صحية رائد في البحرين متخصص في رعاية العظام والعمود الفقري</p>',
                'background_type' => 'slider',
                'background_images' => ['hero-about-1.jpg', 'hero-about-2.jpg'],
                'background_videos' => [],
                'slider_interval' => 4000,
                'slider_autoplay' => true,
                'button1_text_en' => 'Our Team',
                'button1_text_ar' => 'فريقنا',
                'button1_url' => '/staff',
                'button2_text_en' => 'Contact Us',
                'button2_text_ar' => 'اتصل بنا',
                'button2_url' => '/contact',
                'is_active' => true,
            ],
            [
                'page_identifier' => 'services',
                'page_name' => 'Our Services',
                'title_en' => 'Comprehensive Medical Services',
                'title_ar' => 'خدمات طبية شاملة',
                'description_en' => '<p>From diagnostics to treatment, we provide complete orthopedic care under one roof</p>',
                'description_ar' => '<p>من التشخيص إلى العلاج، نقدم رعاية عظام كاملة تحت سقف واحد</p>',
                'background_type' => 'slider',
                'background_images' => ['hero-services-1.jpg', 'hero-services-2.jpg'],
                'background_videos' => ['hero-services-video.mp4'],
                'slider_interval' => 5000,
                'slider_autoplay' => true,
                'button1_text_en' => 'View Services',
                'button1_text_ar' => 'عرض الخدمات',
                'button1_url' => '/services#list',
                'is_active' => true,
            ],
            [
                'page_identifier' => 'gallery',
                'page_name' => 'Gallery',
                'title_en' => 'Our Facility Gallery',
                'title_ar' => 'معرض مرافقنا',
                'description_en' => '<p>Explore our modern facilities and advanced medical equipment</p>',
                'description_ar' => '<p>استكشف مرافقنا الحديثة ومعداتنا الطبية المتطورة</p>',
                'background_type' => 'youtube',
                'youtube_url' => 'https://www.youtube.com/watch?v=example',
                'background_images' => [],
                'background_videos' => [],
                'slider_interval' => 5000,
                'slider_autoplay' => true,
                'button1_text_en' => 'View Gallery',
                'button1_text_ar' => 'عرض المعرض',
                'button1_url' => '/gallery#photos',
                'is_active' => true,
            ],
            [
                'page_identifier' => 'contact',
                'page_name' => 'Contact Us',
                'title_en' => 'Get in Touch',
                'title_ar' => 'تواصل معنا',
                'description_en' => '<p>We are here to help. Contact us for appointments and inquiries</p>',
                'description_ar' => '<p>نحن هنا للمساعدة. اتصل بنا للمواعيد والاستفسارات</p>',
                'background_type' => 'slider',
                'background_images' => ['hero-contact-1.jpg', 'hero-contact-2.jpg'],
                'background_videos' => [],
                'slider_interval' => 5000,
                'slider_autoplay' => true,
                'button1_text_en' => 'Our Location',
                'button1_text_ar' => 'موقعنا',
                'button1_url' => '/contact#map',
                'button2_text_en' => 'Book Appointment',
                'button2_text_ar' => 'احجز موعد',
                'button2_url' => '/appointments',
                'is_active' => true,
            ],
        ];

        foreach ($heroSections as $heroSection) {
            HeroSection::create($heroSection);
        }
    }
}

