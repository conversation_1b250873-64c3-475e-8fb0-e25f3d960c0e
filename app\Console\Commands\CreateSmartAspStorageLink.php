<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

// class CreateSmartAspStorageLink extends Command
// {
//     protected $signature = 'storage:link-smarterasp';
//     protected $description = 'Create storage link for SmarterASP hosting';

//     public function handle()
//     {
//         $sourcePath = storage_path('app/public');
//         $targetPath = dirname(base_path()) . '/storage';

//         try {
//             // Ensure source directory exists
//             if (!File::exists($sourcePath)) {
//                 File::makeDirectory($sourcePath, 0755, true);
//             }

//             // Create or clear target directory
//             if (File::exists($targetPath)) {
//                 File::deleteDirectory($targetPath);
//             }
//             File::makeDirectory($targetPath, 0755, true);

//             // Copy all files from source to target
//             $this->copyDirectory($sourcePath, $targetPath);

//             // Create a marker file to indicate this is a managed directory
//             file_put_contents($targetPath . '/.storage_link', 'Managed by Laravel Storage Link');

//             $this->info('Storage directory has been linked successfully.');

//             // Output paths for verification
//             $this->info('Source: ' . $sourcePath);
//             $this->info('Target: ' . $targetPath);

//         } catch (\Exception $e) {
//             $this->error('Failed to create storage link: ' . $e->getMessage());
//             $this->error('Source: ' . $sourcePath);
//             $this->error('Target: ' . $targetPath);
//         }
//     }

//     protected function copyDirectory($source, $destination)
//     {
//         if (!File::exists($destination)) {
//             File::makeDirectory($destination, 0755, true);
//         }

//         $items = File::allFiles($source);

//         foreach ($items as $item) {
//             $targetPath = $destination . '/' . $item->getRelativePathname();
//             $targetDir = dirname($targetPath);

//             if (!File::exists($targetDir)) {
//                 File::makeDirectory($targetDir, 0755, true);
//             }

//             File::copy($item->getRealPath(), $targetPath);
//         }
//     }
// }
