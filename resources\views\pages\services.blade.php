@extends('layouts.app')

@push('styles')
<style>
    /* Custom RTL breadcrumb styling with stronger color rules */
    .rtl-breadcrumb {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        padding: 0;
        margin: 0;
        list-style: none;
        font-family: var(--arabic-font);
    }

    /* Force white color for all elements in RTL breadcrumb */
    .rtl-breadcrumb a,
    .rtl-breadcrumb span,
    .custom-rtl-breadcrumb a,
    .custom-rtl-breadcrumb span {
        color: rgba(255, 255, 255, 0.7) !important;
        text-decoration: none !important;
    }

    /* Override any hover effects */
    .rtl-breadcrumb a:hover,
    .custom-rtl-breadcrumb a:hover {
        color: #fff !important;
    }

    /* Make active item full white */
    .rtl-breadcrumb span:first-child {
        color: #fff !important;
    }

    .rtl-breadcrumb .separator {
        margin: 0 0.5rem;
        color: rgba(255, 255, 255, 0.7) !important;
    }

    /* Fix for Arabic title rendering */
    .arabic-title {
        font-family: var(--arabic-font) !important;
        letter-spacing: 0 !important;
        word-spacing: normal !important;
        text-align: right !important;
        animation: none !important;
        display: inline-block !important;
        white-space: nowrap !important;
        direction: rtl !important;
    }

    /* RTL specific styles for service process section */
    [dir="rtl"] .service-process-list-item {
        flex-direction: row-reverse;
    }

    [dir="rtl"] .service-process-list-item .icon-box {
        margin-right: 0;
        margin-left: 20px;
    }

    [dir="rtl"] .service-process-list-item .service-process-content-body {
        flex-direction: row-reverse;
    }

    [dir="rtl"] .service-process-list-item .service-process-content-body .service-process-btn {
        margin-left: 0;
        margin-right: 20px;
    }

    [dir="rtl"] .service-process-box-content {
        text-align: right;
    }

    [dir="rtl"] .service-process-box-content .section-btn {
        text-align: right;
    }

    .text-right {
        text-align: right !important;
    }

    .arabic-content {
        font-family: var(--arabic-font) !important;
        letter-spacing: 0 !important;
        direction: rtl !important;
    }

    /* RTL styles for rehabilitation section */
    [dir="rtl"] .section-title h3,
    [dir="rtl"] .section-title h2,
    [dir="rtl"] .rehab-benefits-content h3 {
        text-align: right;
        font-family: var(--arabic-font);
    }

    [dir="rtl"] .section-btn {
        text-align: left;
    }

    /* Fix for Arabic text rendering */
    [dir="rtl"] .text-anime-style-3 {
        font-family: var(--arabic-font) !important;
        letter-spacing: normal !important;
        word-spacing: normal !important;
        text-align: right !important;
    }

    /* Center icon box in both LTR and RTL modes */
    .rehab-benefits-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
    }
</style>
@endpush

@section('content')
<style>
    .service-item-wrapper {
        display: block;
        position: relative;
        /* overflow: hidden; */
        border-radius: 10px;
    }

    .service-item-wrapper .view-more-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(
            to bottom,
            rgba(0, 0, 0, 0.2),
            rgba(0, 0, 0, 0.5)
        );
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        opacity: 0;
        transition: all 0.4s ease;
        z-index: 2; /* Reduced z-index to be below the icon */
        pointer-events: none; /* This allows clicks to pass through to elements below */
    }

    .service-item-wrapper:hover .view-more-overlay {
        opacity: 1;
    }

    /* Service title styling */
    .service-item-wrapper h3 {
        color: #fff;
        margin-bottom: 10px;
        transform: translateY(-20px);
        transition: all 0.4s ease;
    }

    .service-item-wrapper p {
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 20px;
        transform: translateY(-20px);
        transition: all 0.4s ease;
    }

    .service-item-wrapper:hover h3,
    .service-item-wrapper:hover p {
        transform: translateY(0);
    }

    .view-more-btn {
        padding: 12px 30px;
        background: rgba(255, 255, 255, 0.9);
        color: var(--primary-color);
        border-radius: 50px;
        font-weight: 600;
        font-size: 16px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        transform: translateY(20px);
        opacity: 0;
        transition: all 0.4s ease;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        backdrop-filter: blur(5px);
        pointer-events: auto; /* Re-enable pointer events for the button */
    }

    .service-item-wrapper:hover .view-more-btn {
        transform: translateY(0);
        opacity: 1;
    }

    .view-more-btn:hover {
        background: var(--primary-color);
        color: #fff;
        transform: translateY(-3px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
    }

    .view-more-btn svg {
        width: 18px;
        height: 18px;
        transition: transform 0.3s ease;
    }

    .view-more-btn:hover svg {
        transform: translateX(5px);
        stroke: #fff;
    }

    /* Ensure icon box stays on top */
    .service-item .icon-box {
        z-index: 3; /* Increased z-index to be above overlay */
    }

    @media (max-width: 767px) {
        .view-more-btn {
            padding: 10px 20px;
            font-size: 14px;
        }

        .view-more-btn svg {
            width: 16px;
            height: 16px;
        }
    }
    .view-more-btn {
        padding: 12px 30px;
        background: var(--white-color);
        color: var(--primary-color);
        border-radius: 50px;
        font-weight: 600;
        font-size: 16px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        transform: translateY(20px);
        opacity: 0;
        transition: all 0.4s ease;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        position: relative;
        overflow: hidden;
        pointer-events: auto;
        z-index: 2;
    }

    .view-more-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: var(--primary-color);
        transition: all 0.4s ease;
        z-index: -1;
    }

    .service-item-wrapper:hover .view-more-btn {
        transform: translateY(0);
        opacity: 1;
    }

    .view-more-btn:hover {
        color: var(--white-color);
    }

    .view-more-btn:hover::before {
        left: 0;
    }

    .view-more-btn svg {
        width: 18px;
        height: 18px;
        transition: transform 0.3s ease;
    }

    .view-more-btn:hover svg {
        transform: translateX(5px);
        stroke: var(--white-color);
    }

    @media (max-width: 767px) {
        .view-more-btn {
            padding: 10px 20px;
            font-size: 14px;
        }

        .view-more-btn svg {
            width: 16px;
            height: 16px;
        }
    }
</style>
    <!-- Page Header Start -->
	<div class="page-header service-page-header bg-radius-section parallaxie" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
		<div class="container">
			<div class="row align-items-center">
				<div class="col-lg-12">
					<!-- Page Header Box Start -->
					<div class="page-header-box">
						<h1 class="{{ app()->getLocale() === 'ar' ? 'arabic-title' : 'text-anime-style-2' }}" data-cursor="-opaque">{{ __('messages.services_list') }}</h1>
						<!-- Custom breadcrumb for RTL/LTR support -->
						@if(app()->getLocale() == 'ar')
							<!-- Arabic RTL breadcrumb -->
							<nav class="wow fadeInUp custom-rtl-breadcrumb">
								<div class="rtl-breadcrumb">
									<a href="{{ url(app()->getLocale()) }}" style="color: rgba(255, 255, 255, 0.7) !important; text-decoration: none !important; font-size: 16px;">{{ __('messages.Home') }}</a>
									<span class="separator" style="color: rgba(255, 255, 255, 0.7) !important; font-size: 16px;">/</span>
									<span style="color: #fff !important; font-size: 16px;">{{ __('messages.services') }}</span>
								</div>
							</nav>
						@else
							<!-- English LTR breadcrumb -->
							<nav class="wow fadeInUp">
								<ol class="breadcrumb">
									<li class="breadcrumb-item"><a href="{{ url(app()->getLocale()) }}">{{ __('messages.Home') }}</a></li>
									<li class="breadcrumb-item active" aria-current="page">{{ __('messages.services') }}</li>
								</ol>
							</nav>
						@endif
					</div>
					<!-- Page Header Box End -->
				</div>
			</div>
		</div>
	</div>
	<!-- Page Header End -->

    <!-- Service Process Section Start -->
    <div class="service-process bg-radius-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-4 col-md-6 order-lg-1 order-md-2 order-3">
                    <!-- Service Process List Start -->
                    <div class="service-process-list" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
                        <!-- Service Process List Item Start -->
                        <div class="service-process-list-item wow fadeInUp">
                            <!-- Icon Box Start -->
                            <div class="icon-box">
                                <img src="{{ asset("assets/images/icon-service-list-dot.svg") }}" alt="">
                            </div>
                            <!-- Icon Box End -->

                            <!-- Service Process Content Start -->
                            <div class="service-process-content">
                                <div class="service-process-content-body">
                                    <div class="service-process-title">
                                        <h3 class="{{ app()->getLocale() == 'ar' ? 'arabic-content' : '' }}">{{ __('messages.initial_contact') }}</h3>
                                    </div>
                                    <div class="service-process-btn">
                                        <a href="#"><img src="{{ asset("assets/images/arrow-long-white.svg") }}" alt=""></a>
                                    </div>
                                </div>

                                <div class="service-process-footer">
                                    <p class="{{ app()->getLocale() == 'ar' ? 'arabic-content' : '' }}">{{ __('messages.initial_contact_desc') }}</p>
                                </div>
                            </div>
                            <!-- Service Process Content End -->
                        </div>
                        <!-- Service Process List Item End -->

                        <!-- Service Process List Item Start -->
                        <div class="service-process-list-item wow fadeInUp" data-wow-delay="0.25s">
                            <!-- Icon Box Start -->
                            <div class="icon-box">
                                <img src="{{ asset("assets/images/icon-service-list-dot.svg") }}" alt="">
                            </div>
                            <!-- Icon Box End -->

                            <!-- Service Process Content Start -->
                            <div class="service-process-content">
                                <div class="service-process-content-body">
                                    <div class="service-process-title">
                                        <h3 class="{{ app()->getLocale() == 'ar' ? 'arabic-content' : '' }}">{{ __('messages.treatment_planning') }}</h3>
                                    </div>
                                    <div class="service-process-btn">
                                        <a href="#"><img src="{{ asset("assets/images/arrow-long-white.svg") }}" alt=""></a>
                                    </div>
                                </div>

                                <div class="service-process-footer">
                                    <p class="{{ app()->getLocale() == 'ar' ? 'arabic-content' : '' }}">{{ __('messages.treatment_planning_desc') }}</p>
                                </div>
                            </div>
                            <!-- Service Process Content End -->
                        </div>
                        <!-- Service Process List Item End -->

                        <!-- Service Process List Item Start -->
                        <div class="service-process-list-item wow fadeInUp" data-wow-delay="0.5s">
                            <!-- Icon Box Start -->
                            <div class="icon-box">
                                <img src="{{ asset("assets/images/icon-service-list-dot.svg") }}" alt="">
                            </div>
                            <!-- Icon Box End -->

                            <!-- Service Process Content Start -->
                            <div class="service-process-content">
                                <div class="service-process-content-body">
                                    <div class="service-process-title">
                                        <h3 class="{{ app()->getLocale() == 'ar' ? 'arabic-content' : '' }}">{{ __('messages.discharge_maintenance') }}</h3>
                                    </div>
                                    <div class="service-process-btn">
                                        <a href="#"><img src="{{ asset("assets/images/arrow-long-white.svg") }}" alt=""></a>
                                    </div>
                                </div>

                                <div class="service-process-footer">
                                    <p class="{{ app()->getLocale() == 'ar' ? 'arabic-content' : '' }}">{{ __('messages.discharge_maintenance_desc') }}</p>
                                </div>
                            </div>
                            <!-- Service Process Content End -->
                        </div>
                        <!-- Service Process List Item End -->
                    </div>
                    <!-- Service Process List End -->
                </div>

                <div class="col-lg-4 order-lg-2 order-md-3 order-2">
                    <!-- Service Process Image Start -->
                    <div class="service-process-img">
                        <figure class="image-anime reveal">
                            <img src="{{ asset("assets/images/service-process-image.jpg") }}" alt="">
                        </figure>
                    </div>
                    <!-- Service Process Image End -->
                </div>

                <div class="col-lg-4 col-md-6 order-lg-3 order-md-1 order-1">
                    <!-- Service Process Box Content Start -->
                    <div class="service-process-box-content" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
                        <!-- Section Title Start -->
                        <div class="section-title {{ app()->getLocale() == 'ar' ? 'text-right' : '' }}">
                            <h3 class="wow fadeInUp {{ app()->getLocale() == 'ar' ? 'arabic-content' : '' }}">{{ __('messages.process') }}</h3>
                            <h2 class="{{ app()->getLocale() == 'ar' ? 'arabic-title' : 'text-anime-style-3' }}" data-cursor="-opaque">{{ __('messages.complete_service_process') }}</h2>
                            <p class="wow fadeInUp {{ app()->getLocale() == 'ar' ? 'arabic-content' : '' }}" data-wow-delay="0.25s">{{ __('messages.service_process_desc') }}</p>
                        </div>
                        <!-- Section Title End -->

                        <!-- Section Btn Start -->
                        <div class="section-btn wow fadeInUp" data-wow-delay="0.5s">
                            <a href="#" class="btn-default"><span class="{{ app()->getLocale() == 'ar' ? 'arabic-content' : '' }}">{{ __('messages.make_appointment') }}</span></a>
                        </div>
                        <!-- Section Btn End -->
                    </div>
                    <!-- Service Process Box Content End -->
                </div>
            </div>
        </div>
    </div>
    <!-- Service Process Section End -->

    <!-- Page Services Section Start -->
    <div class="page-services bg-radius-section">
        <div class="container">
            <div class="row section-row align-items-center">
                <div class="col-lg-12">
                    <!-- Section Title Start -->
                    <div class="section-title">
                        <h3 class="wow fadeInUp">rehabilitation</h3>
                        <h2 class="text-anime-style-3" data-cursor="-opaque">Services</h2>
                    </div>
                    <!-- Section Title End -->
                </div>
            </div>

            <div class="row">
                {{-- <div class="col-lg-4 col-md-6">
                    <!-- Service Item Start -->
                    <div class="service-item wow fadeInUp">
                        <!-- Service Item Image Start -->
                        <div class="service-item-image">
                            <figure>
                                <img src="{{ asset("assets/images/service-img-1.jpg") }}" alt="Manual Therapy">
                            </figure>
                        </div>
                        <!-- Service Item Image End -->

                        <!-- Service Item Icon Start -->
                        <div class="icon-box">
                            <img src="{{ asset("assets/images/icon-service-1.svg") }}" alt="Manual Therapy Icon">
                        </div>
                        <!-- Service Item Icon ENd -->

                        <!-- Service Body Start -->
                        <div class="service-body">
                            <!-- Service Content Start -->
                            <div class="service-content">
                                <h3>joint replacement</h3>
                                <p>Advanced surgical procedures for knee, hip, and other joint replacements.</p>
                            </div>
                            <!-- Service Content End -->

                             <!-- Service Btn Start -->
                            <div class="service-btn">
                                <a href="#"><img src="{{ asset("assets/images/arrow-readmore-btn.svg") }}" alt="Read More"></a>
                            </div>
                            <!-- Service Btn End -->
                        </div>
                        <!-- Service Body End -->
                    </div>
                    <!-- Service Item End -->
                </div>

                <div class="col-lg-4 col-md-6">
                    <!-- Service Item Start -->
                    <div class="service-item wow fadeInUp" data-wow-delay="0.25s">
                        <!-- Service Item Image Start -->
                        <div class="service-item-image">
                            <figure>
                                <img src="{{ asset("assets/images/service-img-2.jpg") }}" alt="Chronic Pain">
                            </figure>
                        </div>
                        <!-- Service Item Image End -->

                        <!-- Service Item Icon Start -->
                        <div class="icon-box">
                            <img src="{{ asset("assets/images/icon-service-2.svg") }}" alt="Chronic Pain Icon">
                        </div>
                        <!-- Service Item Icon ENd -->

                        <!-- Service Body Start -->
                        <div class="service-body">
                            <!-- Service Content Start -->
                            <div class="service-content">
                                <h3>spine care</h3>
                                <p>Specialized treatment for various spine conditions and disorders.</p>
                            </div>
                            <!-- Service Content End -->

                             <!-- Service Btn Start -->
                            <div class="service-btn">
                                <a href="#"><img src="{{ asset("assets/images/arrow-readmore-btn.svg") }}" alt="Read More"></a>
                            </div>
                            <!-- Service Btn End -->
                        </div>
                        <!-- Service Body End -->
                    </div>
                    <!-- Service Item End -->
                </div>

                <div class="col-lg-4 col-md-6">
                    <!-- Service Item Start -->
                    <div class="service-item wow fadeInUp" data-wow-delay="0.5s">
                        <!-- Service Item Image Start -->
                        <div class="service-item-image">
                            <figure>
                                <img src="{{ asset("assets/images/service-img-3.jpg") }}" alt="Hand Therapy">
                            </figure>
                        </div>
                        <!-- Service Item Image End -->

                        <!-- Service Item Icon Start -->
                        <div class="icon-box">
                            <img src="{{ asset("assets/images/icon-service-3.svg") }}" alt="Hand Therapy Icon">
                        </div>
                        <!-- Service Item Icon ENd -->

                        <!-- Service Body Start -->
                        <div class="service-body">
                            <!-- Service Content Start -->
                            <div class="service-content">
                                <h3>sports medicine</h3>
                                <p>Treatment for sports-related injuries and performance optimization.</p>
                            </div>
                            <!-- Service Content End -->

                             <!-- Service Btn Start -->
                            <div class="service-btn">
                                <a href="#"><img src="{{ asset("assets/images/arrow-readmore-btn.svg") }}" alt="Read More"></a>
                            </div>
                            <!-- Service Btn End -->
                        </div>
                        <!-- Service Body End -->
                    </div>
                    <!-- Service Item End -->
                </div>

                <div class="col-lg-4 col-md-6">
                    <!-- Service Item Start -->
                    <div class="service-item wow fadeInUp" data-wow-delay="0.75s">
                        <!-- Service Item Image Start -->
                        <div class="service-item-image">
                            <figure>
                                <img src="{{ asset("assets/images/service-img-4.jpg") }}" alt="Sports Therapy">
                            </figure>
                        </div>
                        <!-- Service Item Image End -->

                        <!-- Service Item Icon Start -->
                        <div class="icon-box">
                            <img src="{{ asset("assets/images/icon-service-4.svg") }}" alt="Sports Therapy Icon">
                        </div>
                        <!-- Service Item Icon ENd -->

                        <!-- Service Body Start -->
                        <div class="service-body">
                            <!-- Service Content Start -->
                            <div class="service-content">
                                <h3>sports medicine</h3>
                                <p>Treatment for sports-related injuries and performance optimization.</p>
                            </div>
                            <!-- Service Content End -->

                             <!-- Service Btn Start -->
                            <div class="service-btn">
                                <a href="#"><img src="{{ asset("assets/images/arrow-readmore-btn.svg") }}" alt="Read More"></a>
                            </div>
                            <!-- Service Btn End -->
                        </div>
                        <!-- Service Body End -->
                    </div>
                    <!-- Service Item End -->
                </div>

                <div class="col-lg-4 col-md-6">
                    <!-- Service Item Start -->
                    <div class="service-item wow fadeInUp" data-wow-delay="1s">
                        <!-- Service Item Image Start -->
                        <div class="service-item-image">
                            <figure>
                                <img src="{{ asset("assets/images/service-img-5.jpg") }}" alt="Cupping Therapy">
                            </figure>
                        </div>
                        <!-- Service Item Image End -->

                        <!-- Service Item Icon Start -->
                        <div class="icon-box">
                            <img src="{{ asset("assets/images/icon-service-5.svg") }}" alt="Cupping Therapy Icon">
                        </div>
                        <!-- Service Item Icon ENd -->

                        <!-- Service Body Start -->
                        <div class="service-body">
                            <!-- Service Content Start -->
                            <div class="service-content">
                                <h3>arthroscopy</h3>
                                <p>Minimally invasive procedures for joint diagnosis and treatment.</p>
                            </div>
                            <!-- Service Content End -->

                             <!-- Service Btn Start -->
                            <div class="service-btn">
                                <a href="#"><img src="{{ asset("assets/images/arrow-readmore-btn.svg") }}" alt="Read More"></a>
                            </div>
                            <!-- Service Btn End -->
                        </div>
                        <!-- Service Body End -->
                    </div>
                    <!-- Service Item End -->
                </div>

                <div class="col-lg-4 col-md-6">
                    <!-- Service Item Start -->
                    <div class="service-item wow fadeInUp" data-wow-delay="1.25s">
                        <!-- Service Item Image Start -->
                        <div class="service-item-image">
                            <figure>
                                <img src="{{ asset("assets/images/service-img-6.jpg") }}" alt="Laser Therapy">
                            </figure>
                        </div>
                        <!-- Service Item Image End -->

                        <!-- Service Item Icon Start -->
                        <div class="icon-box">
                            <img src="{{ asset("assets/images/icon-service-6.svg") }}" alt="Laser Therapy Icon">
                        </div>
                        <!-- Service Item Icon ENd -->

                        <!-- Service Body Start -->
                        <div class="service-body">
                            <!-- Service Content Start -->
                            <div class="service-content">
                                <h3>trauma care</h3>
                                <p>Expert treatment for fractures and orthopedic injuries.</p>
                            </div>
                            <!-- Service Content End -->

                             <!-- Service Btn Start -->
                            <div class="service-btn">
                                <a href="#"><img src="{{ asset("assets/images/arrow-readmore-btn.svg") }}" alt="Read More"></a>
                            </div>
                            <!-- Service Btn End -->
                        </div>
                        <!-- Service Body End -->
                    </div>
                    <!-- Service Item End -->
                </div> --}}
                @foreach($services->skip(1) as $service)
                    <div class="col-lg-4 col-md-6">
                        <!-- Service Item Start -->
                        <a href="{{ route('service.show', ['locale' => app()->getLocale(), 'slug' => $service->slug]) }}" class="service-item-wrapper">
                            <div class="service-item wow fadeInUp" @if(!$loop->first) data-wow-delay="{{ $loop->index * 0.25 }}s" @endif>
                                <!-- Service Item Image Start -->
                                <div class="service-item-image">
                                    <figure>
                                        <img src="{{ Storage::url($service->featured_image) }}" alt="{{ $service->{"title_" . app()->getLocale()} }}" loading="lazy" onerror="this.onerror=null; this.src='{{ asset('assets/images/service-img-1.jpg') }}'">
                                    </figure>
                                </div>
                                <!-- Service Item Image End -->

                                <!-- Service Item Icon Start -->
                                <div class="icon-box">
                                    <img src="{{ Storage::url($service->icon) }}" alt="{{ $service->{"title_" . app()->getLocale()} }} Icon">
                                </div>
                                <!-- Service Item Icon End -->

                                <!-- Service Body Start -->
                                <div class="service-body">
                                    <!-- Service Content Start -->
                                    <div class="service-content">
                                        <h3>{{ $service->{"title_" . app()->getLocale()} }}</h3>
                                        <p>{{ Str::words(strip_tags($service->{"description_" . app()->getLocale()}), 10, '...') }}</p>
                                    </div>
                                    <!-- Service Content End -->
                                </div>
                                <!-- Service Body End -->
                            </div>

                            <!-- View More Overlay -->
                            <div class="view-more-overlay">
                                <span class="view-more-btn">
                                    View More
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                                    </svg>
                                </span>
                            </div>
                        </a>
                        <!-- Service Item End -->
                    </div>
                @endforeach
            </div>
        </div>
    </div>
    <!-- Page Services Section End -->

    @include('components.testimonials') <!-- Testimonials Section -->

    <!-- Care Rehabilitation Section Start -->
    <div class="care-rehabilitation bg-radius-section" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
        <div class="container">
            <div class="row section-row align-items-center">
                <div class="col-lg-5">
                    <!-- Section Title Start -->
                    <div class="section-title">
                        <h3 class="wow fadeInUp">{{ __('messages.rehabilitation') }}</h3>
                        <h2 class="{{ app()->getLocale() === 'ar' ? 'arabic-title' : 'text-anime-style-3' }}" data-cursor="-opaque">{{ __('messages.excellence_in_care') }}</h2>
                    </div>
                    <!-- Section Title End -->
                </div>

                <div class="col-lg-7">
                    <!-- Section Btn Start -->
                    <div class="section-btn wow fadeInUp" data-wow-delay="0.25s">
                        <a href="#" class="btn-default"><span class="{{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ __('messages.make_appointment') }}</span></a>
                    </div>
                    <!-- Section Btn End -->
                </div>
            </div>

            <div class="row">
                <div class="col-lg-2 col-md-4 col-6">
                    <!-- Rehab Benefits Item Start -->
                    <div class="rehab-benefits-item wow fadeInUp">
                        <div class="icon-box">
                            <img src="{{ asset("assets/images/icon-rehab-benefits-1.svg") }}" alt="">
                        </div>
                        <div class="rehab-benefits-content">
                            <h3 class="{{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ __('messages.experienced_team') }}</h3>
                        </div>
                    </div>
                    <!-- Rehab Benefits Item End -->
                </div>

                <div class="col-lg-2 col-md-4 col-6">
                    <!-- Rehab Benefits Item Start -->
                    <div class="rehab-benefits-item wow fadeInUp" data-wow-delay="0.5s">
                        <div class="icon-box">
                            <img src="{{ asset("assets/images/icon-rehab-benefits-2.svg") }}" alt="">
                        </div>
                        <div class="rehab-benefits-content">
                            <h3 class="{{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ __('messages.advanced_technology') }}</h3>
                        </div>
                    </div>
                    <!-- Rehab Benefits Item End -->
                </div>

                <div class="col-lg-2 col-md-4 col-6">
                    <!-- Rehab Benefits Item Start -->
                    <div class="rehab-benefits-item wow fadeInUp" data-wow-delay="0.75s">
                        <div class="icon-box">
                            <img src="{{ asset("assets/images/icon-rehab-benefits-3.svg") }}" alt="">
                        </div>
                        <div class="rehab-benefits-content">
                            <h3 class="{{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ __('messages.personalized_treatment') }}</h3>
                        </div>
                    </div>
                    <!-- Rehab Benefits Item End -->
                </div>

                <div class="col-lg-2 col-md-4 col-6">
                    <!-- Rehab Benefits Item Start -->
                    <div class="rehab-benefits-item wow fadeInUp" data-wow-delay="1s">
                        <div class="icon-box">
                            <img src="{{ asset("assets/images/icon-rehab-benefits-4.svg") }}" alt="">
                        </div>
                        <div class="rehab-benefits-content">
                            <h3 class="{{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ __('messages.convenient_accessible') }}</h3>
                        </div>
                    </div>
                    <!-- Rehab Benefits Item End -->
                </div>

                <div class="col-lg-2 col-md-4 col-6">
                    <!-- Rehab Benefits Item Start -->
                    <div class="rehab-benefits-item wow fadeInUp" data-wow-delay="1.25s">
                        <div class="icon-box">
                            <img src="{{ asset("assets/images/icon-rehab-benefits-5.svg") }}" alt="">
                        </div>
                        <div class="rehab-benefits-content">
                            <h3 class="{{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ __('messages.expertise_experience') }}</h3>
                        </div>
                    </div>
                    <!-- Rehab Benefits Item End -->
                </div>

                <div class="col-lg-2 col-md-4 col-6">
                    <!-- Rehab Benefits Item Start -->
                    <div class="rehab-benefits-item wow fadeInUp" data-wow-delay="1.5s">
                        <div class="icon-box">
                            <img src="{{ asset("assets/images/icon-rehab-benefits-6.svg") }}" alt="">
                        </div>
                        <div class="rehab-benefits-content">
                            <h3 class="{{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ __('messages.community_involvement') }}</h3>
                        </div>
                    </div>
                    <!-- Rehab Benefits Item End -->
                </div>
            </div>
        </div>
    </div>
    <!-- Care Rehabilitation Section End -->
@endsection


















