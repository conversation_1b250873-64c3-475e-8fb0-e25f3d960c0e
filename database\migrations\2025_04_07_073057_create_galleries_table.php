<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('galleries', function (Blueprint $table) {
            $table->id();

            // Basic Information
            $table->string('title_en');
            $table->string('title_ar')->nullable();
            $table->text('description_en')->nullable();
            $table->text('description_ar')->nullable();

            // Media Type and Files
            $table->enum('type', ['image', 'video']);
            $table->string('media_url')->nullable(); // For video URLs (YouTube, Vimeo, etc.)
            $table->string('media_file')->nullable(); // For uploaded video files
            $table->string('image')->nullable(); // For uploaded images
            $table->string('thumbnail')->nullable(); // For video thumbnails

            // Organization
            $table->string('category')->nullable();
            $table->integer('display_order')->default(0);
            $table->boolean('is_active')->default(true);

            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('galleries');
    }
};

