@php
    $insurances = \App\Models\Insurance::where('is_active', true)
        ->orderBy('display_order')
        ->get();
@endphp

<style>
        /* Insurance Partners Styles - Modern Design */
    .insurance-partners {
        padding: 100px 0;
        margin-top: 50px;
        background: linear-gradient(180deg, rgba(var(--primary-color-rgb), 0.03) 0%, rgba(var(--primary-color-rgb), 0) 100%);
    }

    .insurance-slider-container {
        position: relative;
        padding: 0 60px; /* Add padding for the arrows */
    }

    .insurance-card {
        background: var(--white-color);
        border-radius: 20px;
        padding: 25px 20px 15px; /* Reduced bottom padding */
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
        height: 100%;
        transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        position: relative;
        overflow: hidden;
        border: 1px solid rgba(var(--primary-color-rgb), 0.08);
    }

    .insurance-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        border-color: rgba(var(--primary-color-rgb), 0.2);
    }

    .insurance-logo-wrapper {
        width: 100%;
        height: 120px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 25px;
        padding: 15px;
        border-radius: 15px;
        background: rgba(var(--primary-color-rgb), 0.03);
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .insurance-card:hover .insurance-logo-wrapper {
        background: rgba(var(--primary-color-rgb), 0.05);
    }

    .insurance-logo {
        max-width: 85%;
        max-height: 85%;
        object-fit: contain;
        transition: transform 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
        filter: grayscale(20%);
        opacity: 0.9;
    }

    .insurance-card:hover .insurance-logo {
        transform: scale(1.08);
        filter: grayscale(0%);
        opacity: 1;
    }

    .insurance-name {
        font-size: 18px;
        font-weight: 500;
        margin-bottom: 0; /* Remove bottom margin */
        color: var(--primary-color);
        transition: all 0.3s ease;
    }

    .insurance-card:hover .insurance-name {
        color: var(--accent-color);
    }

    .insurance-description {
        font-size: 14px;
        color: var(--text-color);
        line-height: 1.6;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
        margin-bottom: 0;
        transition: all 0.3s ease;
    }

    /* Overlay effect */
    .insurance-overlay {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 0;
        background: linear-gradient(to top, rgba(var(--primary-color-rgb), 0.9) 0%, rgba(var(--primary-color-rgb), 0.7) 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
        border-radius: 0 0 20px 20px;
    }

    .insurance-card:hover .insurance-overlay {
        height: 60px;
        opacity: 1;
    }

    .overlay-content {
        text-align: center;
        color: var(--white-color);
    }

    .learn-more {
        font-size: 14px;
        font-weight: 500;
        letter-spacing: 0.5px;
        position: relative;
    }

    .learn-more:after {
        content: '';
        position: absolute;
        bottom: -5px;
        left: 0;
        width: 0;
        height: 2px;
        background: var(--white-color);
        transition: width 0.3s ease;
    }

    .insurance-card:hover .learn-more:after {
        width: 100%;
    }

    /* Custom Navigation */
    .insurance-navigation {
        display: none; /* Hide the original navigation */
    }

    .insurance-button-prev,
    .insurance-button-next {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        width: 44px;
        height: 44px;
        background: var(--white-color);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        color: var(--primary-color);
        z-index: 10;
    }

    .insurance-button-prev {
        left: 0;
    }

    .insurance-button-next {
        right: 0;
    }

    .insurance-button-prev:hover,
    .insurance-button-next:hover {
        background: var(--primary-color);
        color: var(--white-color);
        transform: translateY(-50%) scale(1.1);
        box-shadow: 0 8px 20px rgba(var(--primary-color-rgb), 0.3);
    }

    .insurance-pagination {
        position: absolute;
        bottom: -30px;
        left: 0;
        right: 0;
        text-align: center;
    }

    .insurance-pagination .swiper-pagination-bullet {
        width: 10px;
        height: 10px;
        background: rgba(var(--primary-color-rgb), 0.3);
        opacity: 1;
        margin: 0 5px;
        transition: all 0.3s ease;
    }

    .insurance-pagination .swiper-pagination-bullet-active {
        background: var(--primary-color);
        width: 20px;
        border-radius: 5px;
    }

    /* Responsive adjustments */
    @media (max-width: 991px) {
        .insurance-partners {
            padding: 80px 0;
        }

        .insurance-card {
            padding: 25px 20px;
        }
    }

    @media (max-width: 767px) {
        .insurance-partners {
            padding: 60px 0;
            margin-top: 30px;
        }

        .insurance-logo-wrapper {
            height: 100px;
            margin-bottom: 20px;
        }

        .insurance-name {
            font-size: 16px;
            margin-bottom: 10px;
        }

        .insurance-description {
            font-size: 13px;
            -webkit-line-clamp: 2;
        }

        .insurance-card:hover .insurance-overlay {
            height: 50px;
        }

        .learn-more {
            font-size: 13px;
        }
    }

    @media (max-width: 480px) {
        .insurance-partners {
            padding: 50px 0;
        }

        .insurance-navigation {
            margin-top: 20px;
        }

        .insurance-button-prev,
        .insurance-button-next {
            width: 38px;
            height: 38px;
        }

        .insurance-pagination {
            margin: 0 15px;
        }
    }
</style>

@push('styles')
<style>
    /* RTL styles for insurance section */
    [dir="rtl"] .section-title h2,
    [dir="rtl"] .section-title .subtitle {
        text-align: right;
        font-family: var(--arabic-font);
    }

    /* Force right alignment for Arabic title */
    [dir="rtl"] .text-end .subtitle,
    [dir="rtl"] .text-end h2 {
        text-align: right !important;
        margin-right: 0;
        margin-left: auto;
    }

    /* Arabic title specific styling */
    [dir="rtl"] .arabic-title {
        display: inline-block;
        text-align: right !important;
        float: right;
        width: 100%;
    }

    /* Rest of your existing RTL styles... */
    [dir="rtl"] .insurance-name {
        text-align: right;
        font-family: var(--arabic-font);
    }

    [dir="rtl"] .insurance-description {
        text-align: right;
        font-family: var(--arabic-font);
    }

    /* Flip navigation buttons for RTL */
    [dir="rtl"] .insurance-button-prev,
    [dir="rtl"] .insurance-button-next {
        transform: translateY(-50%) scaleX(-1);
    }

    [dir="rtl"] .insurance-button-prev {
        left: auto;
        right: 0;
    }

    [dir="rtl"] .insurance-button-next {
        right: auto;
        left: 0;
    }

    /* Fix for Arabic title rendering */
    .arabic-title {
        font-family: var(--arabic-font) !important;
        letter-spacing: 0 !important;
        word-spacing: normal !important;
        text-align: right !important;
        animation: none !important;
        display: inline-block !important;
        white-space: nowrap !important;
        direction: rtl !important;
    }
</style>
@endpush

<!-- Insurance Partners Section Start -->
<div class="insurance-partners bg-radius-section">
    <div class="container">
        <!-- Section Title Start -->
        <div class="section-title mb-5">
            <div class="row align-items-center">
                <div class="col-lg-6 order-lg-{{ app()->getLocale() === 'ar' ? '1' : '0' }} {{ app()->getLocale() === 'ar' ? 'text-end' : '' }}">
                    <span class="subtitle wow fadeInUp">{{ __('messages.Our Partners') }}</span>
                    <h2 class="{{ app()->getLocale() === 'ar' ? 'arabic-title' : 'title text-anime-style-2' }}" data-cursor="-opaque">{{ __('messages.Insurance Partners') }}</h2>
                </div>
                <div class="col-lg-6 order-lg-{{ app()->getLocale() === 'ar' ? '0' : '1' }}">
                    <div class="section-description">
                        <p class="text-muted">{{ __('messages.We work with leading insurance providers to ensure our patients receive the best care possible.') }}</p>
                    </div>
                </div>
            </div>
        </div>
        <!-- Section Title End -->

        <!-- Insurance Slider Start -->
        <div class="insurance-slider-container">
            <div class="swiper-button-prev insurance-button-prev">
                <i class="fas fa-chevron-left"></i>
            </div>
            <div class="swiper-button-next insurance-button-next">
                <i class="fas fa-chevron-right"></i>
            </div>

            <div class="swiper insurance-swiper">
                <div class="swiper-wrapper">
                    @forelse($insurances as $insurance)
                        <div class="swiper-slide">
                            <div class="insurance-card wow fadeInUp" data-wow-delay="{{ $loop->iteration * 0.1 }}s">
                                <div class="insurance-logo-wrapper">
                                    <img src="{{ asset('storage/' . $insurance->logo) }}"
                                         alt="{{ $insurance->{'name_' . app()->getLocale()} }}"
                                         class="insurance-logo"
                                         loading="lazy"
                                         onerror="this.onerror=null; this.src='https://ui-avatars.com/api/?background=random&name={{ urlencode($insurance->name_en) }}'">
                                </div>
                                <h4 class="insurance-name">{{ $insurance->{'name_' . app()->getLocale()} }}</h4>
                                {{-- <div class="insurance-description">
                                    {!! $insurance->{'description_' . app()->getLocale()} !!}
                                </div> --}}
                                {{-- <div class="insurance-overlay">
                                    <div class="overlay-content">
                                        <span class="learn-more">{{ __('messages.Learn More') }}</span>
                                    </div>
                                </div> --}}
                            </div>
                        </div>
                    @empty
                        <div class="col-12 text-center">
                            <p>{{ __('messages.No Insurance Partners') }}</p>
                        </div>
                    @endforelse
                </div>
            </div>
            <div class="swiper-pagination insurance-pagination"></div>
        </div>
        <!-- Insurance Slider End -->
    </div>
</div>
<!-- Insurance Partners Section End -->

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Swiper
    const insuranceSwiper = new Swiper('.insurance-swiper', {
        slidesPerView: 1,
        spaceBetween: 30,
        loop: true,
        speed: 800,
        autoplay: {
            delay: 4000,
            disableOnInteraction: false,
            pauseOnMouseEnter: true
        },
        effect: 'slide',
        grabCursor: true,
        pagination: {
            el: '.insurance-pagination',
            clickable: true,
            dynamicBullets: true
        },
        navigation: {
            nextEl: '.insurance-button-next',
            prevEl: '.insurance-button-prev'
        },
        breakpoints: {
            576: {
                slidesPerView: 2,
                spaceBetween: 20
            },
            768: {
                slidesPerView: 3,
                spaceBetween: 25
            },
            992: {
                slidesPerView: 4,
                spaceBetween: 30
            },
            1200: {
                slidesPerView: 5,
                spaceBetween: 30
            }
        }
    });
});
</script>
@endpush















