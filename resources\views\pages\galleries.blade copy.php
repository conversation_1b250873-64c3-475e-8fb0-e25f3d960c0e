@extends('layouts.app')  <!-- Extend the base layout -->


@section('content')
    <!-- Page Header Start -->
    <div class="page-header gallery-page-header bg-radius-section parallaxie" data-bg-image="{{ asset('assets/images/gallery-hero-bg.jpg') }}">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-12">
                    <!-- Page Header Box Start -->
                    <div class="page-header-box text-center">
                        <h1 class="text-anime-style-2" data-cursor="-opaque">{{ __('messages.Our Gallery') }}</h1>
                        <p class="wow fadeInUp" data-wow-delay="0.2s">{{ __('messages.Explore our collection of medical excellence and patient care') }}</p>
                        <nav class="wow fadeInUp" data-wow-delay="0.3s">
                            <ol class="breadcrumb justify-content-center">
                                <li class="breadcrumb-item"><a href="{{ url(app()->getLocale()) }}">{{ __('messages.home') }}</a></li>
                                <li class="breadcrumb-item active" aria-current="page">{{ __('messages.Gallery') }}</li>
                            </ol>
                        </nav>
                    </div>
                    <!-- Page Header Box End -->
                </div>
            </div>
        </div>
    </div>
    <!-- Page Header End -->

    <!-- Gallery Main Content Start -->
    <main class="gallery-main-content section-padding">
        <!-- Gallery Filter Section Start -->
        <section class="gallery-filter-section">
            <div class="container">
                <!-- Section Title Start -->
                <div class="section-title text-center mb-5">
                    <h2 class="title wow fadeInUp" data-wow-delay="0.2s">{{ __('messages.Our Medical Gallery') }}</h2>
                    <p class="subtitle wow fadeInUp" data-wow-delay="0.3s">{{ __('messages.Browse through our comprehensive collection') }}</p>
                </div>
                <!-- Section Title End -->

                <div class="gallery-filter mb-5">
                    <div class="row justify-content-center">
                        <div class="col-lg-8">
                            <div class="filter-buttons text-center">
                                <button class="btn-default filter-btn active" data-filter="all">
                                    <span>
                                        <i class="fas fa-th-large me-2"></i>
                                        {{ __('messages.All') }}
                                    </span>
                                </button>
                                <button class="btn-default filter-btn" data-filter="image">
                                    <span>
                                        <i class="fas fa-image me-2"></i>
                                        {{ __('messages.Images') }}
                                    </span>
                                </button>
                                <button class="btn-default filter-btn" data-filter="video">
                                    <span>
                                        <i class="fas fa-video me-2"></i>
                                        {{ __('messages.Videos') }}
                                    </span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="gallery-grid">
                    <div class="row g-4">
                        @forelse($galleries as $gallery)
                            <div class="col-lg-4 col-md-6 gallery-item {{ $gallery->type }}">
                                <div class="gallery-card wow fadeInUp" data-wow-delay="{{ $loop->iteration * 0.2 }}s">
                                    <div class="gallery-card__image">
                                        @if($gallery->type === 'image')
                                            <a href="{{ asset($gallery->getMediaUrl()) }}"
                                               data-fancybox="gallery"
                                               class="image-popup image-anime"
                                               data-caption="{{ app()->getLocale() === 'en' ? $gallery->title_en : $gallery->title_ar }}"
                                               data-cursor-text="{{ __('messages.View Image') }}">
                                                <img src="{{ asset($gallery->getMediaUrl()) }}"
                                                     alt="{{ app()->getLocale() === 'en' ? $gallery->title_en : $gallery->title_ar }}"
                                                     loading="lazy"
                                                     onerror="this.onerror=null; this.src='https://ui-avatars.com/api/?background=random&name={{ urlencode($gallery->title_en) }}'"
                                                     class="img-fluid">
                                                {{-- <div class="gallery-card__overlay">
                                                    <i class="fas fa-search-plus"></i>
                                                </div> --}}
                                            </a>
                                        @else
                                        <div class="video-gallery-image wow fadeInUp">
                                            <a href="{{
                                                $gallery->media_url ?
                                                    (str_contains($gallery->media_url, 'youtube.com') ?
                                                        str_replace('youtube.com/watch?v=', 'youtube.com/embed/', $gallery->media_url) :
                                                        $gallery->media_url) :
                                                    asset('storage/gallery/videos/' . $gallery->media_file)
                                            }}"
                                               data-fancybox="gallery"
                                               class="video-popup"
                                               data-type="{{
                                                   $gallery->media_url ?
                                                       (str_contains($gallery->media_url, 'youtube.com') ? 'youtube' :
                                                       (str_contains($gallery->media_url, 'vimeo.com') ? 'vimeo' : '')) :
                                                       'html5video'
                                               }}"
                                               @if(!$gallery->media_url && $gallery->media_file)
                                               data-src="{{ asset('storage/gallery/videos/' . $gallery->media_file) }}"
                                               type="video/mp4"
                                               poster="{{ $gallery->getThumbnailUrl() ? asset($gallery->getThumbnailUrl()) : '' }}"
                                               @endif
                                               data-caption="{{ app()->getLocale() === 'en' ? $gallery->title_en : $gallery->title_ar }}"
                                               data-cursor-text="{{ __('messages.Play Video') }}">
                                                <img src="{{ $gallery->getThumbnailUrl() ?
                                                          asset($gallery->getThumbnailUrl()) :
                                                          'https://ui-avatars.com/api/?background=random&name=' . urlencode($gallery->title_en) }}"
                                                     alt="{{ app()->getLocale() === 'en' ? $gallery->title_en : $gallery->title_ar }}"
                                                     loading="lazy"
                                                     class="img-fluid">
                                                <div class="gallery-card__overlay">
                                                    <i class="fas fa-play"></i>
                                                </div>
                                            </a>
                                        </div>
                                        @endif
                                    </div>
                                    <div class="gallery-card__content">
                                        <h3 class="gallery-card__title">
                                            {{ app()->getLocale() === 'en' ? $gallery->title_en : $gallery->title_ar }}
                                        </h3>
                                        <p class="gallery-card__description">
                                            {{ app()->getLocale() === 'en' ? $gallery->description_en : $gallery->description_ar }}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        @empty
                            <div class="col-12 text-center">
                                <div class="gallery-empty">
                                    <i class="fas fa-images gallery-empty__icon"></i>
                                    <p>{{ __('messages.No galleries found') }}</p>
                                </div>
                            </div>
                        @endforelse
                    </div>
                </div>
            </div>
        </section>
        <!-- Gallery Filter Section End -->
    </main>
    <!-- Gallery Main Content End -->
@endsection

@push('styles')
{{-- <style>
    /* Page Header Styles */
    .gallery-page-header {
        margin-top: -100px!important;
        padding: 220px 0 180px!important;
        position: relative!important;
        overflow: hidden!important;
    }

    .gallery-page-header::before {
        content: ''!important;
        position: absolute!important;
        top: 0!important;
        left: 0!important;
        width: 100%!important;
        height: 100%!important;
        background: rgba(0, 0, 0, 0.6)!important;
        z-index: 1!important;
    }

    .gallery-page-header .page-header-box {
        position: relative !important;
        z-index: 2 !important;
    }

    .gallery-page-header .page-header-box p {
        color: var(--white-color) !important;
        font-size: 1.1rem !important;
        margin-top: 1rem !important;
        opacity: 0.9 !important;
    }

    /* Gallery Main Content */
    .section-padding {
        padding: 120px 0 !important;
    }

    .gallery-filter-section {
        position: relative !important;
        background: var(--white-color) !important;
        border-radius: 20px !important;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05) !important;
        margin-top: -60px !important;
        padding: 60px 40px !important;
        z-index: 3 !important;
    }

    /* Filter Button Styles */
    .filter-buttons {
        display: flex !important;
        justify-content: center !important;
        flex-wrap: wrap !important;
        gap: 20px !important;
        margin-bottom: 40px !important;
    }

    .filter-buttons .btn-default {
        position: relative !important;
        display: inline-flex !important;
        line-height: 1.2em !important;
        background: var(--secondary-color) !important;
        text-transform: capitalize !important;
        border-radius: 100px !important;
        padding: 2px 25px 2px 2px !important;
        border: none !important;
        overflow: hidden !important;
        transition: all 0.5s ease-in-out !important;
    }

    .filter-buttons .btn-default:hover {
        background-color: transparent !important;
    }

    .filter-buttons .btn-default::before {
        content: '' !important;
        position: absolute !important;
        top: 50% !important;
        right: 0 !important;
        bottom: 0 !important;
        width: 10px !important;
        height: 10px !important;
        background-image: url('../images/arrow-blue.svg') !important;
        background-repeat: no-repeat !important;
        background-position: center center !important;
        background-size: auto !important;
        transform: translate(-10px, -50%) !important;
        transition: all 0.4s ease-in-out !important;
        z-index: 1 !important;
    }

    .filter-buttons .btn-default:hover::before {
        filter: brightness(0) invert(1) !important;
        transform: translate(-10px, -50%) !important;
    }

    .filter-buttons .btn-default::after {
        content: '' !important;
        display: block !important;
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        bottom: 0 !important;
        width: 0 !important;
        height: 100% !important;
        border-radius: 100px !important;
        background: var(--primary-color) !important;
        transition: all 0.4s ease-in-out !important;
        z-index: 0 !important;
    }

    .filter-buttons .btn-default:hover::after {
        width: 100% !important;
    }

    .filter-buttons .btn-default span {
        position: relative !important;
        display: inline-flex !important;
        align-items: center !important;
        justify-content: center !important;
        font-size: 18px !important;
        font-weight: 500 !important;
        line-height: 1.2em !important;
        background-color: var(--primary-color) !important;
        color: var(--secondary-color) !important;
        border-radius: 100px !important;
        padding: 15px 20px !important;
        z-index: 1 !important;
        transition: all 0.5s ease-in-out !important;
    }

    .filter-buttons .btn-default:hover span {
        background-color: transparent !important;
        color: var(--secondary-color) !important;
    }

    .filter-buttons .btn-default span::after {
        content: '' !important;
        display: block !important;
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        bottom: 0 !important;
        width: 0 !important;
        height: 100% !important;
        border-radius: 100px !important;
        background: var(--secondary-color) !important;
        transition: all 0.4s ease-in-out !important;
        z-index: -1 !important;
    }

    .filter-buttons .btn-default.active {
        background-color: var(--primary-color) !important;
    }

    .filter-buttons .btn-default.active:hover::after {
        background-color: var(--secondary-color) !important;
    }

    .filter-buttons .btn-default.active::before {
        background-image: url('../images/arrow-white.svg') !important;
    }

    .filter-buttons .btn-default.active:hover::before {
        filter: brightness(0.1) invert(0) !important;
    }

    .filter-buttons .btn-default.active span {
        background-color: var(--secondary-color) !important;
        color: var(--primary-color) !important;
    }

    /* Mobile Responsive */
    @media (max-width: 991px) {
        .filter-buttons {
            gap: 10px !important;
        }

        .filter-buttons .btn-default {
            padding: 2px 20px 2px 2px !important;
        }

        .filter-buttons .btn-default span {
            font-size: 16px !important;
            padding: 12px 16px !important;
        }
    }

    @media (max-width: 480px) {
        .filter-buttons {
            flex-direction: column !important;
            align-items: center !important;
        }

        .filter-buttons .btn-default {
            width: 100% !important;
            max-width: 200px !important;
        }
    }

    /* Gallery Item Animation */
    .gallery-item {
        position: relative !important;
        transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1) !important;
        transform: scale(1) translateY(0) !important;
        opacity: 1 !important;
        visibility: visible !important;
        pointer-events: auto !important;
        will-change: transform, opacity !important;
    }

    .gallery-item.filtered-out {
        transform: scale(0.6) translateY(40px) !important;
        opacity: 0 !important;
        visibility: hidden !important;
        pointer-events: none !important;
        position: absolute !important;
        z-index: -1 !important;
    }

    .gallery-item.filtering {
        animation: filterPulse 0.8s cubic-bezier(0.4, 0, 0.2, 1) !important;
    }

    @keyframes filterPulse {
        0% {
            transform: scale(1) !important;
        }
        50% {
            transform: scale(1.05) !important;
        }
        100% {
            transform: scale(1) !important;
        }
    }

    .gallery-grid {
        display: grid !important;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)) !important;
        gap: 30px !important;
        position: relative !important;
        min-height: 200px !important;
        padding: 15px !important;
    }

    .gallery-item {
        position: relative !important;
        transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1) !important;
        transform: scale(1) !important;
        opacity: 1 !important;
        visibility: visible !important;
        pointer-events: auto !important;
        will-change: transform, opacity, grid-position !important;
        grid-column: auto !important;
        grid-row: auto !important;
    }

    .gallery-item.filtered-out {
        transform: scale(0.6) !important;
        opacity: 0 !important;
        visibility: hidden !important;
        pointer-events: none !important;
        position: absolute !important;
        grid-column: 1 !important;
        grid-row: 1 !important;
        z-index: -1 !important;
    }

    /* Responsive grid adjustments */
    @media (max-width: 1200px) {
        .gallery-grid {
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr)) !important;
            gap: 20px !important;
        }
    }

    @media (max-width: 768px) {
        .gallery-grid {
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)) !important;
            gap: 15px !important;
        }
    }

    /* Gallery Card */
    .gallery-card {
        background: var(--white-color) !important;
        border-radius: 15px !important;
        overflow: hidden !important;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05) !important;
        transition: all 0.3s ease !important;
        height: 100% !important;
    }

    .gallery-card:hover {
        transform: translateY(-5px) !important;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1) !important;
    }

    .gallery-card__image {
        position: relative !important;
        width: 100% !important;
        padding-top: 75% !important; /* 4:3 Aspect Ratio */
        overflow: hidden !important;
    }

    .gallery-card__image img {
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 100% !important;
        object-fit: cover !important;
        transition: transform 0.5s ease !important;
    }

    .gallery-card:hover .gallery-card__image img {
        transform: scale(1.1) !important;
    }

    .gallery-card__overlay {
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 100% !important;
        background: rgba(0, 0, 0, 0.4) !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        opacity: 0 !important;
        transition: all 0.3s ease !important;
    }

    .gallery-card__overlay i {
        position: relative !important;
        width: 50px !important;
        height: 50px !important;
        background: var(--white-color) !important;
        border-radius: 50% !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        color: var(--primary-color) !important;
        font-size: 18px !important;
        transform: scale(0.8) !important;
        transition: all 0.3s ease !important;
    }

    .gallery-card:hover .gallery-card__overlay {
        opacity: 1 !important;
    }

    .gallery-card:hover .gallery-card__overlay i {
        transform: scale(1) !important;
    }

    /* Add cursor text for better UX */
    .gallery-card__image a {
        cursor: pointer !important;
    }

    .image-anime {
        position: relative !important;
        display: block !important;
        overflow: hidden !important;
    }

    .gallery-card__content {
        padding: 1.5rem !important;
        background: var(--white-color) !important;
    }

    .gallery-card__title {
        font-size: 1.25rem !important;
        margin-bottom: 0.5rem !important;
        color: var(--primary-color) !important;
        transition: color 0.3s ease !important;
    }

    .gallery-card:hover .gallery-card__title {
        color: var(--secondary-color) !important;
    }

    .gallery-card__description {
        font-size: 0.875rem !important;
        color: var(--text-color) !important;
        margin-bottom: 0 !important;
    }

    /* Empty State */
    .gallery-empty {
        padding: 60px 20px !important;
    }

    .gallery-empty__icon {
        font-size: 4rem !important;
        color: var(--primary-color) !important;
        opacity: 0.5 !important;
        margin-bottom: 20px !important;
    }

    /* Responsive Styles */
    @media (max-width: 991px) {
        .gallery-page-header {
            padding: 180px 0 140px !important;
        }

        .section-padding {
            padding: 80px 0 !important;
        }

        .gallery-filter-section {
            padding: 40px 20px !important;
        }
    }

    @media (max-width: 767px) {
        .gallery-page-header {
            padding: 160px 0 120px !important;
        }

        .section-padding {
            padding: 60px 0 !important;
        }

        .gallery-filter-section {
            padding: 30px 15px !important;
            margin-top: -40px !important;
        }

        .filter-btn {
            padding: 10px 20px !important;
            font-size: 0.875rem !important;
        }
    }

    @media (max-width: 768px) {
        .filter-buttons {
            flex-wrap: wrap !important;
            gap: 12px !important;
        }

        .filter-btn {
            min-width: 120px !important;
            padding: 10px 20px !important;
            font-size: 14px !important;
        }
    }

    /* Gallery Grid */
    .gallery-grid {
        overflow: hidden !important;
    }

    /* Gallery Item */
    .gallery-item {
        transform: scale(1) !important;
        opacity: 1 !important;
        transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1) !important;
        transform-origin: center !important;
    }

    .gallery-item.filtered-out {
        transform: scale(0.6) translateY(40px) !important;
        opacity: 0 !important;
    }

    /* Gallery Card */
    .gallery-card {
        background: var(--white-color) !important;
        border-radius: 15px !important;
        overflow: hidden !important;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05) !important;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
        height: 100% !important;
    }

    .gallery-card:hover {
        transform: translateY(-5px) !important;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1) !important;
    }

    .gallery-card__image {
        position: relative !important;
        width: 100% !important;
        padding-top: 75% !important; /* 4:3 Aspect Ratio */
        overflow: hidden !important;
    }

    .gallery-card__image img {
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 100% !important;
        object-fit: cover !important;
        transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1) !important;
    }

    .gallery-card:hover .gallery-card__image img {
        transform: scale(1.1) !important;
    }

    .gallery-card__overlay {
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 100% !important;
        background: rgba(0, 0, 0, 0.4) !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        opacity: 0 !important;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
    }

    .gallery-card:hover .gallery-card__overlay {
        opacity: 1 !important;
    }

    .gallery-card__overlay i {
        color: var(--white-color) !important;
        font-size: 2.5rem !important;
        transform: scale(0.8) !important;
        opacity: 0 !important;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
    }

    .gallery-card:hover .gallery-card__overlay i {
        transform: scale(1) !important;
        opacity: 1 !important;
    }

    /* Filter Buttons */
    .filter-btn {
        position: relative !important;
        padding: 12px 24px !important;
        border-radius: 30px !important;
        background: var(--white-color) !important;
        color: var(--primary-color) !important;
        border: 2px solid var(--primary-color) !important;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
        overflow: hidden !important;
    }

    .filter-btn::before {
        content: '' !important;
        position: absolute !important;
        top: 50% !important;
        left: 50% !important;
        width: 0 !important;
        height: 0 !important;
        background: var(--primary-color) !important;
        border-radius: 50% !important;
        transform: translate(-50%, -50%) !important;
        transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1) !important;
        z-index: -1 !important;
    }

    .filter-btn:hover::before,
    .filter-btn.active::before {
        width: 200% !important;
        height: 200% !important;
    }

    .filter-btn:hover,
    .filter-btn.active {
        color: var(--white-color) !important;
        transform: translateY(-2px) !important;
    }

    /* Content Animation */
    .gallery-card__content {
        padding: 1.5rem !important;
        transform: translateY(0) !important;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
    }

    .gallery-card:hover .gallery-card__content {
        transform: translateY(-5px) !important;
    }

    .gallery-card__title {
        font-size: 1.25rem !important;
        margin-bottom: 0.5rem !important;
        color: var(--primary-color) !important;
        transition: color 0.3s ease !important;
    }

    .gallery-card:hover .gallery-card__title {
        color: var(--secondary-color) !important;
    }

    .gallery-card__description {
        font-size: 0.875rem !important;
        color: var(--text-color) !important;
        margin-bottom: 0 !important;
        opacity: 0.8 !important;
        transition: opacity 0.3s ease !important;
    }

    .gallery-card:hover .gallery-card__description {
        opacity: 1 !important;
    }

    /* Empty State Animation */
    .gallery-empty {
        animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) !important;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0 !important;
            transform: translateY(20px) !important;
        }
        to {
            opacity: 1 !important;
            transform: translateY(0) !important;
        }
    }

    /* Update gallery item transitions */
    .gallery-item {
        position: relative !important;
        transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1) !important;
        transform: scale(1) !important;
        opacity: 1 !important;
        height: auto !important;
        width: 100% !important;
        display: block !important;
        will-change: transform, opacity !important;
    }

    /* Add button click animation */
    .filter-btn-clicked {
        animation: buttonPulse 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    }

    @keyframes buttonPulse {
        0% { transform: scale(1) !important; }
        50% { transform: scale(0.95) !important; }
        100% { transform: scale(1) !important; }
    }

    /* Ensure smooth grid transitions */
    .gallery-grid {
        transition: height 0.8s cubic-bezier(0.4, 0, 0.2, 1) !important;
        will-change: height !important;
    }

    /* Add hover effect for gallery items */
    .gallery-item:not(.filtered-out) {
        transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1) !important;
    }

    .gallery-item:not(.filtered-out):hover {
        transform: scale(1.02) !important;
    }

    /* Standardize gallery item dimensions */
    .gallery-item {
        aspect-ratio: 16/9 !important;
        height: 100% !important;
        width: 100% !important;
    }

    .gallery-card {
        height: 100% !important;
    }

    .gallery-card__image {
        position: relative !important;
        width: 100% !important;
        height: 0 !important;
        padding-top: 56.25% !important; /* 16:9 Aspect Ratio (9/16 = 0.5625) */
        overflow: hidden !important;
    }

    /* Standardize media content sizing */
    .gallery-card__image img,
    .gallery-card__image video,
    .gallery-card__image iframe,
    .video-wrapper {
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 100% !important;
        object-fit: cover !important;
    }

    /* Ensure video wrapper maintains aspect ratio */
    .video-wrapper {
        padding-top: 56.25% !important; /* 16:9 Aspect Ratio */
    }

    /* Grid layout adjustments */
    .gallery-grid .row {
        --bs-gutter-x: 30px !important;
        --bs-gutter-y: 30px !important;
    }

    .gallery-grid .col-lg-4,
    .gallery-grid .col-md-6 {
        margin-bottom: var(--bs-gutter-y) !important;
    }

    /* Image container positioning */
    .gallery-card__image {
        position: relative !important;
        width: 100% !important;
        padding-bottom: 75% !important; /* 4:3 Aspect ratio */
    }

    /* Overlay styling */
    .gallery-card__overlay {
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 100% !important;
        background: rgba(0, 0, 0, 0.4) !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        opacity: 0 !important;
        transition: all 0.3s ease !important;
        z-index: 2 !important; /* Ensure overlay is above the image */
    }

    /* Icon styling */
    .gallery-card__overlay i {
        color: var(--white-color) !important;
        font-size: 24px !important;
        width: 50px !important;
        height: 50px !important;
        background: rgba(255, 255, 255, 0.2) !important;
        border-radius: 50% !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        transform: scale(0.8) !important;
        transition: all 0.3s ease !important;
    }

    /* Hover effects */
    .gallery-card:hover .gallery-card__overlay {
        opacity: 1 !important;
    }

    .gallery-card:hover .gallery-card__overlay i {
        transform: scale(1) !important;
    }

    /* Ensure images and videos are properly positioned */
    .gallery-card__image img,
    .gallery-card__image video {
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 100% !important;
        object-fit: cover !important;
    }

    /* Link wrapper */
    .image-popup,
    .video-popup {
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 100% !important;
        display: block !important;
    }
</style> --}}
@endpush

@push('scripts')  <!-- Push scripts to the stack -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize Fancybox
        Fancybox.bind("[data-fancybox='gallery']", {
            // Enhanced Image options
            Image: {
                zoom: true,
                wheel: true,          // Enable mouse wheel for zooming
                click: "toggleZoom",  // Click to toggle zoom
                doubleClick: "toggleZoom", // Double click to toggle zoom
                wheelLimit: 10,       // Maximum zoom level with mouse wheel
                zoomFriction: 0.88,   // Zoom friction (smoothness)
                infinite: false,      // Disable infinite zoom
            },

            Images: {
                Panzoom: {
                    maxScale: 5,
                    minScale: 0.5,
                },
            },
            // Video specific options
            Video: {
                ratio: 16/9, // Default aspect ratio
                autoplay: false, // Don't autoplay videos
                click: "play", // Start playing on click
            },
            Youtube: {
                ratio: 16/9, // Default aspect ratio
                autoplay: false, // Don't autoplay videos
                controls: 1, // Show YouTube video controls
            },
            Vimeo: {
                ratio: 16/9, // Default aspect ratio
                autoplay: false, // Don't autoplay videos
                controls: true, // Show Vimeo video controls
            },
            Toolbar: {
                display: {
                    left: ["prev"],
                    middle: [
                        "zoomIn",
                        "zoomOut",
                        "toggle1to1",
                        "toggleZoom",
                        "rotateCCW",
                        "rotateCW",
                        "flipX",
                        "flipY"
                    ],
                    right: ["next"],
                }
            },

            // Zoom options
            zoom: {
                friction: 0.88,        // Zoom animation friction
                speedIn: 600,          // Zoom in speed
                speedOut: 600,         // Zoom out speed
                mode: "zoom",          // Zoom mode
                clicked: true,
                mouseWheel: true,      // Enable mouse wheel zoom
                touch: true,           // Enable touch zoom
                pinch: true,           // Enable pinch zoom
                maxScale: 5,           // Maximum zoom scale (5x)
                minScale: 0.5,         // Minimum zoom scale
            },

            // Pan options
            pan: {
                enabled: true,         // Enable panning
                friction: 0.88,        // Pan friction
                momentum: true,        // Enable momentum after panning
            },

            // Gesture options
            gesture: {
                enabled: true,         // Enable gestures
                friction: 0.88,        // Gesture friction
                minScale: 0.5,         // Minimum scale for pinch
                maxScale: 5,           // Maximum scale for pinch
            },

            // Thumbnails
            Thumbs: {
                type: "classic",
                showOnStart: true,    // Don't show thumbnails automatically
                key: "t",             // Press 't' to toggle thumbnails
            },

            // Keyboard shortcuts
            keyboard: {
                Escape: "close",
                ArrowLeft: "prev",
                ArrowRight: "next",
                Delete: "close",
                "+": "zoomIn",         // Plus key to zoom in
                "-": "zoomOut",        // Minus key to zoom out
                "0": "toggleZoom",     // 0 key to toggle zoom
            },

            on: {
                "init": (fancybox) => {
                    const clickedElement = fancybox.options.$trigger;
                    if (clickedElement) {
                        const gallery = document.querySelectorAll('[data-fancybox="gallery"]');
                        const index = Array.from(gallery).indexOf(clickedElement);
                        if (index >= 0) {
                            fancybox.jumpTo(index);
                        }
                    }
                },
                "reveal": (fancybox, slide) => {
                    // Reset zoom level on slide change
                    if (slide.$content) {
                        slide.$content.style.transform = '';
                    }
                },
                initLayout: (fancybox) => {
                    // Enable zoom by default
                    fancybox.Panzoom = {
                        maxScale: 5
                    };
                }
            }
        });

        // Add keyboard shortcuts info
        const zoomInfo = document.createElement('div');
        zoomInfo.className = 'zoom-shortcuts-info';
        zoomInfo.innerHTML = `
            <style>
                .zoom-shortcuts-info {
                    position: fixed;
                    bottom: 20px;
                    left: 20px;
                    background: rgba(0,0,0,0.7);
                    color: white;
                    padding: 10px;
                    border-radius: 5px;
                    font-size: 12px;
                    z-index: 9999;
                    display: none;
                }
                .fancybox__container:hover .zoom-shortcuts-info {
                    display: block;
                }
            </style>
            Zoom Controls:<br>
            + : Zoom In<br>
            - : Zoom Out<br>
            0 : Reset Zoom<br>
            Mouse Wheel: Zoom In/Out<br>
            T: Toggle Thumbnails
        `;
        document.body.appendChild(zoomInfo);

        // Enhanced Filter functionality
        const filterButtons = document.querySelectorAll('.filter-btn');
        const galleryItems = document.querySelectorAll('.gallery-item');

        function filterGallery(filterValue) {
            const grid = document.querySelector('.gallery-grid');
            const items = Array.from(document.querySelectorAll('.gallery-item'));
            let delay = 0;
            const stagger = 50;

            // Reset all items to ensure transitions work
            items.forEach(item => {
                item.style.display = 'block';
                // Force reflow
                item.offsetHeight;
            });

            // Apply transitions with staggered delays
            items.forEach((item, index) => {
                const shouldShow = filterValue === 'all' || item.classList.contains(filterValue);

                // Reset transition
                item.style.transition = 'none';
                item.offsetHeight; // Force reflow

                // Add transition with delay
                item.style.transition = `all 0.8s cubic-bezier(0.4, 0, 0.2, 1) ${delay}ms`;

                if (shouldShow) {
                    // Add a small initial scale for "all" filter to ensure animation
                    if (filterValue === 'all') {
                        item.style.transform = 'scale(0.8)';
                        item.style.opacity = '0';
                        item.offsetHeight; // Force reflow

                        // Then animate to full scale
                        requestAnimationFrame(() => {
                            item.style.transform = 'scale(1)';
                            item.style.opacity = '1';
                        });
                    } else {
                        item.style.transform = 'scale(1)';
                        item.style.opacity = '1';
                    }
                    item.classList.remove('filtered-out');
                    delay += stagger;
                } else {
                    item.classList.add('filtered-out');
                    item.style.transform = 'scale(0.6)';
                    item.style.opacity = '0';
                }
            });

            // Update layout after transition
            setTimeout(() => {
                items.forEach(item => {
                    if (item.classList.contains('filtered-out')) {
                        item.style.display = 'none';
                    }
                });
            }, 800 + (items.length * stagger));
        }

        filterButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();

                // Update active state
                filterButtons.forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');

                // Add animation class to button
                button.classList.add('filter-btn-clicked');
                setTimeout(() => button.classList.remove('filter-btn-clicked'), 300);

                // Apply filter
                const filterValue = button.getAttribute('data-filter');
                filterGallery(filterValue);
            });
        });

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', () => {
            const grid = document.querySelector('.gallery-grid');

            // Initial layout with animation
            setTimeout(() => {
                filterGallery('all');
            }, 300);
        });
    });
</script>
@endpush  <!-- End of push scripts to the stack -->



