@extends('layouts.app')

@section('content')
    <!-- Page Header Start -->
	<div class="page-header about-page-header bg-radius-section parallaxie">
		<div class="container">
			<div class="row align-items-center">
				<div class="col-lg-12">
					<!-- Page Header Box Start -->
					<div class="page-header-box">
						<h1 class="{{ app()->getLocale() == 'ar' ? 'arabic-title' : 'text-anime-style-2' }}" data-cursor="-opaque">{{ $bodyPart->{'name_' . app()->getLocale()} }}</h1>
						<nav class="wow fadeInUp">
							<ol class="breadcrumb {{ app()->getLocale() == 'ar' ? 'rtl-breadcrumb' : '' }}">
								<li class="breadcrumb-item"><a href="{{ url(app()->getLocale()) }}">{{ __('messages.Home') }}</a></li>
								<li class="breadcrumb-item active" aria-current="page">{{ $bodyPart->{'name_' . app()->getLocale()} }}</li>
							</ol>
						</nav>
					</div>
					<!-- Page Header Box End -->
				</div>
			</div>
		</div>
	</div>
	<!-- Page Header End -->
<section class="body-part-content bg-radius-section">
    <div class="container">
        <!-- Section Header -->
        <div class="row section-row align-items-center">
            <div class="col-lg-12">
                <div class="section-title text-center">
                    <span class="sub-title wow fadeInUp">{{ __('messages.Overview') }}</span>
                    <h2 class="{{ app()->getLocale() == 'ar' ? 'arabic-title' : 'text-anime-style-3' }}" data-cursor="-opaque">{{ $bodyPart->{'name_' . app()->getLocale()} }}</h2>
                    <div class="section-divider">
                        <div class="divider-icon">
                            <i class="fas fa-star"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="row">
            <div class="col-lg-12">
                <div class="body-part-wrapper">
                    <!-- Main Content Box -->
                    <div class="content-box reveal wow fadeIn" data-wow-delay="0.2s" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
                        <div class="content-box-inner {{ app()->getLocale() == 'ar' ? 'arabic-content' : '' }}">
                            {!! $bodyPart->{'description_' . app()->getLocale()} !!}
                        </div>
                    </div>

                    <!-- Decoration Elements -->
                    <div class="decoration-element-1"></div>
                    <div class="decoration-element-2"></div>
                    <div class="decoration-element-3"></div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
    .body-part-content {
        background-color: var(--white-color);
        padding: 100px 0;
        position: relative;
        overflow: hidden;
    }

    /* Section Title Styles */
    .section-title .sub-title {
        display: block;
        color: var(--primary-color);
        font-size: 1.1rem;
        font-weight: 500;
        margin-bottom: 15px;
        text-transform: uppercase;
        letter-spacing: 2px;
    }

    /* Main Content Box */
    .body-part-wrapper {
        position: relative;
        padding: 40px 20px; /* Added horizontal padding */
        width: 100%;
    }

    .content-box {
        position: relative;
        padding: 50px 70px; /* Increased horizontal padding */
        background: var(--white-color);
        border-radius: 20px;
        box-shadow: 0 15px 40px rgba(var(--primary-color-rgb), 0.05);
        z-index: 2;
        width: 100%;
        max-width: 1200px; /* Increased max-width */
        margin: 0 auto; /* Center the box */
    }

    .content-box-inner {
        position: relative;
        max-width: 100%; /* Changed from 900px to use full width */
        margin: 0 auto;
    }

    /* Interactive Elements */
    .interactive-elements {
        margin-top: 40px;
        padding-top: 30px;
        border-top: 1px solid rgba(var(--primary-color-rgb), 0.1);
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .action-buttons {
        display: flex;
        flex-direction: column;
        gap: 15px;
        width: 100%;
        max-width: 300px; /* Limit the width of buttons */
    }

    .primary-btn, .outline-btn {
        padding: 15px 25px;
        border-radius: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
        font-weight: 500;
        transition: all 0.3s ease;
        width: 100%;
        text-align: center;
    }

    .primary-btn {
        background: var(--primary-color);
        color: var(--white-color);
    }

    .outline-btn {
        border: 2px solid var(--primary-color);
        color: var(--primary-color);
    }

    .primary-btn:hover, .outline-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(var(--primary-color-rgb), 0.2);
    }

    /* Content Styling */
    .content-box h1, .content-box h2, .content-box h3,
    .content-box h4, .content-box h5, .content-box h6 {
        color: var(--primary-color);
        margin-bottom: 20px;
        font-weight: 600;
        position: relative;
        padding-bottom: 15px;
    }

    .content-box h2::after,
    .content-box h3::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 50px;
        height: 2px;
        background: rgba(var(--primary-color-rgb), 0.2);
    }

    .content-box p {
        color: gray !important;
        margin-bottom: 20px;
        line-height: 1.8;
        font-size: 1.05rem;
    }

    /* Add these new styles to maintain blue color for list items */
    .content-box ul li,
    .content-box ol li {
        color: gray !important;
        line-height: 1.8;
        font-size: 1.05rem;
    }

    /* If you need to style nested paragraphs inside list items differently */
    .content-box ul li p,
    .content-box ol li p {
        color: gray !important;
    }

    /* Decoration Elements */
    .decoration-element-1,
    .decoration-element-2,
    .decoration-element-3 {
        position: absolute;
        border-radius: 50%;
        z-index: 1;
    }

    .decoration-element-1 {
        top: -50px;
        right: -100px;
        width: 200px;
        height: 200px;
        background: linear-gradient(45deg, rgba(var(--primary-color-rgb), 0.05), rgba(var(--primary-color-rgb), 0.1));
    }

    .decoration-element-2 {
        bottom: -80px;
        left: -120px;
        width: 250px;
        height: 250px;
        background: linear-gradient(-45deg, rgba(var(--primary-color-rgb), 0.05), rgba(var(--primary-color-rgb), 0.1));
    }

    .decoration-element-3 {
        top: 50%;
        right: -50px;
        width: 150px;
        height: 150px;
        background: linear-gradient(135deg, rgba(var(--primary-color-rgb), 0.03), rgba(var(--primary-color-rgb), 0.08));
    }

    /* Responsive Styles */
    @media (max-width: 991px) {
        .body-part-content {
            padding: 60px 0;
        }

        .content-box {
            padding: 40px 50px;
        }
    }

    @media (max-width: 768px) {
        .body-part-content {
            padding: 40px 0;
        }

        .interactive-elements {
            flex-direction: column;
            align-items: stretch;
        }

        .action-buttons {
            justify-content: center;
        }

        .decoration-element-1,
        .decoration-element-2,
        .decoration-element-3 {
            display: none;
        }
    }

    @media (max-width: 480px) {
        .content-box {
            padding: 20px 25px;
        }

        .body-part-wrapper {
            padding: 20px 10px;
        }
    }

    /* RTL and Arabic content styles */
    [dir="rtl"] .content-box h1, [dir="rtl"] .content-box h2,
    [dir="rtl"] .content-box h3, [dir="rtl"] .content-box h4,
    [dir="rtl"] .content-box h5, [dir="rtl"] .content-box h6 {
        text-align: right;
        font-family: var(--arabic-font);
    }

    [dir="rtl"] .content-box h2::after,
    [dir="rtl"] .content-box h3::after {
        left: auto;
        right: 0;
    }

    [dir="rtl"] .content-box p,
    [dir="rtl"] .content-box ul li,
    [dir="rtl"] .content-box ol li {
        text-align: right;
        font-family: var(--arabic-font);
    }

    .arabic-content {
        font-family: var(--arabic-font) !important;
        text-align: right !important;
        direction: rtl !important;
    }

    .arabic-content * {
        font-family: var(--arabic-font) !important;
        letter-spacing: 0 !important;
        word-spacing: normal !important;
    }

    /* RTL breadcrumb styles */
    .rtl-breadcrumb {
        direction: rtl;
        text-align: right;
    }

    .rtl-breadcrumb .breadcrumb-item {
        float: right;
    }

    .rtl-breadcrumb .breadcrumb-item+.breadcrumb-item::before {
        float: right;
        padding-left: 0.5rem;
        padding-right: 0;
    }
</style>
<!-- Common Causes Section - Tabbed Style -->
<section class="common-causes-section py-5" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
    <div class="container">
        <div class="row justify-content-center mb-5">
            <div class="col-lg-8 text-center">
                {{-- <span class="sub-heading text-primary mb-3 d-block wow fadeInUp">{{ __('messages.Common Causes') }}</span> --}}
                <h2 class="section-title h1 mb-4 wow fadeInUp {{ app()->getLocale() == 'ar' ? 'arabic-content' : '' }}">{{ __('messages.Explore common conditions affecting this area') }}</h2>
                <p class="lead text-muted wow fadeInUp {{ app()->getLocale() == 'ar' ? 'arabic-content' : '' }}">{{ __('messages.Learn about various conditions that commonly affect this area and their symptoms') }}</p>
            </div>
        </div>

        @if($bodyPart->activeCommonCauses->count() > 0)
            <div class="causes-tabs" id="causesTabSection">
                <!-- Desktop View -->
                <div class="tabs-wrapper d-none d-md-block">
                    <div class="tabs-container">
                        @foreach($bodyPart->activeCommonCauses as $cause)
                            <button
                                type="button"
                                class="tab-pill {{ $loop->first ? 'active' : '' }} {{ app()->getLocale() == 'ar' ? 'arabic-tab' : '' }}"
                                data-tab="{{ $loop->iteration }}"
                                data-title="{{ $cause->{'title_' . app()->getLocale()} }}"
                                role="tab"
                                aria-selected="{{ $loop->first ? 'true' : 'false' }}"
                                aria-controls="tab-content-{{ $loop->iteration }}"
                            >
                                <span class="tab-number">{{ str_pad($loop->iteration, 2, '0', STR_PAD_LEFT) }}</span>
                                <span class="tab-title">{{ $cause->{'title_' . app()->getLocale()} }}</span>
                            </button>
                        @endforeach
                    </div>
                </div>

                <!-- Mobile View -->
                <div class="tabs-wrapper-mobile d-md-none mb-4">
                    <select class="form-select {{ app()->getLocale() == 'ar' ? 'arabic-content' : '' }}" id="tabSelect">
                        @foreach($bodyPart->activeCommonCauses as $cause)
                            <option value="{{ $loop->iteration }}">
                                {{ $cause->{'title_' . app()->getLocale()} }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <style>
                    /* Add this at the top of your style section */
                    :root {
                        --tab-height: 45px;
                        --tab-gap: 8px;
                        --tabs-padding: 15px;
                    }

                    /* RTL specific styles for tabs */
                    [dir="rtl"] .tab-number {
                        margin-right: 0;
                        margin-left: 10px;
                    }

                    [dir="rtl"] .tab-number::after {
                        right: auto;
                        left: -5px;
                    }

                    /* [dir="rtl"] .cause-header {
                        flex-direction: row-reverse;
                    } */

                    [dir="rtl"] .cause-content h3,
                    [dir="rtl"] .cause-description {
                        text-align: right;
                    }

                    .arabic-tab {
                        font-family: var(--arabic-font);
                    }

                    /* Rest of your existing styles */
                    .tabs-wrapper {
                        max-width: 100%;
                        margin: 0 auto;
                    }

                    .tabs-container {
                        display: grid;
                        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
                        gap: var(--tab-gap);
                        padding: var(--tabs-padding);
                        background: rgba(var(--primary-color-rgb), 0.03);
                        border-radius: 12px;
                        margin-bottom: 2rem;
                    }

                    .tab-pill {
                        position: relative;
                        height: var(--tab-height);
                        overflow: hidden;
                        transition: all 0.3s ease;
                        background: white;
                        border: 1px solid rgba(var(--primary-color-rgb), 0.1);
                        border-radius: 8px;
                        display: flex;
                        align-items: center;
                        padding: 0 15px;
                        font-size: 0.95rem;
                        color: rgba(var(--primary-color-rgb), 0.8);
                        cursor: pointer;
                    }

                    /* Hover effect */
                    .tab-pill:hover {
                        transform: translateY(-1px);
                        box-shadow: 0 4px 12px rgba(var(--primary-color-rgb), 0.1);
                        border-color: rgba(var(--primary-color-rgb), 0.3);
                    }

                    /* Active state */
                    .tab-pill.active {
                        background: linear-gradient(
                            135deg,
                            rgba(var(--primary-color-rgb), 0.1) 0%,
                            rgba(var(--primary-color-rgb), 0.2) 100%
                        );
                        border-color: rgba(var(--primary-color-rgb), 0.4);
                        box-shadow: 0 4px 15px rgba(var(--primary-color-rgb), 0.15);
                        color: rgba(var(--primary-color-rgb), 1);
                    }

                    /* Number style */
                    .tab-number {
                        display: inline-flex;
                        align-items: center;
                        justify-content: center;
                        min-width: 24px;
                        margin-right: 10px;
                        font-weight: bold;
                        color: rgba(var(--primary-color-rgb), 0.6);
                        position: relative;
                    }

                    .tab-number::after {
                        content: '';
                        position: absolute;
                        right: -5px;
                        height: 15px;
                        width: 1px;
                        background: rgba(var(--primary-color-rgb), 0.2);
                    }

                    /* Active tab number */
                    .tab-pill.active .tab-number {
                        color: rgba(var(--primary-color-rgb), 1);
                    }

                    /* Tab content transition */
                    .tab-content {
                        opacity: 0;
                        transform: translateY(10px);
                        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                    }

                    .tab-content.active {
                        opacity: 1;
                        transform: translateY(0);
                    }

                    /* Responsive adjustments */
                    @media (max-width: 768px) {
                        .tabs-container {
                            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
                            gap: 6px;
                            padding: 10px;
                        }

                        .tab-pill {
                            font-size: 0.85rem;
                            height: 40px;
                            padding: 0 10px;
                        }

                        .tab-number {
                            min-width: 20px;
                            margin-right: 8px;
                        }
                    }

                    /* For very small screens */
                    @media (max-width: 480px) {
                        .tabs-container {
                            grid-template-columns: repeat(2, 1fr);
                        }
                    }
                </style>

                <script>
                document.addEventListener('DOMContentLoaded', function() {
                    const tabSection = document.getElementById('causesTabSection');

                    function showTab(tabNumber) {
                        // Update URL hash without scrolling
                        const activeTab = document.querySelector(`.tab-pill[data-tab="${tabNumber}"]`);
                        const tabTitle = activeTab?.getAttribute('data-title') || '';
                        const safeTitle = tabTitle.toLowerCase().replace(/[^a-z0-9]+/g, '-');
                        history.replaceState(null, null, `#${safeTitle}`);

                        // Hide all tab contents with fade
                        document.querySelectorAll('.tab-content').forEach(content => {
                            content.classList.remove('active');
                            content.setAttribute('aria-hidden', 'true');
                        });

                        // Remove active class from all tabs
                        document.querySelectorAll('.tab-pill').forEach(tab => {
                            tab.classList.remove('active');
                            tab.setAttribute('aria-selected', 'false');
                        });

                        // Show selected tab content
                        const selectedContent = document.querySelector(`.tab-content[data-content="${tabNumber}"]`);
                        if (selectedContent) {
                            selectedContent.classList.add('active');
                            selectedContent.setAttribute('aria-hidden', 'false');
                        }

                        // Add active class to selected tab
                        const selectedTab = document.querySelector(`.tab-pill[data-tab="${tabNumber}"]`);
                        if (selectedTab) {
                            selectedTab.classList.add('active');
                            selectedTab.setAttribute('aria-selected', 'true');

                            // Ensure tab is visible in container
                            const container = selectedTab.parentElement;
                            container.scrollLeft = selectedTab.offsetLeft - (container.clientWidth / 2) + (selectedTab.clientWidth / 2);
                        }
                    }

                    // Handle keyboard navigation
                    tabSection.addEventListener('keydown', (e) => {
                        const currentTab = document.querySelector('.tab-pill.active');
                        if (!currentTab) return;

                        const tabs = Array.from(document.querySelectorAll('.tab-pill'));
                        const currentIndex = tabs.indexOf(currentTab);

                        if (e.key === 'ArrowRight' || e.key === 'ArrowDown') {
                            e.preventDefault();
                            const nextTab = tabs[(currentIndex + 1) % tabs.length];
                            showTab(nextTab.getAttribute('data-tab'));
                            nextTab.focus();
                        } else if (e.key === 'ArrowLeft' || e.key === 'ArrowUp') {
                            e.preventDefault();
                            const prevTab = tabs[(currentIndex - 1 + tabs.length) % tabs.length];
                            showTab(prevTab.getAttribute('data-tab'));
                            prevTab.focus();
                        }
                    });

                    // Handle URL hash on page load
                    function handleUrlHash() {
                        const hash = window.location.hash.slice(1);
                        if (hash) {
                            const tabs = Array.from(document.querySelectorAll('.tab-pill'));
                            const matchingTab = tabs.find(tab => {
                                const title = tab.getAttribute('data-title').toLowerCase().replace(/[^a-z0-9]+/g, '-');
                                return title === hash;
                            });

                            if (matchingTab) {
                                const tabNumber = matchingTab.getAttribute('data-tab');
                                showTab(tabNumber);
                                matchingTab.setAttribute('data-highlight', 'true');
                                setTimeout(() => matchingTab.removeAttribute('data-highlight'), 1000);
                            }
                        }
                    }

                    // Add click event listeners to tab pills
                    document.querySelectorAll('.tab-pill').forEach(tab => {
                        tab.addEventListener('click', function() {
                            const tabNumber = this.getAttribute('data-tab');
                            showTab(tabNumber);
                        });
                    });

                    // Add change event listener to mobile select
                    const tabSelect = document.getElementById('tabSelect');
                    if (tabSelect) {
                        tabSelect.addEventListener('change', function() {
                            showTab(this.value);
                        });
                    }

                    // Initialize with URL hash or first tab
                    handleUrlHash() || showTab('1');

                    // Handle browser back/forward
                    window.addEventListener('popstate', handleUrlHash);
                });
                </script>

                <!-- Content -->
                <div class="tab-content-wrapper">
                    @foreach($bodyPart->activeCommonCauses as $cause)
                        <div class="tab-content {{ $loop->first ? 'active' : '' }}" data-content="{{ $loop->iteration }}">
                            <div class="row align-items-center">
                                @if($cause->image)
                                    <div class="col-lg-6 mb-4 mb-lg-0 {{ app()->getLocale() == 'ar' ? 'order-lg-2' : '' }}">
                                        <div class="cause-image">
                                            <img src="{{ $cause->imageUrl }}" alt="{{ $cause->{'title_' . app()->getLocale()} }}" class="img-fluid">
                                            <div class="cause-image-overlay"></div>
                                        </div>
                                    </div>
                                @endif
                                <div class="col-lg-{{ $cause->image ? '6' : '12' }} {{ app()->getLocale() == 'ar' ? 'order-lg-1' : '' }}">
                                    <div class="cause-content">
                                        <div class="cause-header">
                                            <span class="cause-number">{{ str_pad($loop->iteration, 2, '0', STR_PAD_LEFT) }}</span>
                                            <h3 class="{{ app()->getLocale() == 'ar' ? 'arabic-content' : '' }}">{{ $cause->{'title_' . app()->getLocale()} }}</h3>
                                        </div>
                                        <div class="cause-description {{ app()->getLocale() == 'ar' ? 'arabic-content' : '' }}">
                                            {!! $cause->{'description_' . app()->getLocale()} !!}
                                        </div>
                                        @if($cause->videos && count($cause->videos) > 0)
                                            <div class="cause-videos mt-4">
                                                @foreach($cause->videoUrls as $videoUrl)
                                                    <div class="video-wrapper mb-3">
                                                        <video controls class="w-100">
                                                            <source src="{{ $videoUrl }}" type="video/mp4">
                                                            Your browser does not support the video tag.
                                                        </video>
                                                    </div>
                                                @endforeach
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        @else
            <div class="alert alert-warning {{ app()->getLocale() == 'ar' ? 'arabic-content text-right' : '' }}">
                {{ __('messages.No common causes found for this body part.') }}
            </div>
        @endif
    </div>
</section>

<style>
    /* Add this at the top of your style section to enable RGB color usage */
    :root {
        --primary-color-rgb: 0, 123, 255; /* Replace with your primary color RGB values */
    }

    .causes-tabs {
        max-width: 1200px;
        margin: 0 auto;
    }

    .tabs-container {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        justify-content: center;
        padding: 1rem;
        margin-bottom: 3rem;
        position: relative;
        background: transparent;
    }

    .tab-pill {
        background: transparent;
        border: none;
        color: #666;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        transition: all 0.3s ease;
        cursor: pointer;
        position: relative;
        display: flex;
        align-items: center;
        gap: 0.75rem;
        border-radius: 30px;
        outline: 2px solid transparent;
    }

    .tab-pill:hover {
        color: var(--primary-color);
        outline: 2px solid var(--primary-color);
        outline-offset: -2px;
        background: rgba(var(--primary-color-rgb), 0.02);
    }

    .tab-pill.active {
        color: var(--primary-color);
        outline: 2px solid var(--primary-color);
        outline-offset: -2px;
        background: rgba(var(--primary-color-rgb), 0.05);
    }

    .tab-number {
        font-size: 0.875rem;
        color: currentColor;
        opacity: 0.7;
    }

    /* Remove the tab-line since we're using outline style */
    .tab-line {
        display: none;
    }

    .tab-content-wrapper {
        background: white;
        border-radius: 20px;
        padding: 3rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    }

    .tab-content {
        display: none;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .tab-content.active {
        display: block;
        opacity: 1;
    }

    .cause-image {
        position: relative;
        border-radius: 20px;
        overflow: hidden;
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }

    .cause-image:hover {
        transform: translateY(-5px);
    }

    .cause-image img {
        width: 100%;
        height: auto;
        object-fit: cover;
    }

    .cause-image-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(180deg, rgba(0,0,0,0) 0%, rgba(0,0,0,0.1) 100%);
    }

    .cause-header {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 1.5rem;
    }

    .cause-number {
        font-size: 2rem;
        font-weight: 700;
        color: var(--primary-color);
        opacity: 0.2;
    }

    .cause-content h3 {
        margin: 0;
        color: var(--primary-color);
        font-size: 1.75rem;
        font-weight: 700;
    }

    .cause-description {
        line-height: 1.8;
        color: #666;
        font-size: 1.1rem;
    }

    @media (max-width: 767px) {
        .tabs-wrapper-mobile .form-select {
            width: 100%;
            padding: 1rem;
            border-radius: 30px;
            border: none;
            outline: 2px solid var(--primary-color);
            font-weight: 600;
            color: var(--primary-color);
            background-color: white;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .tab-content-wrapper {
            padding: 1.5rem;
        }

        .cause-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }

        .cause-number {
            font-size: 1.5rem;
        }

        .cause-content h3 {
            font-size: 1.5rem;
        }
    }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Function to show tab
    function showTab(tabNumber) {
        // Hide all tab contents
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });

        // Remove active class from all tabs
        document.querySelectorAll('.tab-pill').forEach(tab => {
            tab.classList.remove('active');
        });

        // Show selected tab content
        const selectedContent = document.querySelector(`.tab-content[data-content="${tabNumber}"]`);
        if (selectedContent) {
            selectedContent.classList.add('active');
        }

        // Add active class to selected tab
        const selectedTab = document.querySelector(`.tab-pill[data-tab="${tabNumber}"]`);
        if (selectedTab) {
            selectedTab.classList.add('active');
        }
    }

    // Add click event listeners to tab pills
    document.querySelectorAll('.tab-pill').forEach(tab => {
        tab.addEventListener('click', function() {
            const tabNumber = this.getAttribute('data-tab');
            showTab(tabNumber);
        });
    });

    // Add change event listener to mobile select
    const tabSelect = document.getElementById('tabSelect');
    if (tabSelect) {
        tabSelect.addEventListener('change', function() {
            showTab(this.value);
        });
    }

    // Show first tab by default
    showTab('1');
});
</script>
<!-- Consultation Section -->
<section class="consultation-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="consultation-wrapper">
                    <div class="consultation-content text-center">
                        <h3 class="wow fadeInUp">{{ __('messages.Ready to Get Started?') }}</h3>
                        <p class="wow fadeInUp" data-wow-delay="0.1s">
                            {{ __('messages.Schedule your consultation or download detailed information') }}
                        </p>
                        <div class="consultation-buttons">
                            <a href="#" class="consultation-btn primary">
                                <span class="icon"><i class="fas fa-calendar-alt"></i></span>
                                <span class="text">
                                    <strong>{{ __('messages.Book Consultation') }}</strong>
                                    <small>{{ __('messages.Schedule Your Visit') }}</small>
                                </span>
                            </a>
                            <a href="#" class="consultation-btn secondary">
                            {{-- <a href="{{ route('download.info', ['part' => 'neck', 'locale' => app()->getLocale()]) }}" class="consultation-btn secondary"> --}}
                                <span class="icon"><i class="fas fa-download"></i></span>
                                <span class="text">
                                    <strong>{{ __('messages.Download Info') }}</strong>
                                    <small>{{ __('messages.Get Detailed Information') }}</small>
                                </span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
    .consultation-section {
        padding: 80px 0;
        background: linear-gradient(to bottom, rgba(var(--primary-color-rgb), 0.03), rgba(var(--primary-color-rgb), 0.08));
        margin-top: 50px;
    }

    .consultation-wrapper {
        background: var(--white-color);
        border-radius: 20px;
        padding: 50px;
        box-shadow: 0 15px 40px rgba(var(--primary-color-rgb), 0.08);
    }

    .consultation-content h3 {
        color: var(--primary-color);
        font-size: 2rem;
        margin-bottom: 15px;
        font-weight: 600;
    }

    .consultation-content p {
        color: var(--text-color);
        font-size: 1.1rem;
        margin-bottom: 40px;
        opacity: 0.8;
    }

    .consultation-buttons {
        display: flex;
        gap: 20px;
        justify-content: center;
        flex-wrap: wrap;
    }

    .consultation-btn {
        display: flex;
        align-items: center;
        padding: 20px 30px;
        border-radius: 15px;
        min-width: 280px;
        transition: all 0.3s ease;
        border: 2px solid transparent;
    }

    .consultation-btn.primary {
        background: var(--primary-color);
        color: var(--white-color);
    }

    .consultation-btn.secondary {
        background: var(--white-color);
        border-color: var(--primary-color);
        color: var(--primary-color);
    }

    .consultation-btn .icon {
        font-size: 24px;
        margin-right: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 50px;
        height: 50px;
        border-radius: 12px;
        background: rgba(255, 255, 255, 0.1);
    }

    .consultation-btn.secondary .icon {
        background: rgba(var(--primary-color-rgb), 0.1);
    }

    .consultation-btn .text {
        text-align: left;
        display: flex;
        flex-direction: column;
    }

    .consultation-btn .text strong {
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 2px;
    }

    .consultation-btn .text small {
        font-size: 0.85rem;
        opacity: 0.9;
    }

    .consultation-btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 20px rgba(var(--primary-color-rgb), 0.15);
    }

    .consultation-btn.primary:hover {
        background: var(--primary-color-dark, #0056b3);
    }

    .consultation-btn.secondary:hover {
        background: rgba(var(--primary-color-rgb), 0.05);
    }

    @media (max-width: 768px) {
        .consultation-section {
            padding: 40px 0;
        }

        .consultation-wrapper {
            padding: 30px 20px;
        }

        .consultation-content h3 {
            font-size: 1.75rem;
        }

        .consultation-btn {
            width: 100%;
            min-width: auto;
        }

        .consultation-buttons {
            flex-direction: column;
            gap: 15px;
        }
    }
</style>
@endsection



























