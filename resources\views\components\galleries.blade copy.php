@php
    $galleries = \App\Models\Gallery::where('is_active', true)
        ->orderBy('display_order')
        ->get();
@endphp

<!-- Galleries Section Start -->
<div class="our-gallery bg-radius-section mt-100">
    <div class="container">
        <!-- Section Title Start -->
        <div class="section-title mb-4">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <span class="subtitle wow fadeInUp">{{ __('messages.Our Gallery') }}</span>
                    <h2 class="title text-anime-style-2" data-cursor="-opaque">{{ __('messages.Latest Media') }}</h2>
                </div>
                <div class="col-lg-6">
                    <div class="section-right-btn text-lg-end">
                        <a href="{{ url(app()->getLocale() . '/Galleries') }}" class="btn-default">
                            <span>{{ __('messages.View All Gallery') }}</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <!-- Section Title End -->

        <!-- Gallery Slider Start -->
        <div class="row">
            <div class="col-12">
                <div class="gallery-slider">
                    <div class="swiper">
                        <div class="swiper-wrapper">
                            @forelse($galleries as $gallery)
                                <div class="swiper-slide">
                                    <div class="gallery-item wow fadeInUp" data-wow-delay="{{ $loop->iteration * 0.2 }}s">
                                        <div class="post-featured-image">
                                            <figure>
                                                @if($gallery->type === 'image')
                                                    <a href="{{ asset($gallery->getMediaUrl()) }}"
                                                       data-fancybox="gallery"
                                                       data-caption="{{ app()->getLocale() === 'en' ? $gallery->title_en : $gallery->title_ar }}"
                                                       class="image-popup">
                                                        <img src="{{ asset($gallery->getMediaUrl()) }}"
                                                             alt="{{ app()->getLocale() === 'en' ? $gallery->title_en : $gallery->title_ar }}"
                                                             loading="lazy">
                                                    </a>
                                                @else
                                                    <a href="{{ $gallery->media_url ?: asset('storage/gallery/videos/' . $gallery->media_file) }}"
                                                       data-fancybox="gallery"
                                                       data-type="video"
                                                       data-caption="{{ app()->getLocale() === 'en' ? $gallery->title_en : $gallery->title_ar }}"
                                                       class="video-popup">
                                                        <img src="{{ asset($gallery->getThumbnailUrl()) }}"
                                                             alt="{{ app()->getLocale() === 'en' ? $gallery->title_en : $gallery->title_ar }}"
                                                             loading="lazy">
                                                    </a>
                                                @endif
                                            </figure>
                                        </div>
                                        <div class="post-content">
                                            <h3 class="post-title">
                                                {{ app()->getLocale() === 'en' ? $gallery->title_en : $gallery->title_ar }}
                                            </h3>
                                        </div>
                                    </div>
                                </div>
                            @empty
                                <div class="col-12 text-center">
                                    <p>{{ __('messages.No Gallery Items') }}</p>
                                </div>
                            @endforelse
                        </div>
                        <div class="swiper-pagination" aria-label="{{ __('messages.Gallery Navigation') }}"></div>
                        <div class="swiper-button-next gallery-button-next"
                             aria-label="{{ __('messages.Next') }}"></div>
                        <div class="swiper-button-prev gallery-button-prev"
                             aria-label="{{ __('messages.Previous') }}"></div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Gallery Slider End -->
    </div>
</div>
<!-- Galleries Section End -->





