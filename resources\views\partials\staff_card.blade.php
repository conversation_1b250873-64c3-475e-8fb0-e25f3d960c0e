<div class="team-member-item">
    <div class="team-image small-image">
        <figure>
            @if($staff->photo)
                <img src="{{ $staff->photo }}"
                     alt="{{ app()->getLocale() == 'ar' ? ($staff->name_ar ?: $staff->name_en) : $staff->name_en }}"
                     loading="lazy"
                     onerror="this.onerror=null; this.src='https://ui-avatars.com/api/?background=random&name={{ urlencode($staff->name_en) }}';">
            @else
                <img src="https://ui-avatars.com/api/?background=random&name={{ urlencode($staff->name_en) }}"
                     alt="{{ app()->getLocale() == 'ar' ? ($staff->name_ar ?: $staff->name_en) : $staff->name_en }}"
                     loading="lazy">
            @endif
        </figure>
        <a href="{{ route('doctor.profile', ['locale' => app()->getLocale(), 'id' => $staff->id]) }}" class="view-profile-btn">
            <span>{{ __('messages.view_profile') }}</span>
            <i class="fas fa-arrow-right"></i>
        </a>
    </div>
    <div class="team-content">
        <h3>{{ app()->getLocale() == 'ar' ? ($staff->name_ar ?: $staff->name_en) : $staff->name_en }}</h3>
        <p>
            {{ app()->getLocale() == 'ar'
                ? ($staff->title_ar ?: ($staff->department->name_ar ?? __('messages.specialist')))
                : ($staff->title_en ?: ($staff->department->name_en ?? __('messages.specialist'))) }}
        </p>
    </div>
</div>
