<?php

namespace App\Services;

use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;

class StorageSyncService
{
    protected $sourcePath;
    protected $targetPath;

    // public function __construct()
    // {
    //     $this->sourcePath = storage_path('app/public');
    //     $this->targetPath = dirname(base_path()) . '/storage';
    // }

    // public function syncFile($path, $contents = null)
    // {
    //     try {
    //         // Normalize the path
    //         $path = str_replace('\\', '/', $path);
    //         $this->sourcePath = str_replace('\\', '/', $this->sourcePath);

    //         // Get relative path
    //         $relativePath = str_replace($this->sourcePath . '/', '', $path);
    //         $targetFile = $this->targetPath . '/' . $relativePath;

    //         Log::info('Attempting to sync file', [
    //             'source' => $path,
    //             'target' => $targetFile,
    //             'relative' => $relativePath
    //         ]);

    //         // Ensure target directory exists
    //         $targetDir = dirname($targetFile);
    //         if (!File::exists($targetDir)) {
    //             File::makeDirectory($targetDir, 0755, true);
    //         }

    //         // If contents provided, write directly
    //         if ($contents !== null) {
    //             File::put($targetFile, $contents);
    //             Log::info('File written with contents', ['target' => $targetFile]);
    //         }
    //         // Otherwise copy the file
    //         else if (File::exists($path)) {
    //             // Ensure we have read permissions
    //             chmod($path, 0644);

    //             // Copy the file
    //             File::copy($path, $targetFile);

    //             // Ensure proper permissions on the new file
    //             chmod($targetFile, 0644);

    //             Log::info('File copied successfully', [
    //                 'source' => $path,
    //                 'target' => $targetFile
    //             ]);
    //         } else {
    //             Log::warning('Source file does not exist', ['path' => $path]);
    //             return false;
    //         }

    //         return true;
    //     } catch (\Exception $e) {
    //         Log::error('Storage sync failed', [
    //             'error' => $e->getMessage(),
    //             'trace' => $e->getTraceAsString(),
    //             'path' => $path
    //         ]);
    //         return false;
    //     }
    // }

    // public function syncAllFiles()
    // {
    //     try {
    //         $files = File::allFiles($this->sourcePath);
    //         $count = 0;

    //         foreach ($files as $file) {
    //             $sourcePath = str_replace('\\', '/', $file->getRealPath());
    //             if ($this->syncFile($sourcePath)) {
    //                 $count++;
    //             }
    //         }

    //         Log::info("Synced {$count} files successfully");
    //         return true;
    //     } catch (\Exception $e) {
    //         Log::error('Full storage sync failed', [
    //             'error' => $e->getMessage(),
    //             'trace' => $e->getTraceAsString()
    //         ]);
    //         return false;
    //     }
    // }
}
