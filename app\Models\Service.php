<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\ViewField;
use FilamentTiptapEditor\TiptapEditor;
use FilamentTiptapEditor\Enums\TiptapOutput;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;

class Service extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'title_en',
        'title_ar',
        'slug',
        'description_en',
        'description_ar',
        'details_en',
        'details_ar',
        'icon',
        'featured_image',
        'attachments',
        'videos',
        'meta_title_en',
        'meta_title_ar',
        'meta_description_en',
        'meta_description_ar',
        'is_active',
        'display_order',
        'gallery_images',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'display_order' => 'integer',
        'attachments' => 'array',
        'videos' => 'array',
        'gallery_images' => 'array',
    ];

    public function getVideoUrlsAttribute()
    {
        return array_map(function($video) {
            $cleanPath = str_replace('services/videos/', '', $video);
            return url('/storage/services/videos/' . $cleanPath);
        }, $this->videos ?? []);
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($service) {
            $service->slug = Str::slug($service->title_en);
        });

        static::updating(function ($service) {
            $service->slug = Str::slug($service->title_en);
        });
    }

    public static function getFormSchema(): array
    {
        return [
            Tabs::make('Service')
                ->tabs([
                    Tabs\Tab::make('Basic Information')
                        ->icon('heroicon-o-information-circle')
                        ->schema([
                            Grid::make()
                                ->schema([
                                    TextInput::make('title_en')
                                        ->label('Title (English)')
                                        ->required()
                                        ->maxLength(255)
                                        ->columnSpan(1),
                                    TextInput::make('title_ar')
                                        ->label('Title (Arabic)')
                                        ->required()
                                        ->placeholder('ادخل العنوان...')
                                        ->maxLength(255)
                                        ->extraAttributes([
                                            'dir' => 'rtl',
                                            'style' => 'text-align: right'
                                        ])
                                        ->columnSpan(1),
                                ])
                                ->columns(2)
                                ->columnSpan('full'),
                            Grid::make()
                                ->schema([
                                    TiptapEditor::make('description_en')
                                        ->label('Description (English)')
                                        ->required()
                                        ->profile('default')
                                        ->extraInputAttributes(['style' => 'min-height: 500px'])
                                        ->disk('public')
                                        ->directory('services/media')
                                        ->acceptedFileTypes([
                                            'video/mp4',
                                            'video/webm',
                                            'image/jpeg',
                                            'image/jpg',
                                            'image/png',
                                            'image/gif',
                                            'image/webp',
                                            'image/svg+xml',
                                            'application/pdf'
                                        ])
                                        ->maxSize(204800)
                                        ->output(TiptapOutput::Html)
                                        ->columnSpan(1),
                                    TiptapEditor::make('description_ar')
                                        ->label('Description (Arabic)')
                                        ->required()
                                        ->profile('default')
                                        ->extraInputAttributes([
                                            'style' => 'min-height: 500px; direction: rtl; text-align: right;',
                                            'dir' => 'rtl'
                                        ])
                                        ->disk('public')
                                        ->directory('services/media')
                                        ->acceptedFileTypes([
                                            'video/mp4',
                                            'video/webm',
                                            'image/jpeg',
                                            'image/jpg',
                                            'image/png',
                                            'image/gif',
                                            'image/webp',
                                            'image/svg+xml',
                                            'application/pdf'
                                        ])
                                        ->maxSize(204800)
                                        ->output(TiptapOutput::Html)
                                        ->columnSpan(1),
                                ])
                                ->columns(2)
                                ->columnSpan('full'),
                        ])
                        ->columnSpan('full'),

                    Tabs\Tab::make('Media')
                        ->icon('heroicon-o-photo')
                        ->schema([
                            Grid::make()
                                ->schema([
                                    FileUpload::make('icon')
                                        ->label('Icon')
                                        ->image()
                                        ->directory('services/icons')
                                        ->columnSpan(1),
                                    FileUpload::make('featured_image')
                                        ->label('Featured Image')
                                        ->image()
                                        ->directory('services/featured')
                                        ->columnSpan(1),
                                ])
                                ->columns(2)
                                ->columnSpan('full'),

                            Grid::make(1)
                                ->schema([
                                    FileUpload::make('gallery_images')
                                        ->label('Gallery Images')
                                        ->multiple()
                                        ->image()
                                        ->directory('services/gallery')
                                        ->enableReordering()
                                        ->maxSize(5120)
                                        ->columnSpan('full'),
                                ])
                                ->columnSpan('full'),

                            Grid::make(1)
                                ->schema([
                                    FileUpload::make('videos')
                                        ->label('Upload Videos')
                                        ->multiple()
                                        ->directory('services/videos')
                                        ->acceptedFileTypes(['video/mp4', 'video/webm'])
                                        ->maxSize(202400),

                                    ViewField::make('video_urls')
                                        ->label('Video URLs')
                                        ->visible(fn ($record) => $record && $record->videos)
                                        ->view('components.video-urls-display')
                                        ->dehydrated(false)
                                ])
                                ->columnSpan('full'),
                        ])
                        ->columnSpan('full'),

                    Tabs\Tab::make('SEO')
                        ->icon('heroicon-o-magnifying-glass')
                        ->schema([
                            Grid::make()
                                ->schema([
                                    TextInput::make('meta_title_en')
                                        ->label('Meta Title (English)')
                                        ->maxLength(60)
                                        ->columnSpan(1),
                                    TextInput::make('meta_title_ar')
                                        ->label('Meta Title (Arabic)')
                                        ->maxLength(60)
                                        ->extraAttributes([
                                            'dir' => 'rtl',
                                            'style' => 'text-align: right'
                                        ])
                                        ->columnSpan(1),
                                    TextInput::make('meta_description_en')
                                        ->label('Meta Description (English)')
                                        ->maxLength(160)
                                        ->columnSpan(1),
                                    TextInput::make('meta_description_ar')
                                        ->label('Meta Description (Arabic)')
                                        ->maxLength(160)
                                        ->extraAttributes([
                                            'dir' => 'rtl',
                                            'style' => 'text-align: right'
                                        ])
                                        ->columnSpan(1),
                                ])
                                ->columns(2)
                                ->columnSpan('full'),
                        ])
                        ->columnSpan('full'),

                    Tabs\Tab::make('Settings')
                        ->icon('heroicon-o-cog')
                        ->schema([
                            Grid::make()
                                ->schema([
                                    Toggle::make('is_active')
                                        ->label('Active')
                                        ->default(true)
                                        ->columnSpan(1),
                                    TextInput::make('display_order')
                                        ->label('Display Order')
                                        ->numeric()
                                        ->default(0)
                                        ->columnSpan(1),
                                ])
                                ->columns(2)
                                ->columnSpan('full'),
                        ])
                        ->columnSpan('full'),
                ])
                ->columnSpan('full')
                ->persistTabInQueryString()
                ->contained(false),
        ];
    }
}







