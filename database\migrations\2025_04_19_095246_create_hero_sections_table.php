<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('hero_sections', function (Blueprint $table) {
            $table->id();

            // Page identifier
            $table->string('page_identifier')->unique(); // e.g., 'home', 'about', 'gallery'
            $table->string('page_name');

            // Content
            $table->string('title_en');
            $table->string('title_ar');
            $table->text('description_en')->nullable();
            $table->text('description_ar')->nullable();

            // Media
            $table->string('background_type')->default('image'); // image, video, youtube
            $table->json('background_images')->nullable();
            $table->json('background_videos')->nullable();
            $table->integer('slider_interval')->default(5000);
            $table->boolean('slider_autoplay')->default(true);
            $table->string('youtube_url')->nullable();

            // Button 1
            $table->string('button1_text_en')->nullable();
            $table->string('button1_text_ar')->nullable();
            $table->string('button1_url')->nullable();

            // Button 2
            $table->string('button2_text_en')->nullable();
            $table->string('button2_text_ar')->nullable();
            $table->string('button2_url')->nullable();

            // Settings
            $table->boolean('is_active')->default(true);

            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('hero_sections');
    }
};
