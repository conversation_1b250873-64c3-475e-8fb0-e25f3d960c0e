<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('common_causes', function (Blueprint $table) {
            $table->id();

            // Foreign key to body_parts table
            $table->foreignId('body_part_id')
                ->constrained()
                ->onDelete('cascade');

            // Basic Information
            $table->string('title_en');
            $table->string('title_ar')->nullable();
            $table->text('description_en')->nullable();
            $table->text('description_ar')->nullable();

            // Media
            $table->string('image')->nullable();

            // Status and Order
            $table->boolean('is_active')->default(true);
            $table->integer('display_order')->default(0);

            // Meta Information
            $table->string('meta_title_en')->nullable();
            $table->string('meta_title_ar')->nullable();
            $table->text('meta_description_en')->nullable();
            $table->text('meta_description_ar')->nullable();

            // File Attachments
            $table->json('attachments')->nullable();
            $table->json('videos')->nullable();

            // Timestamps
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('common_causes');
    }
};
