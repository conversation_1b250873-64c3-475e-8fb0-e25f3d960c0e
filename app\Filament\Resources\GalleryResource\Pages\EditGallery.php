<?php

namespace App\Filament\Resources\GalleryResource\Pages;

use App\Filament\Resources\GalleryResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditGallery extends EditRecord
{
    protected static string $resource = GalleryResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make()
                ->requiresConfirmation()
                ->modalHeading('Delete Gallery Item')
                ->modalDescription('Are you sure you want to delete this gallery item? This action cannot be undone.')
                ->modalSubmitActionLabel('Yes, delete it')
                ->modalCancelActionLabel('No, cancel'),
        ];
    }
}

