@php
    $featuredStaff = \App\Models\Staff::with('department')
        ->where('is_active', true)
        ->orderBy('display_order')
        ->take(4)
        ->get();
@endphp

<!-- Therapist Team Section Start -->
<div class="therapist-team bg-radius-section" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
        <div class="container">
            <div class="row section-row align-items-center">
                <div class="col-lg-8">
                    <!-- Section Title Start -->
                    <div class="section-title">
                        <h3 class="wow fadeInUp">{{ __('messages.orthopedic_team') }}</h3>
                        <h2 class="{{ app()->getLocale() === 'ar' ? 'arabic-title' : 'text-anime-style-3' }}" data-cursor="-opaque">{{ __('messages.dedicated_team') }}</h2>
                    </div>
                    <!-- Section Title End -->
                </div>

                <div class="col-lg-4">
                    <!-- Section Btn Start -->
                    <div class="section-btn wow fadeInUp">
                        <a href="{{ route('doctors', app()->getLocale()) }}" class="btn-default"><span>{{ __('messages.view_all_team') }}</span></a>
                    </div>
                    <!-- Section Btn End -->
                </div>
            </div>

            <div class="row">
                @foreach($featuredStaff as $index => $doctor)
                <div class="col-lg-3 col-md-6">
                    <!-- Team Member Item Start -->
                    <div class="team-member-item wow fadeInUp" @if($index > 0) data-wow-delay="{{ $index * 0.25 }}s" @endif>
                        <!-- Team Image Start -->
                        <div class="team-image">
                            <figure class="image-anime">
                                @if($doctor->photo)
                                    <img src="{{ $doctor->photo }}"
                                         alt="{{ app()->getLocale() == 'ar' ? ($doctor->name_ar ?: $doctor->name_en) : $doctor->name_en }}"
                                         loading="lazy"
                                         onerror="this.onerror=null; this.src='https://ui-avatars.com/api/?background=random&name={{ urlencode($doctor->name_en) }}';">
                                @else
                                    <img src="https://ui-avatars.com/api/?background=random&name={{ urlencode($doctor->name_en) }}"
                                         alt="{{ app()->getLocale() == 'ar' ? ($doctor->name_ar ?: $doctor->name_en) : $doctor->name_en }}"
                                         loading="lazy">
                                @endif
                            </figure>

                            <!-- View Profile Button Start -->
                            <a href="{{ route('doctor.profile', ['locale' => app()->getLocale(), 'id' => $doctor->id]) }}" class="view-profile-btn">
                                <span>{{ __('messages.view_profile') }}</span>
                                <i class="fas fa-arrow-right"></i>
                            </a>
                            <!-- View Profile Button End -->

                            <!-- Team Social Icon Start -->
                            <div class="team-social-icon">
                                <ul>
                                    @if($doctor->facebook_url)
                                    <li><a href="{{ $doctor->facebook_url }}" class="social-icon" target="_blank"><i class="fa-brands fa-facebook-f"></i></a></li>
                                    @else
                                        <li><a href="#" class="social-icon"><i class="fa-brands fa-facebook-f"></i></a></li>
                                    @endif
                                    @if($doctor->youtube_url)
                                        <li><a href="{{ $doctor->youtube_url }}" class="social-icon" target="_blank"><i class="fa-brands fa-youtube"></i></a></li>
                                    @else
                                        <li><a href="#" class="social-icon"><i class="fa-brands fa-youtube"></i></a></li>
                                    @endif
                                    @if($doctor->instagram_url)
                                        <li><a href="{{ $doctor->instagram_url }}" class="social-icon" target="_blank"><i class="fa-brands fa-instagram"></i></a></li>
                                    @else
                                        <li><a href="#" class="social-icon"><i class="fa-brands fa-instagram"></i></a></li>
                                    @endif
                                    @if($doctor->twitter_url)
                                        <li><a href="{{ $doctor->twitter_url }}" class="social-icon" target="_blank"><i class="fa-brands fa-x-twitter"></i></a></li>
                                    @else
                                        <li><a href="#" class="social-icon"><i class="fa-brands fa-x-twitter"></i></a></li>
                                    @endif
                                </ul>
                            </div>
                            <!-- Team Social Icon End -->
                        </div>
                        <!-- Team Image End -->

                        <!-- Team Content Start -->
                        <div class="team-content">
                            <h3>{{ app()->getLocale() == 'ar' ? ($doctor->name_ar ?: $doctor->name_en) : $doctor->name_en }}</h3>
                            <p>{{ app()->getLocale() == 'ar' ?
                                ($doctor->title_ar ?: ($doctor->department->name_ar ?? $doctor->title_en ?? $doctor->department->name_en ?? __('messages.specialist'))) :
                                ($doctor->title_en ?? $doctor->department->name_en ?? __('messages.specialist')) }}</p>
                        </div>
                        <!-- Team Content End -->
                    </div>
                    <!-- Team Member Item End -->
                </div>
                @endforeach
            </div>
        </div>
</div>
<!-- Therapist Team Section End -->

@push('styles')
<style>
    /* RTL styles for doctors section */
    [dir="rtl"] .team-content h3,
    [dir="rtl"] .team-content p,
    [dir="rtl"] .section-title h3,
    [dir="rtl"] .section-title h2 {
        text-align: right;
        font-family: var(--arabic-font);
    }

    [dir="rtl"] .section-btn {
        text-align: left;
    }

    /* Fix for Arabic title rendering */
    .arabic-title {
        font-family: var(--arabic-font) !important;
        letter-spacing: 0 !important;
        word-spacing: normal !important;
        text-align: right !important;
        animation: none !important;
        display: inline-block !important;
        white-space: nowrap !important;
        direction: rtl !important;
    }

    /* Adjust view profile button for RTL */
    [dir="rtl"] .view-profile-btn {
        flex-direction: row-reverse;
    }

    [dir="rtl"] .view-profile-btn i {
        transform: rotate(180deg);
        margin-right: 0;
        margin-left: 5px;
    }
</style>
@endpush
