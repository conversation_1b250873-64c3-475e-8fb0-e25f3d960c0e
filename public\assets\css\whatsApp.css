
    .whatsapp-container {
        position: fixed;
        bottom: 20px;
        left: 18px;
        z-index: 9999;
        display: flex;
        align-items: center;
    }

    .whatsapp-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 60px;
        height: 60px;
        background-color: #25D366; /* WhatsApp brand green */
        border-radius: 50%;
        box-shadow: 0 4px 12px rgba(37, 211, 102, 0.3);
        transition: all 0.3s ease;
        position: relative;
    }

    .whatsapp-btn img {
        width: 30px;
        height: 30px;
        transition: transform 0.3s ease;
    }

    /* Tooltip styling - appears to the right */
    .whatsapp-tooltip {
        position: absolute;
        left: calc(100% + 10px);
        top: 50%;
        transform: translateY(-50%);
        background: #333;
        color: white;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
        white-space: nowrap;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .whatsapp-btn:hover {
        transform: scale(1.1);
        box-shadow: 0 6px 16px rgba(37, 211, 102, 0.4);
    }

    .whatsapp-btn:hover img {
        transform: scale(1.1);
    }

    .whatsapp-btn:hover .whatsapp-tooltip {
        opacity: 1;
        visibility: visible;
        left: calc(100% + 15px);
    }

    /* Mobile responsiveness */
    @media (max-width: 768px) {
        .whatsapp-btn {
            width: 50px;
            height: 50px;
        }

        .whatsapp-btn img {
            width: 25px;
            height: 25px;
        }
    }

    @media (max-width: 480px) {
        .whatsapp-btn {
            width: 45px;
            height: 45px;
        }

        .whatsapp-btn img {
            width: 22px;
            height: 22px;
        }

        .whatsapp-tooltip {
            font-size: 11px;
            padding: 5px 10px;
        }
    }
