<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\TagsInput;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use FilamentTiptapEditor\TiptapEditor;
use FilamentTiptapEditor\Enums\TiptapOutput;

class BlogArticle extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'title_en',
        'title_ar',
        'slug',
        'content_en',
        'content_ar',
        'excerpt_en',
        'excerpt_ar',
        'category_en',
        'category_ar',
        'tags',
        'featured_image',
        'gallery_images',
        'attachments',
        'author_name_en',
        'author_name_ar',
        'author_title_en',
        'author_title_ar',
        'author_image',
        'medical_disclaimer_en',
        'medical_disclaimer_ar',
        'related_conditions',
        'references',
        'meta_title_en',
        'meta_title_ar',
        'meta_description_en',
        'meta_description_ar',
        'meta_keywords',
        'published_at',
        'is_featured',
        'is_active',
        'display_order',
        'reading_time',
        'view_count',
    ];

    protected $casts = [
        'tags' => 'array',
        'gallery_images' => 'array',
        'attachments' => 'array',
        'related_conditions' => 'array',
        'references' => 'array',
        'meta_keywords' => 'array',
        'is_featured' => 'boolean',
        'is_active' => 'boolean',
        'published_at' => 'datetime',
        'reading_time' => 'integer',
        'view_count' => 'integer',
        'display_order' => 'integer',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($article) {
            $article->slug = Str::slug($article->title_en);
        });

        static::updating(function ($article) {
            $article->slug = Str::slug($article->title_en);
        });
    }

    public function incrementViewCount()
    {
        $this->increment('view_count');
    }

    public static function getFormSchema(): array
    {
        return [
            Tabs::make('Blog Article')
                ->tabs([
                    Tabs\Tab::make('Basic Information')
                        ->schema([
                            Grid::make()
                                ->schema([
                                    TextInput::make('title_en')
                                        ->label('Title (English)')
                                        ->required()
                                        ->maxLength(255)
                                        ->live(onBlur: true)
                                        ->afterStateUpdated(fn ($state, callable $set) =>
                                            $set('meta_title_en', $state))
                                        ->columnSpan(1),

                                    TextInput::make('title_ar')
                                        ->label('Title (Arabic)')
                                        ->required()
                                        ->maxLength(255)
                                        ->live(onBlur: true)
                                        ->afterStateUpdated(fn ($state, callable $set) =>
                                            $set('meta_title_ar', $state))
                                        ->extraAttributes([
                                            'dir' => 'rtl',
                                            'style' => 'text-align: right'
                                        ])
                                        ->columnSpan(1),

                                    TiptapEditor::make('content_en')
                                        ->label('Content (English)')
                                        ->required()
                                        ->profile('default')
                                        ->extraInputAttributes(['style' => 'min-height: 500px'])
                                        ->disk('public')
                                        ->directory('blog/media')
                                        ->acceptedFileTypes([
                                            'video/mp4',
                                            'video/webm',
                                            'image/jpeg',
                                            'image/jpg',
                                            'image/png',
                                            'image/gif',
                                            'image/webp',
                                            'image/svg+xml',
                                            'application/pdf'
                                        ])
                                        ->maxSize(204800)
                                        ->output(TiptapOutput::Html)
                                        ->columnSpan(1),

                                    TiptapEditor::make('content_ar')
                                        ->label('Content (Arabic)')
                                        ->required()
                                        ->profile('default')
                                        ->extraInputAttributes([
                                            'style' => 'min-height: 500px; direction: rtl; text-align: right;',
                                            'dir' => 'rtl'
                                        ])
                                        ->disk('public')
                                        ->directory('blog/media')
                                        ->acceptedFileTypes([
                                            'video/mp4',
                                            'video/webm',
                                            'image/jpeg',
                                            'image/jpg',
                                            'image/png',
                                            'image/gif',
                                            'image/webp',
                                            'image/svg+xml',
                                            'application/pdf'
                                        ])
                                        ->maxSize(204800)
                                        ->output(TiptapOutput::Html)
                                        ->columnSpan(1),

                                    TextInput::make('excerpt_en')
                                        ->label('Excerpt (English)')
                                        ->maxLength(255)
                                        ->columnSpan(1),

                                    TextInput::make('excerpt_ar')
                                        ->label('Excerpt (Arabic)')
                                        ->maxLength(255)
                                        ->extraAttributes([
                                            'dir' => 'rtl',
                                            'style' => 'text-align: right'
                                        ])
                                        ->columnSpan(1),
                                ])
                                ->columns(2),
                        ]),

                    Tabs\Tab::make('Media')
                        ->schema([
                            FileUpload::make('featured_image')
                                ->label('Featured Image')
                                ->image()
                                ->directory('blog/featured-images'),

                            FileUpload::make('gallery_images')
                                ->label('Gallery Images')
                                ->multiple()
                                ->image()
                                ->directory('blog/gallery-images'),

                            FileUpload::make('attachments')
                                ->label('Attachments')
                                ->multiple()
                                ->directory('blog/attachments'),
                        ]),

                    Tabs\Tab::make('Categories & Tags')
                        ->schema([
                            TextInput::make('category_en')
                                ->label('Category (English)')
                                ->maxLength(255),

                            TextInput::make('category_ar')
                                ->label('Category (Arabic)')
                                ->maxLength(255)
                                ->extraAttributes([
                                    'dir' => 'rtl',
                                    'style' => 'text-align: right'
                                ]),

                            TagsInput::make('tags')
                                ->label('Tags')
                                ->separator(','),
                        ]),

                    Tabs\Tab::make('Author Information')
                        ->schema([
                            TextInput::make('author_name_en')
                                ->label('Author Name (English)')
                                ->maxLength(255),

                            TextInput::make('author_name_ar')
                                ->label('Author Name (Arabic)')
                                ->maxLength(255)
                                ->extraAttributes([
                                    'dir' => 'rtl',
                                    'style' => 'text-align: right'
                                ]),

                            TextInput::make('author_title_en')
                                ->label('Author Title (English)')
                                ->maxLength(255),

                            TextInput::make('author_title_ar')
                                ->label('Author Title (Arabic)')
                                ->maxLength(255)
                                ->extraAttributes([
                                    'dir' => 'rtl',
                                    'style' => 'text-align: right'
                                ]),

                            FileUpload::make('author_image')
                                ->label('Author Image')
                                ->image()
                                ->directory('blog/author-images'),
                        ]),

                    Tabs\Tab::make('Medical Information')
                        ->schema([
                            TiptapEditor::make('medical_disclaimer_en')
                                ->label('Medical Disclaimer (English)')
                                ->profile('default')
                                ->extraInputAttributes(['style' => 'min-height: 500px'])
                                ->disk('public')
                                ->directory('blog/media')
                                ->acceptedFileTypes([
                                    'video/mp4',
                                    'video/webm',
                                    'image/jpeg',
                                    'image/jpg',
                                    'image/png',
                                    'image/gif',
                                    'image/webp',
                                    'image/svg+xml',
                                    'application/pdf'
                                ])
                                ->maxSize(204800)
                                ->output(TiptapOutput::Html),

                            TiptapEditor::make('medical_disclaimer_ar')
                                ->label('Medical Disclaimer (Arabic)')
                                ->profile('default')
                                ->extraInputAttributes([
                                    'style' => 'min-height: 500px; direction: rtl; text-align: right;',
                                    'dir' => 'rtl'
                                ])
                                ->disk('public')
                                ->directory('blog/media')
                                ->acceptedFileTypes([
                                    'video/mp4',
                                    'video/webm',
                                    'image/jpeg',
                                    'image/jpg',
                                    'image/png',
                                    'image/gif',
                                    'image/webp',
                                    'image/svg+xml',
                                    'application/pdf'
                                ])
                                ->maxSize(204800)
                                ->output(TiptapOutput::Html),

                            TagsInput::make('related_conditions')
                                ->label('Related Conditions')
                                ->separator(','),

                            TagsInput::make('references')
                                ->label('References')
                                ->separator(','),
                        ]),

                    Tabs\Tab::make('SEO')
                        ->schema([
                            TextInput::make('meta_title_en')
                                ->label('Meta Title (English)')
                                ->maxLength(60),

                            TextInput::make('meta_title_ar')
                                ->label('Meta Title (Arabic)')
                                ->maxLength(60)
                                ->extraAttributes([
                                    'dir' => 'rtl',
                                    'style' => 'text-align: right'
                                ]),

                            TextInput::make('meta_description_en')
                                ->label('Meta Description (English)')
                                ->maxLength(160),

                            TextInput::make('meta_description_ar')
                                ->label('Meta Description (Arabic)')
                                ->maxLength(160)
                                ->extraAttributes([
                                    'dir' => 'rtl',
                                    'style' => 'text-align: right'
                                ]),

                            TagsInput::make('meta_keywords')
                                ->label('Meta Keywords')
                                ->separator(','),
                        ]),

                    Tabs\Tab::make('Publishing')
                        ->schema([
                            DateTimePicker::make('published_at')
                                ->label('Publish Date'),

                            Toggle::make('is_featured')
                                ->label('Featured Article')
                                ->default(false),

                            Toggle::make('is_active')
                                ->label('Active')
                                ->default(true),

                            TextInput::make('display_order')
                                ->label('Display Order')
                                ->numeric()
                                ->default(0),

                            TextInput::make('reading_time')
                                ->label('Reading Time (minutes)')
                                ->numeric()
                                ->default(5),
                        ]),
                ])
                ->columnSpan('full'),
        ];
    }
}




