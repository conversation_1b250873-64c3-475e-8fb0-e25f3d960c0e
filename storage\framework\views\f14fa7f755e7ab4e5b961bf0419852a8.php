<div class="team-member-item">
    <div class="team-image small-image">
        <figure>
            <?php if($staff->photo): ?>
                <img src="<?php echo e($staff->photo); ?>"
                     alt="<?php echo e(app()->getLocale() == 'ar' ? ($staff->name_ar ?: $staff->name_en) : $staff->name_en); ?>"
                     loading="lazy"
                     onerror="this.onerror=null; this.src='https://ui-avatars.com/api/?background=random&name=<?php echo e(urlencode($staff->name_en)); ?>';">
            <?php else: ?>
                <img src="https://ui-avatars.com/api/?background=random&name=<?php echo e(urlencode($staff->name_en)); ?>"
                     alt="<?php echo e(app()->getLocale() == 'ar' ? ($staff->name_ar ?: $staff->name_en) : $staff->name_en); ?>"
                     loading="lazy">
            <?php endif; ?>
        </figure>
        <a href="<?php echo e(route('doctor.profile', ['locale' => app()->getLocale(), 'id' => $staff->id])); ?>" class="view-profile-btn">
            <span><?php echo e(__('messages.view_profile')); ?></span>
            <i class="fas fa-arrow-right"></i>
        </a>
    </div>
    <div class="team-content">
        <h3><?php echo e(app()->getLocale() == 'ar' ? ($staff->name_ar ?: $staff->name_en) : $staff->name_en); ?></h3>
        <p>
            <?php echo e(app()->getLocale() == 'ar'
                ? ($staff->title_ar ?: ($staff->department->name_ar ?? __('messages.specialist')))
                : ($staff->title_en ?: ($staff->department->name_en ?? __('messages.specialist')))); ?>

        </p>
    </div>
</div>
<?php /**PATH C:\xampp\htdocs\Carefirst\alsharaf_orthopedics\resources\views/partials/staff_card.blade.php ENDPATH**/ ?>