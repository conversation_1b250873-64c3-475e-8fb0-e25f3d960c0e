<?php

namespace App\Models;

use App\Models\CommonCause;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\Actions;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\ViewField;
use FilamentTiptapEditor\TiptapEditor;
use FilamentTiptapEditor\Enums\TiptapOutput;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class BodyPart extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name_en',
        'name_ar',
        'slug',
        'description_en',
        'description_ar',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    /**
     * Get the common causes associated with this body part.
     */
    public function commonCauses()
    {
        return $this->hasMany(CommonCause::class);
    }

    /**
     * Get active common causes associated with this body part.
     */
    public function activeCommonCauses()
    {
        return $this->hasMany(CommonCause::class)->where('is_active', true)->orderBy('display_order');
    }

    /**
     * Get the body part's name based on current locale.
     */
    public function getNameAttribute()
    {
        $locale = app()->getLocale();
        return $locale === 'ar' ? $this->name_ar : $this->name_en;
    }

    /**
     * Get the body part's description based on current locale.
     */
    public function getDescriptionAttribute()
    {
        $locale = app()->getLocale();
        return $locale === 'ar' ? $this->description_ar : $this->description_en;
    }

    public static function getFormSchema()
    {
        return [
            Tabs::make('Body Part')
                ->tabs([
                    Tabs\Tab::make('Basic Information')
                        ->icon('heroicon-o-information-circle')
                        ->schema([
                            Grid::make(2)
                                ->schema([
                                    TextInput::make('name_en')
                                        ->label('Name (English)')
                                        ->required()
                                        ->maxLength(255),
                                    TextInput::make('name_ar')
                                        ->label('Name (Arabic)')
                                        ->required()
                                        ->placeholder('ادخل الاسم...')
                                        ->maxLength(255)
                                        ->extraAttributes([
                                            'dir' => 'rtl',
                                            'style' => 'text-align: right'
                                        ])
                                        ->default(null),
                                ]),
                            Grid::make(2)
                                ->schema([
                                    // FileUpload::make('video')
                                    // ->label('Upload Video')
                                    // ->directory('videos')
                                    // ->acceptedFileTypes(['video/mp4', 'video/webm'])
                                    // ->maxSize(20240)
                                    // ->helperText('After uploading, copy the video path and embed it using a <video> tag in the editor.'),

                                    RichEditor::make('description_en')
                                        ->label('Description (English)')
                                        ->required()
                                        ->toolbarButtons([
                                            'bold',
                                            'italic',
                                            'underline',
                                            'strike',
                                            'link',
                                            'orderedList',
                                            'unorderedList',
                                            'redo',
                                            'undo',
                                        ]),
                                    RichEditor::make('description_ar')
                                        ->label('Description (Arabic)')
                                        ->required()
                                        ->toolbarButtons([
                                            'bold',
                                            'italic',
                                            'underline',
                                            'strike',
                                            'link',
                                            'orderedList',
                                            'unorderedList',
                                            'redo',
                                            'undo'
                                        ])
                                        ->placeholder('اكتب الوصف هنا...')
                                        ->extraAttributes([
                                            'dir' => 'rtl',
                                            'style' => 'text-align: right'
                                        ]),
                                ]),
                        ]),

                    Tabs\Tab::make('Common Causes')
                        ->icon('heroicon-o-clipboard-document-list')
                        ->schema([
                            Repeater::make('commonCauses')
                                ->relationship()
                                ->collapsible()
                                ->collapsed()
                                ->itemLabel(function (array $state): string {
                                    return $state['title_en'] ?? 'New Common Cause';
                                })
                                ->schema([
                                    Grid::make(2)
                                        ->schema([
                                            TextInput::make('title_en')
                                                ->label('Title (English)')
                                                ->required()
                                                ->maxLength(255),
                                            TextInput::make('title_ar')
                                                ->label('Title (Arabic)')
                                                // ->required()
                                                ->placeholder('ادخل العنوان...')
                                                ->maxLength(255)
                                                ->extraAttributes([
                                                    'dir' => 'rtl',
                                                    'style' => 'text-align: right'
                                                ]),
                                        ]),
                                    Grid::make(1)
                                        ->schema([
                                                TiptapEditor::make('description_en')
                                                ->label('Description (English)')
                                                ->profile('default')
                                                ->extraInputAttributes(['style' => 'min-height: 500px'])
                                                ->disk('public')
                                                ->directory('common-causes/media')
                                                ->acceptedFileTypes([
                                                    'video/mp4',
                                                    'video/webm',
                                                    'image/jpeg',
                                                    'image/jpg',
                                                    'image/png',
                                                    'image/gif',
                                                    'image/webp',
                                                    'image/svg+xml',
                                                    'application/pdf'
                                                ])
                                                ->maxSize(204800)
                                                ->output(TiptapOutput::Html)
                                                ->configure([
                                                    'extensions' => [
                                                        'videoUpload' => [
                                                            'name' => 'video-upload',
                                                            'buttonLabel' => 'Insert Video',
                                                            'iconClass' => 'heroicon-o-video-camera',
                                                            'action' => <<<'JS'
                                                                const input = document.createElement('input');
                                                                input.type = 'file';
                                                                input.accept = 'video/mp4,video/webm';

                                                                input.onchange = async () => {
                                                                    const file = input.files[0];
                                                                    if (!file) return;

                                                                    const formData = new FormData();
                                                                    formData.append('file', file);

                                                                    try {
                                                                        const response = await fetch('/upload-tiny-video', {
                                                                            method: 'POST',
                                                                            headers: {
                                                                                'X-CSRF-TOKEN': document.querySelector('meta[name=csrf-token]').content
                                                                            },
                                                                            body: formData
                                                                        });

                                                                        const data = await response.json();

                                                                        if (data.location) {
                                                                            const videoHtml = '<video controls style="max-width: 100%"><source src="' + data.location + '" type="video/mp4">Your browser does not support the video tag.</video>';
                                                                            editor.commands.insertContent(videoHtml);
                                                                        }
                                                                    } catch (error) {
                                                                        console.error('Error:', error);
                                                                    }

                                                                    input.value = '';
                                                                };

                                                                input.click();
                                                            JS
                                                        ]
                                                    ],
                                                    'mediaUrls' => [
                                                        'storagePrefix' => '/storage/',
                                                    ],
                                                    'onUpdate' => <<<'JS'
                                                        function(props) {
                                                            if (props.src && !props.src.startsWith('/storage/')) {
                                                                props.src = '/storage/' + props.src.replace(/^\//, '');
                                                            }
                                                            return props;
                                                        }
                                                    JS
                                                ])
                                                ->afterStateUpdated(function ($state, $record) {
                                                    if (!$record || !is_array($state)) return;

                                                    $content = $state;

                                                    if (!is_string($content)) {
                                                        return;
                                                    }

                                                    // Ensure all media URLs have /storage prefix
                                                    $content = preg_replace(
                                                        [
                                                            '#(src=["\'])/?(common-causes/media)/#',
                                                            '#(src=["?\'])(common-causes/media)/#'
                                                        ],
                                                        '$1/storage/$2/',
                                                        $content
                                                    );

                                                    // Extract image URLs from content
                                                    preg_match_all('/<img[^>]*src=["\']([^"\']*)["\'][^>]*>/i', $content, $matches);

                                                    // Extract video sources
                                                    preg_match_all('/<video[^>]*>.*?<source[^>]*src=["\']([^"\']*)["\'][^>]*>.*?<\/video>/is', $content, $videoMatches);

                                                    // Get existing attachments
                                                    $uploadedVideos = $record->attachments ?? [];
                                                    $existingVideoUrls = array_filter($uploadedVideos, function($url) {
                                                        return str_contains($url, '/common-causes/media/');
                                                    });

                                                    // Combine all URLs
                                                    $allUrls = array_merge(
                                                        $matches[1] ?? [],
                                                        $videoMatches[1] ?? [],
                                                        $existingVideoUrls
                                                    );

                                                    // Process URLs to ensure correct format
                                                    $attachments = array_map(function($url) {
                                                        // Remove any duplicate /storage/ prefixes
                                                        $url = preg_replace('#(/storage/)+#', '/storage/', $url);

                                                        if (str_starts_with($url, 'http')) {
                                                            // For full URLs, ensure correct /storage/ path
                                                            return preg_replace(
                                                                '#(https?://[^/]+)(/storage/)?/?(common-causes/media)/#',
                                                                '$1/storage/$3/',
                                                                $url
                                                            );
                                                        }

                                                        // For relative paths
                                                        $path = preg_replace(
                                                            [
                                                                '#^/storage/#',
                                                                '#^storage/#',
                                                                '#^/common-causes/#',
                                                                '#^common-causes/#'
                                                            ],
                                                            '',
                                                            $url
                                                        );

                                                        return url('/storage/' . $path);
                                                    }, $allUrls);

                                                    // Update the record
                                                    $record->attachments = array_values(array_unique(array_filter($attachments)));
                                                    $record->description_en = $content;
                                                    $record->save();
                                                })
                                                ->statePath('description_en')
                                                ->configure([
                                                    'paragraph' => true,
                                                    'document' => [
                                                        'content' => 'block+',
                                                    ],
                                                ]),
                                                TiptapEditor::make('description_ar')
                                                    ->label('Description (Arabic)')
                                                    ->profile('default')
                                                    ->extraInputAttributes([
                                                        'style' => 'min-height: 500px; direction: rtl; text-align: right;',
                                                        'dir' => 'rtl'
                                                    ])
                                                    ->disk('public')
                                                    ->directory('common-causes/media')
                                                    ->acceptedFileTypes([
                                                        'video/mp4',
                                                        'video/webm',
                                                        'image/jpeg',
                                                        'image/jpg',
                                                        'image/png',
                                                        'image/gif',
                                                        'image/webp',
                                                        'image/svg+xml',
                                                        'application/pdf'
                                                    ])
                                                    ->maxSize(204800)
                                                    ->output(TiptapOutput::Html)
                                                    ->configure([
                                                        'extensions' => [
                                                            'videoUpload' => [
                                                                'name' => 'video-upload',
                                                                'buttonLabel' => 'إدراج فيديو',
                                                                'iconClass' => 'heroicon-o-video-camera',
                                                                'action' => <<<'JS'
                                                                    const input = document.createElement('input');
                                                                    input.type = 'file';
                                                                    input.accept = 'video/mp4,video/webm';

                                                                    input.onchange = async () => {
                                                                        const file = input.files[0];
                                                                        if (!file) return;

                                                                        const formData = new FormData();
                                                                        formData.append('file', file);

                                                                        try {
                                                                            const response = await fetch('/upload-tiny-video', {
                                                                                method: 'POST',
                                                                                headers: {
                                                                                    'X-CSRF-TOKEN': document.querySelector('meta[name=csrf-token]').content
                                                                                },
                                                                                body: formData
                                                                            });

                                                                            const data = await response.json();

                                                                            if (data.location) {
                                                                                const videoHtml = '<video controls style="max-width: 100%"><source src="' + data.location + '" type="video/mp4">المتصفح الخاص بك لا يدعم تشغيل الفيديو.</video>';
                                                                                editor.commands.insertContent(videoHtml);
                                                                            }
                                                                        } catch (error) {
                                                                            console.error('Error:', error);
                                                                        }

                                                                        input.value = '';
                                                                    };

                                                                    input.click();
                                                                JS
                                                            ]
                                                        ],
                                                        'mediaUrls' => [
                                                            'storagePrefix' => '/storage/',
                                                        ],
                                                        'onUpdate' => <<<'JS'
                                                            function(props) {
                                                                if (props.src && !props.src.startsWith('/storage/')) {
                                                                    props.src = '/storage/' + props.src.replace(/^\//, '');
                                                                }
                                                                return props;
                                                            }
                                                        JS
                                                    ])
                                                    ->afterStateUpdated(function ($state, $record) {
                                                        if (!$record || !is_array($state)) return;

                                                        $content = $state;

                                                        if (!is_string($content)) {
                                                            return;
                                                        }

                                                        // Ensure all media URLs have /storage prefix
                                                        $content = preg_replace(
                                                            [
                                                                '#(src=["\'])/?(common-causes/media)/#',
                                                                '#(src=["?\'])(common-causes/media)/#'
                                                            ],
                                                            '$1/storage/$2/',
                                                            $content
                                                        );

                                                        // Extract image URLs from content
                                                        preg_match_all('/<img[^>]*src=["\']([^"\']*)["\'][^>]*>/i', $content, $matches);

                                                        // Extract video sources
                                                        preg_match_all('/<video[^>]*>.*?<source[^>]*src=["\']([^"\']*)["\'][^>]*>.*?<\/video>/is', $content, $videoMatches);

                                                        // Get existing attachments
                                                        $uploadedVideos = $record->attachments ?? [];
                                                        $existingVideoUrls = array_filter($uploadedVideos, function($url) {
                                                            return str_contains($url, '/common-causes/media/');
                                                        });

                                                        // Combine all URLs
                                                        $allUrls = array_merge(
                                                            $matches[1] ?? [],
                                                            $videoMatches[1] ?? [],
                                                            $existingVideoUrls
                                                        );

                                                        // Process URLs to ensure correct format
                                                        $attachments = array_map(function($url) {
                                                            // Remove any duplicate /storage/ prefixes
                                                            $url = preg_replace('#(/storage/)+#', '/storage/', $url);

                                                            if (str_starts_with($url, 'http')) {
                                                                // For full URLs, ensure correct /storage/ path
                                                                return preg_replace(
                                                                    '#(https?://[^/]+)(/storage/)?/?(common-causes/media)/#',
                                                                    '$1/storage/$3/',
                                                                    $url
                                                                );
                                                            }

                                                            // For relative paths
                                                            $path = preg_replace(
                                                                [
                                                                    '#^/storage/#',
                                                                    '#^storage/#',
                                                                    '#^/common-causes/#',
                                                                    '#^common-causes/#'
                                                                ],
                                                                '',
                                                                $url
                                                            );

                                                            return url('/storage/' . $path);
                                                        }, $allUrls);

                                                        // Update the record
                                                        $record->attachments = array_values(array_unique(array_filter($attachments)));
                                                        $record->description_ar = $content;
                                                        $record->save();
                                                    })
                                                    ->statePath('description_ar')
                                                    ->configure([
                                                        'paragraph' => true,
                                                        'document' => [
                                                            'content' => 'block+',
                                                        ],
                                                    ]),
                                            // TinyEditor::make('description_en')
                                            //     ->language('en')
                                            //     ->maxHeight(700)
                                            //     ->showMenuBar()
                                            //     ->fileAttachmentsDisk('public')
                                            //     ->fileAttachmentsVisibility('public')
                                            //     ->fileAttachmentsDirectory('common-causes/media')
                                            //     ->afterStateUpdated(function ($state, $record) {
                                            //         // Extract image URLs from content
                                            //         preg_match_all('/<img[^>]*src=["\']([^"\']*)["\'][^>]*>/i', $state, $matches);

                                            //         // Extract video sources
                                            //         preg_match_all('/<video[^>]*>.*?<source[^>]*src=["\']([^"\']*)["\'][^>]*>.*?<\/video>/is', $state, $videoMatches);

                                            //         // Also get videos from FileUpload component if they exist
                                            //         $uploadedVideos = $record->attachments ?? [];
                                            //         $existingVideoUrls = array_filter($uploadedVideos, function($url) {
                                            //             return str_contains($url, '/common-causes/videos/');
                                            //         });

                                            //         // Combine all URLs
                                            //         $allUrls = array_merge(
                                            //             $matches[1],
                                            //             $videoMatches[1],
                                            //             $existingVideoUrls
                                            //         );

                                            //         // Store full URLs in attachments
                                            //         $attachments = array_map(function($url) {
                                            //             if (str_starts_with($url, 'http')) {
                                            //                 return $url;
                                            //             }

                                            //             $path = preg_replace(
                                            //                 [
                                            //                     '#^/storage/#',
                                            //                     '#^storage/#',
                                            //                     '#^/public/storage/#',
                                            //                     '#^public/storage/#'
                                            //                 ],
                                            //                 '',
                                            //                 $url
                                            //             );

                                            //             return url('/storage/' . $path);
                                            //         }, $allUrls);

                                            //         // Clean up the content
                                            //         $cleanContent = preg_replace(
                                            //             [
                                            //                 '#(src=["\'](https?://[^/]+))storage/#',
                                            //                 '#(src=["\']/?)public/storage/#',
                                            //                 '#(src=["\']https?://[^/]+)/public/storage/#',
                                            //                 '#(src=["\'])/public/storage/#',
                                            //                 '#(src=["?\'])public/storage/#'
                                            //             ],
                                            //             '$1/storage/',
                                            //             $state
                                            //         );

                                            //         if ($record) {
                                            //             $record->attachments = array_values(array_unique(array_filter($attachments)));
                                            //             $record->description_en = $cleanContent;
                                            //             $record->save();
                                            //         }
                                            //     })
                                            //     ->extraAttributes([
                                            //         'x-on:trix-attachment-add' => 'if (!$event.attachment.file) return',
                                            //         'x-on:trix-change' => '$dispatch("input", $event.target.value)',
                                            //     ]),
                                            // RichEditor::make('description_en')
                                            //     ->label('Description (English)')
                                            //     ->required()
                                            //     ->fileAttachmentsDisk('public')
                                            //     ->fileAttachmentsDirectory('common-causes/media')
                                            //     ->fileAttachmentsVisibility('public')
                                            //     ->toolbarButtons([
                                            //         'bold',
                                            //         'italic',
                                            //         'underline',
                                            //         'strike',
                                            //         'link',
                                            //         'orderedList',
                                            //         'unorderedList',
                                            //         'attachFiles',
                                            //         'alignLeft',
                                            //         'alignCenter',
                                            //         'alignRight',
                                            //         'redo',
                                            //         'undo'
                                            //     ])
                                            //     ->enableToolbarButtons([
                                            //         'attachFiles',
                                            //         'alignLeft',
                                            //         'alignCenter',
                                            //         'alignRight'
                                            //     ])
                                            //     ->afterStateHydrated(function ($component, $state, $record) {
                                            //         if ($record) {
                                            //             $component->state($record->getProcessedDescription('en'));
                                            //         }
                                            //     })
                                            //     ->afterStateUpdated(function ($state, $record) {
                                            //         if (!$record) return;

                                            //         // Extract image URLs from content
                                            //         preg_match_all('/<img[^>]*src=["\']([^"\']*)["\'][^>]*>/i', $state, $matches);

                                            //         // Get existing attachments
                                            //         $attachments = $record->attachments ?? [];

                                            //         // Filter out existing image URLs
                                            //         $attachments = array_filter($attachments, function($url) {
                                            //             return !str_contains($url, '/common-causes/media/');
                                            //         });

                                            //         // Process and store new image URLs
                                            //         foreach ($matches[1] as $url) {
                                            //             if (str_starts_with($url, 'http')) {
                                            //                 $attachments[] = $url;
                                            //             } else {
                                            //                 $path = preg_replace(
                                            //                     [
                                            //                         '#^/storage/#',
                                            //                         '#^storage/#',
                                            //                         '#^/public/storage/#',
                                            //                         '#^public/storage/#'
                                            //                     ],
                                            //                     '',
                                            //                     $url
                                            //                 );
                                            //                 $attachments[] = url('/storage/' . $path);
                                            //             }
                                            //         }

                                            //         // Clean up the content
                                            //         $cleanContent = preg_replace(
                                            //             [
                                            //                 '#(src=["\'](https?://[^/]+))storage/#',
                                            //                 '#(src=["\']/?)public/storage/#',
                                            //                 '#(src=["\']https?://[^/]+)/public/storage/#',
                                            //                 '#(src=["\'])/public/storage/#',
                                            //                 '#(src=["?\'])public/storage/#'
                                            //             ],
                                            //             '$1/storage/',
                                            //             $state
                                            //         );

                                            //         // Update the record
                                            //         $record->attachments = array_values(array_unique(array_filter($attachments)));
                                            //         $record->description_en = $cleanContent;
                                            //         $record->save();
                                            //     }),


                                            // RichEditor::make('description_ar')
                                            //     ->label('Description (Arabic)')
                                            //     // ->required()
                                            //     ->fileAttachmentsDisk('public')
                                            //     ->fileAttachmentsDirectory('common-causes/media')
                                            //     ->fileAttachmentsVisibility('public')
                                            //     ->toolbarButtons([
                                            //         'bold',
                                            //         'italic',
                                            //         'underline',
                                            //         'strike',
                                            //         'link',
                                            //         'orderedList',
                                            //         'unorderedList',
                                            //         'attachFiles',
                                            //         'redo',
                                            //         'undo'
                                            //     ])
                                            //     ->enableToolbarButtons([
                                            //         'attachFiles'
                                            //     ])
                                            //     ->afterStateHydrated(function ($component, $state, $record) {
                                            //         if ($record) {
                                            //             $component->state($record->getProcessedDescription('ar'));
                                            //         }
                                            //     })
                                            //     ->extraAttributes([
                                            //         'dir' => 'rtl',
                                            //         'style' => 'text-align: right'
                                            //     ]),
                                            // TinyEditor::make('description_ar')
                                            // // ->language('ar')
                                            // ->maxHeight(700)
                                            // ->showMenuBar()
                                            // ->fileAttachmentsDisk('public')
                                            // ->fileAttachmentsVisibility('public')
                                            // ->fileAttachmentsDirectory('common-causes/media')
                                            // ->afterStateUpdated(function ($state, $record) {
                                            //     // Extract image URLs from content
                                            //     preg_match_all('/<img[^>]*src=["\']([^"\']*)["\'][^>]*>/i', $state, $matches);

                                            //     // Extract video sources
                                            //     preg_match_all('/<video[^>]*>.*?<source[^>]*src=["\']([^"\']*)["\'][^>]*>.*?<\/video>/is', $state, $videoMatches);

                                            //     // Also get videos from FileUpload component if they exist
                                            //     $uploadedVideos = $record->attachments ?? [];
                                            //     $existingVideoUrls = array_filter($uploadedVideos, function($url) {
                                            //         return str_contains($url, '/common-causes/videos/');
                                            //     });

                                            //     // Combine all URLs
                                            //     $allUrls = array_merge(
                                            //         $matches[1],
                                            //         $videoMatches[1],
                                            //         $existingVideoUrls
                                            //     );

                                            //     // Store full URLs in attachments
                                            //     $attachments = array_map(function($url) {
                                            //         if (str_starts_with($url, 'http')) {
                                            //             return $url;
                                            //         }

                                            //         $path = preg_replace(
                                            //             [
                                            //                 '#^/storage/#',
                                            //                 '#^storage/#',
                                            //                 '#^/public/storage/#',
                                            //                 '#^public/storage/#'
                                            //             ],
                                            //             '',
                                            //             $url
                                            //         );

                                            //         return url('/storage/' . $path);
                                            //     }, $allUrls);

                                            //     // Clean up the content
                                            //     $cleanContent = preg_replace(
                                            //         [
                                            //             '#(src=["\'](https?://[^/]+))storage/#',
                                            //             '#(src=["\']/?)public/storage/#',
                                            //             '#(src=["\']https?://[^/]+)/public/storage/#',
                                            //             '#(src=["\'])/public/storage/#',
                                            //             '#(src=["?\'])public/storage/#'
                                            //         ],
                                            //         '$1/storage/',
                                            //         $state
                                            //     );

                                            //     if ($record) {
                                            //         $record->attachments = array_values(array_unique(array_filter($attachments)));
                                            //         $record->description_ar = $cleanContent; // Changed to description_ar
                                            //         $record->save();
                                            //     }
                                            // })
                                            // ->extraAttributes([
                                            //     'dir' => 'rtl',
                                            //     'style' => 'text-align: right',
                                            //     'x-on:trix-attachment-add' => 'if (!$event.attachment.file) return',
                                            //     'x-on:trix-change' => '$dispatch("input", $event.target.value)',
                                            // ])
                                            // ->label('Description (Arabic)')
                                            // ->placeholder('اكتب الوصف هنا...'),
                                        ]),
                                        Grid::make(1)
                                        ->schema([
                                            FileUpload::make('videos')
                                                ->label('Upload Videos')
                                                ->multiple()
                                                ->directory('common-causes/videos')
                                                ->acceptedFileTypes(['video/mp4', 'video/webm'])
                                                ->maxSize(202400),

                                            ViewField::make('video_urls')
                                                ->label('Video URLs')
                                                ->visible(fn ($record) => $record && $record->videos)
                                                ->view('components.video-urls-display')
                                                ->dehydrated(false) // This prevents the field from being included in form submission
                                        ]),
                                    Grid::make(2)
                                        ->schema([
                                            Toggle::make('is_active')
                                                ->label('Active')
                                                ->default(true),
                                            TextInput::make('display_order')
                                                ->label('Display Order')
                                                ->numeric()
                                                ->default(0),
                                        ]),
                                ])
                                ->orderColumn('display_order')
                                ->defaultItems(0)
                                ->createItemButtonLabel('Add Common Cause')
                                ->deleteAction(
                                    fn (Action $action) => $action
                                        ->requiresConfirmation()
                                        ->modalHeading('Delete Common Cause')
                                        ->modalDescription('Are you sure you want to delete this common cause? This action cannot be undone.')
                                        ->modalSubmitActionLabel('Yes, delete it')
                                        ->modalCancelActionLabel('No, cancel')
                                )
                        ]),
                ])
                ->columnSpanFull()
        ];
    }
}





















































































