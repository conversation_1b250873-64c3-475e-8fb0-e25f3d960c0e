<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Toggle;
use FilamentTiptapEditor\TiptapEditor; // Import the TiptapEditor component

class AboutUs extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'mission_en',
        'mission_ar',
        'vision_en',
        'vision_ar',
        'featured_image',
        'mission_icon',
        'vision_icon',
        'video_url',
        'meta_title_en',
        'meta_title_ar',
        'meta_description_en',
        'meta_description_ar',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    public static function getFormSchema(): array
    {
        return [
            Tabs::make('About Us')
                ->tabs([
                    Tabs\Tab::make('Mission')
                        ->icon('heroicon-o-light-bulb')
                        ->schema([
                            Grid::make()
                                ->schema([
                                    TiptapEditor::make('mission_en')
                                        ->label('Mission (English)')
                                        ->required()
                                        ->profile('default')
                                        ->columnSpan(1),
                                    TiptapEditor::make('mission_ar')
                                        ->label('Mission (Arabic)')
                                        ->required()
                                        ->profile('default')
                                        ->extraAttributes([
                                            'dir' => 'rtl',
                                            'style' => 'text-align: right'
                                        ])
                                        ->columnSpan(1),
                                ])
                                ->columns(2),
                        ]),

                    Tabs\Tab::make('Vision')
                        ->icon('heroicon-o-eye')
                        ->schema([
                            Grid::make()
                                ->schema([
                                    TiptapEditor::make('vision_en')
                                        ->label('Vision (English)')
                                        ->required()
                                        ->profile('default')
                                        ->columnSpan(1),
                                    TiptapEditor::make('vision_ar')
                                        ->label('Vision (Arabic)')
                                        ->required()
                                        ->profile('default')
                                        ->extraAttributes([
                                            'dir' => 'rtl',
                                            'style' => 'text-align: right'
                                        ])
                                        ->columnSpan(1),
                                ])
                                ->columns(2),
                        ]),

                    // Tabs\Tab::make('History & Values')
                    //     ->icon('heroicon-o-clock')
                    //     ->schema([
                    //         Grid::make()
                    //             ->schema([
                    //                 TiptapEditor::make('history_en')
                    //                     ->label('History (English)')
                    //                     ->required()
                    //                     ->profile('default')
                    //                     ->columnSpan(1),
                    //                 TiptapEditor::make('history_ar')
                    //                     ->label('History (Arabic)')
                    //                     ->required()
                    //                     ->profile('default')
                    //                     ->extraAttributes([
                    //                         'dir' => 'rtl',
                    //                         'style' => 'text-align: right'
                    //                     ])
                    //                     ->columnSpan(1),
                    //                 TiptapEditor::make('values_en')
                    //                     ->label('Values (English)')
                    //                     ->required()
                    //                     ->profile('default')
                    //                     ->columnSpan(1),
                    //                 TiptapEditor::make('values_ar')
                    //                     ->label('Values (Arabic)')
                    //                     ->required()
                    //                     ->profile('default')
                    //                     ->extraAttributes([
                    //                         'dir' => 'rtl',
                    //                         'style' => 'text-align: right'
                    //                     ])
                    //                     ->columnSpan(1),
                    //             ])
                    //             ->columns(2),
                    //     ]),

                    // Tabs\Tab::make('Facilities & Technology')
                    //     ->icon('heroicon-o-building-office')
                    //     ->schema([
                    //         Grid::make()
                    //             ->schema([
                    //                 TiptapEditor::make('facilities_description_en')
                    //                     ->label('Facilities Description (English)')
                    //                     ->required()
                    //                     ->profile('default')
                    //                     ->columnSpan(1),
                    //                 TiptapEditor::make('facilities_description_ar')
                    //                     ->label('Facilities Description (Arabic)')
                    //                     ->required()
                    //                     ->profile('default')
                    //                     ->extraAttributes([
                    //                         'dir' => 'rtl',
                    //                         'style' => 'text-align: right'
                    //                     ])
                    //                     ->columnSpan(1),
                    //                 TiptapEditor::make('technology_description_en')
                    //                     ->label('Technology Description (English)')
                    //                     ->required()
                    //                     ->profile('default')
                    //                     ->columnSpan(1),
                    //                 TiptapEditor::make('technology_description_ar')
                    //                     ->label('Technology Description (Arabic)')
                    //                     ->required()
                    //                     ->profile('default')
                    //                     ->extraAttributes([
                    //                         'dir' => 'rtl',
                    //                         'style' => 'text-align: right'
                    //                     ])
                    //                     ->columnSpan(1),
                    //             ])
                    //             ->columns(2),
                    //     ]),

                    Tabs\Tab::make('Media')
                        ->icon('heroicon-o-photo')
                        ->schema([
                            Grid::make()
                                ->schema([
                                    FileUpload::make('featured_image')
                                        ->label('Featured Image')
                                        ->image()
                                        ->directory('about-us/featured')
                                        ->columnSpan(1),
                                    FileUpload::make('mission_icon')
                                        ->label('Mission Icon')
                                        ->image()
                                        ->directory('about-us/icons')
                                        ->columnSpan(1),
                                    FileUpload::make('vision_icon')
                                        ->label('Vision Icon')
                                        ->image()
                                        ->directory('about-us/icons')
                                        ->columnSpan(1),
                                    TextInput::make('video_url')
                                        ->label('Video URL')
                                        ->url()
                                        ->columnSpan(1),
                                ])
                                ->columns(2),
                        ]),

                    Tabs\Tab::make('SEO')
                        ->icon('heroicon-o-magnifying-glass')
                        ->schema([
                            Grid::make()
                                ->schema([
                                    TextInput::make('meta_title_en')
                                        ->label('Meta Title (English)')
                                        ->maxLength(60)
                                        ->columnSpan(1),
                                    TextInput::make('meta_title_ar')
                                        ->label('Meta Title (Arabic)')
                                        ->maxLength(60)
                                        ->extraAttributes([
                                            'dir' => 'rtl',
                                            'style' => 'text-align: right'
                                        ])
                                        ->columnSpan(1),
                                    TextInput::make('meta_description_en')
                                        ->label('Meta Description (English)')
                                        ->maxLength(160)
                                        ->columnSpan(1),
                                    TextInput::make('meta_description_ar')
                                        ->label('Meta Description (Arabic)')
                                        ->maxLength(160)
                                        ->extraAttributes([
                                            'dir' => 'rtl',
                                            'style' => 'text-align: right'
                                        ])
                                        ->columnSpan(1),
                                ])
                                ->columns(2),
                        ]),

                    Tabs\Tab::make('Settings')
                        ->icon('heroicon-o-cog')
                        ->schema([
                            Toggle::make('is_active')
                                ->label('Active')
                                ->default(true),
                        ]),
                ])
                ->columnSpanFull()
                ->persistTabInQueryString()
        ];
    }
}


