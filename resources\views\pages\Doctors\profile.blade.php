@extends('layouts.app') <!-- Extend the base layout -->


@section('content')
    <!-- Page Header Start -->
	<div class="page-header team-page-single-header bg-radius-section parallaxie" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
		<div class="container">
			<div class="row align-items-center">
				<div class="col-lg-12">
					<!-- Page Header Box Start -->
					<div class="page-header-box">
						<h1 class="{{ app()->getLocale() === 'ar' ? 'arabic-title' : 'text-anime-style-2' }}" data-cursor="-opaque">{{ __('messages.Doctor Profile') }}</h1>
						<!-- Custom breadcrumb for RTL/LTR support -->
						@if(app()->getLocale() == 'ar')
							<!-- Arabic RTL breadcrumb -->
							<nav class="wow fadeInUp custom-rtl-breadcrumb">
								<div class="rtl-breadcrumb">
									<span style="color: #fff !important; font-size: 16px;">{{ app()->getLocale() === 'ar' ? ($doctor->name_ar ?? $doctor->name_en) : $doctor->name_en }}</span>
									<span class="separator" style="color: rgba(255, 255, 255, 0.7) !important; font-size: 16px;">/</span>
									<a href="{{ route('doctors', ['locale' => app()->getLocale()]) }}" style="color: rgba(255, 255, 255, 0.7) !important; text-decoration: none !important; font-size: 16px;">{{ __('messages.Doctors') }}</a>
									<span class="separator" style="color: rgba(255, 255, 255, 0.7) !important; font-size: 16px;">/</span>
									<a href="{{ url(app()->getLocale()) }}" style="color: rgba(255, 255, 255, 0.7) !important; text-decoration: none !important; font-size: 16px;">{{ __('messages.Home') }}</a>
								</div>
							</nav>
						@else
							<!-- English LTR breadcrumb -->
							<nav class="wow fadeInUp">
								<ol class="breadcrumb">
									<li class="breadcrumb-item"><a href="{{ url(app()->getLocale()) }}">{{ __('messages.Home') }}</a></li>
									<li class="breadcrumb-item"><a href="{{ route('doctors', ['locale' => app()->getLocale()]) }}">{{ __('messages.Doctors') }}</a></li>
									<li class="breadcrumb-item active" aria-current="page">{{ $doctor->name_en }}</li>
								</ol>
							</nav>
						@endif
					</div>
					<!-- Page Header Box End -->
				</div>
			</div>
		</div>
	</div>
	<!-- Page Header End -->

    <!-- Page Team Single Start -->
    <div class="page-team-single bg-radius-section" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-5">
                    <!-- Team Member Image Start -->
                    <div class="team-member-image">
                        <figure class="image-anime reveal">
                            @if($doctor->photo)
                                <img src="{{ $doctor->photo }}"
                                     alt="{{ app()->getLocale() === 'ar' ? ($doctor->name_ar ?? $doctor->name_en) : $doctor->name_en }}"
                                     loading="lazy"
                                     onerror="this.onerror=null; this.src='https://ui-avatars.com/api/?background=random&name={{ urlencode($doctor->name_en) }}';">
                            @else
                                <img src="https://ui-avatars.com/api/?background=random&name={{ urlencode($doctor->name_en) }}"
                                     alt="{{ app()->getLocale() === 'ar' ? ($doctor->name_ar ?? $doctor->name_en) : $doctor->name_en }}"
                                     loading="lazy">
                            @endif
                        </figure>
                    </div>
                    <!-- Team Member Image End -->
                </div>

                <div class="col-lg-7">
                    <!-- Team Member Details Start -->
                    <div class="team-member-details">
                        <!-- Member Details Header Start -->
                        <div class="member-detail-header">
                            <!-- Member Details Title Start -->
                            <div class="member-detail-title">
                                <h2 class="{{ app()->getLocale() === 'ar' ? 'arabic-title' : 'text-anime-style-3' }}" data-cursor="-opaque">{{ app()->getLocale() === 'ar' ? ($doctor->name_ar ?? $doctor->name_en) : $doctor->name_en }}</h2>
                                <p class="wow fadeInUp">{{ app()->getLocale() === 'ar' ? ($doctor->department->name_ar ?? $doctor->department->name_en ?? __('messages.Specialist')) : ($doctor->department->name_en ?? __('messages.Specialist')) }}</p>
                            </div>
                            <!-- Member Details Title End -->

                            <!-- Member Social Links Start -->
                            <div class="member-social-links wow fadeInUp">
                                @if($doctor->facebook_url)
                                    <a href="{{ $doctor->facebook_url }}" target="_blank"><i class="fa-brands fa-facebook-f"></i></a>
                                @endif
                                @if($doctor->youtube_url)
                                    <a href="{{ $doctor->youtube_url }}" target="_blank"><i class="fa-brands fa-youtube"></i></a>
                                @endif
                                @if($doctor->instagram_url)
                                    <a href="{{ $doctor->instagram_url }}" target="_blank"><i class="fa-brands fa-instagram"></i></a>
                                @endif
                                @if($doctor->twitter_url)
                                    <a href="{{ $doctor->twitter_url }}" target="_blank"><i class="fa-brands fa-x-twitter"></i></a>
                                @endif
                            </div>
                            <!-- Member Social Links End -->
                        </div>
                        <!-- Member Details Header End -->

                        <!-- Member Details Content Start -->
                        <div class="member-detail-content">
                            <p>{{ app()->getLocale() === 'ar' ? ($doctor->description_ar ?? $doctor->description_en) : $doctor->description_en }}</p>
                        </div>
                        <!-- Member Details Content End -->

                        <!-- Member Details Body Start -->
                        <div class="member-detail-body">
                            <!-- Member Detail List Item Start -->
                            <div class="member-detail-list-item wow fadeInUp">
                                <div class="icon-box">
                                    <img src="{{ asset('assets/images/icon-member-detail-1.svg') }}" alt="">
                                </div>
                                <div class="member-detail-list-content">
                                    <h3>{{ __('messages.position') }}</h3>
                                    <p>{{ __('messages.Physiotherapist') }}</p>
                                </div>
                            </div>
                            <!-- Member Detail List Item End -->

                            <!-- Member Detail List Item Start -->
                            <div class="member-detail-list-item wow fadeInUp" data-wow-delay="0.25s">
                                <div class="icon-box">
                                    <img src="{{ asset('assets/images/icon-member-detail-2.svg') }}" alt="">
                                </div>
                                <div class="member-detail-list-content">
                                    <h3>{{ __('messages.experience') }}</h3>
                                    <p>{{ __('messages.years_experience', ['years' => '06']) }}</p>
                                </div>
                            </div>
                            <!-- Member Detail List Item End -->

                            <!-- Member Detail List Item Start -->
                            <div class="member-detail-list-item wow fadeInUp" data-wow-delay="0.5s">
                                <div class="icon-box">
                                    <img src="{{ asset('assets/images/icon-member-detail-3.svg') }}" alt="">
                                </div>
                                <div class="member-detail-list-content">
                                    <h3>{{ __('messages.email') }}</h3>
                                    <p>{{ $doctor->email ?? '<EMAIL>' }}</p>
                                </div>
                            </div>
                            <!-- Member Detail List Item End -->

                            <!-- Member Detail List Item Start -->
                            <div class="member-detail-list-item wow fadeInUp" data-wow-delay="0.75s">
                                <div class="icon-box">
                                    <img src="{{ asset('assets/images/icon-member-detail-4.svg') }}" alt="">
                                </div>
                                <div class="member-detail-list-content">
                                    <h3>{{ __('messages.phone') }}</h3>
                                    <p>{{ $doctor->phone ?? '(+0) 123 456 789' }}</p>
                                </div>
                            </div>
                            <!-- Member Detail List Item End -->
                        </div>
                        <!-- Member Details Body End -->
                    </div>
                    <!-- Team Member Details End -->
                </div>
                <!-- team member details end -->
            </div>
        </div>
    </div>
    <!-- Page Team Single End -->

    <!-- Team Details Section End -->

    <!-- About Icon Box List Start -->
    <div class="about-icon-box-list member-expertise bg-radius-section" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
        <div class="container">
            <div class="row section-row">
                <div class="col-lg-12">
                    <!-- Section Title Start -->
                    <div class="section-title">
                        <h3 class="wow fadeInUp {{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ __('messages.expertise') }}</h3>
                        <h2 class="{{ app()->getLocale() === 'ar' ? 'arabic-title' : 'text-anime-style-3' }}" data-cursor="-opaque">{{ __('messages.Our Expertise') }}</h2>
                    </div>
                    <!-- Section Title End -->
                </div>
            </div>

            <div class="row">
                <div class="col-lg-2 col-md-4 col-6">
                    <!-- About Icon List Item Start -->
                    <div class="about-icon-list-item wow fadeInUp">
                        <div class="icon-box">
                            <img src="{{ asset('assets/images/about-icon-list-item-1.svg') }}" alt="">
                        </div>
                        <div class="about-icon-list-content">
                            <h3 class="{{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ __('messages.sports rehabilitation') }}</h3>
                        </div>
                    </div>
                    <!-- About Icon List Item End -->
                </div>

                <div class="col-lg-2 col-md-4 col-6">
                    <!-- About Icon List Item Start -->
                    <div class="about-icon-list-item wow fadeInUp" data-wow-delay="0.25s">
                        <div class="icon-box">
                            <img src="{{ asset('assets/images/about-icon-list-item-2.svg') }}" alt="">
                        </div>
                        <div class="about-icon-list-content">
                            <h3 class="{{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ __('messages.backpain management') }}</h3>
                        </div>
                    </div>
                    <!-- About Icon List Item End -->
                </div>

                <div class="col-lg-2 col-md-4 col-6">
                    <!-- About Icon List Item Start -->
                    <div class="about-icon-list-item wow fadeInUp" data-wow-delay="0.5s">
                        <div class="icon-box">
                            <img src="{{ asset('assets/images/about-icon-list-item-3.svg') }}" alt="">
                        </div>
                        <div class="about-icon-list-content">
                            <h3 class="{{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ __('messages.orthopedic rehabilitation') }}</h3>
                        </div>
                    </div>
                    <!-- About Icon List Item End -->
                </div>

                <div class="col-lg-2 col-md-4 col-6">
                    <!-- About Icon List Item Start -->
                    <div class="about-icon-list-item wow fadeInUp" data-wow-delay="0.75s">
                        <div class="icon-box">
                            <img src="{{ asset('assets/images/about-icon-list-item-4.svg') }}" alt="">
                        </div>
                        <div class="about-icon-list-content">
                            <h3 class="{{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ __('messages.occupational therapy') }}</h3>
                        </div>
                    </div>
                    <!-- About Icon List Item End -->
                </div>

                <div class="col-lg-2 col-md-4 col-6">
                    <!-- About Icon List Item Start -->
                    <div class="about-icon-list-item wow fadeInUp" data-wow-delay="1s">
                        <div class="icon-box">
                            <img src="{{ asset('assets/images/about-icon-list-item-5.svg') }}" alt="">
                        </div>
                        <div class="about-icon-list-content">
                            <h3 class="{{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ __('messages.home physiotherapy') }}</h3>
                        </div>
                    </div>
                    <!-- About Icon List Item End -->
                </div>

                <div class="col-lg-2 col-md-4 col-6">
                    <!-- About Icon List Item Start -->
                    <div class="about-icon-list-item wow fadeInUp" data-wow-delay="1.25s">
                        <div class="icon-box">
                            <img src="{{ asset('assets/images/about-icon-list-item-6.svg') }}" alt="">
                        </div>
                        <div class="about-icon-list-content">
                            <h3 class="{{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ __('messages.pelvic rehabilitation') }}</h3>
                        </div>
                    </div>
                    <!-- About Icon List Item End -->
                </div>
            </div>
        </div>
    </div>
    <!-- About Icon Box List End -->

    <!-- Member Personal Information Start -->
     <div class="member-personal-info bg-radius-section" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <!-- Member Info Content Start -->
                    <div class="member-info-content">
                        <!-- Section Title Start -->
                        <div class="section-title">
                            <h3 class="wow fadeInUp {{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ __('messages.information') }}</h3>
                            <h2 class="{{ app()->getLocale() === 'ar' ? 'arabic-title' : 'text-anime-style-3' }}" data-cursor="-opaque">{{ __('messages.Personal Information') }}</h2>
                            <p class="wow fadeInUp {{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}" data-wow-delay="0.25s">{{ __('messages.personal_info_description') }}</p>
                        </div>
                        <!-- Section Title End -->

                        <!-- Member Info List Start -->
                        <div class="member-info-list wow fadeInUp" data-wow-delay="0.5s">
                            <ul class="{{ app()->getLocale() === 'ar' ? 'rtl-list' : '' }}">
                                <li>{{ __('messages.certification_1') }}</li>
                                <li>{{ __('messages.certification_2') }}</li>
                                <li>{{ __('messages.certification_3') }}</li>
                                <li>{{ __('messages.certification_4') }}</li>
                            </ul>
                        </div>
                        <!-- Member Info List End -->
                    </div>
                    <!-- Member Info Content End -->
                </div>

                <div class="col-lg-6">
                    <!-- Member Working Hour Start -->
                    <div class="member-working-hour">
                        <div class="member-working-hour-box">
                            <!-- Section Title Start -->
                            <div class="section-title">
                                <h3 class="wow fadeInUp {{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ __('messages.opening hours') }}</h3>
                                <h2 class="{{ app()->getLocale() === 'ar' ? 'arabic-title' : 'text-anime-style-3' }}" data-cursor="-opaque">{{ __('messages.Working hours') }}</h2>
                                <p class="wow fadeInUp {{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}" data-wow-delay="0.25s">{{ __('messages.working_hours_description') }}</p>
                            </div>
                            <!-- Section Title End -->

                            <!-- Member Working Hour List Start -->
                            <div class="member-working-hour-list wow fadeInUp" data-wow-delay="0.75s">
                                <ul class="{{ app()->getLocale() === 'ar' ? 'rtl-schedule' : '' }}">
                                    @if(isset($doctor) && $doctor->working_hours)
                                        @foreach($doctor->working_hours as $schedule)
                                            <li>
                                                <h3>{{ app()->getLocale() === 'ar' ? __('messages.days.' . strtolower($schedule['day'])) : ucfirst($schedule['day']) }}:</h3>
                                                <span></span>
                                                <p>
                                                    @if($schedule['status'] === 'open')
                                                        {{ date('h.i A', strtotime($schedule['start_time'])) }} - {{ date('h.i A', strtotime($schedule['end_time'])) }}
                                                    @else
                                                        {{ __('messages.Closed') }}
                                                    @endif
                                                </p>
                                            </li>
                                        @endforeach
                                    @else
                                        <li><h3>{{ app()->getLocale() === 'ar' ? __('messages.days.monday') : 'Monday' }}:</h3><span></span><p>08.00 AM - 08.00 PM</p></li>
                                        <li><h3>{{ app()->getLocale() === 'ar' ? __('messages.days.tuesday') : 'Tuesday' }}:</h3><span></span><p>08.00 AM - 08.00 PM</p></li>
                                        <li><h3>{{ app()->getLocale() === 'ar' ? __('messages.days.wednesday') : 'Wednesday' }}:</h3><span></span><p>08.00 AM - 08.00 PM</p></li>
                                        <li><h3>{{ app()->getLocale() === 'ar' ? __('messages.days.thursday') : 'Thursday' }}:</h3><span></span><p>08.00 AM - 08.00 PM</p></li>
                                        <li><h3>{{ app()->getLocale() === 'ar' ? __('messages.days.friday') : 'Friday' }}:</h3><span></span><p>{{ __('messages.Closed') }}</p></li>
                                        <li><h3>{{ app()->getLocale() === 'ar' ? __('messages.days.saturday') : 'Saturday' }}:</h3><span></span><p>08.00 AM - 08.00 PM</p></li>
                                        <li><h3>{{ app()->getLocale() === 'ar' ? __('messages.days.sunday') : 'Sunday' }}:</h3><span></span><p>08.00 AM - 08.00 PM</p></li>
                                    @endif
                                </ul>
                            </div>
                            <!-- Member Working Hour List End -->
                        </div>
                    </div>
                    <!-- Member Working Hour End -->
                </div>
            </div>
        </div>
     </div>
    <!-- Member Personal Information End -->

    <!-- Member Working History Start -->
    <div class="member-working-history bg-radius-section" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <!-- Member Contact Form Start -->
                    <div class="member-contact-form">
                        <!-- Section Title Start -->
                        <div class="section-title">
                            <h3 class="wow fadeInUp {{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ __('messages.appointment request') }}</h3>
                            <h2 class="{{ app()->getLocale() === 'ar' ? 'arabic-title' : 'text-anime-style-3' }}" data-cursor="-opaque">{{ __('messages.Schedule a Consultation') }}</h2>
                        </div>
                        <!-- Section Title End -->

                        <form id="contactForm" action="#" method="POST" data-toggle="validator" class="wow fadeInUp" data-wow-delay="0.5s">
                            <div class="row">
                                <div class="form-group col-md-6 mb-4">
                                    <input type="text" name="fname" class="form-control" id="fname" placeholder="{{ __('messages.First Name') }}" required>
                                    <div class="help-block with-errors"></div>
                                </div>

                                <div class="form-group col-md-6 mb-4">
                                    <input type="text" name="lname" class="form-control" id="lname" placeholder="{{ __('messages.Last Name') }}" required>
                                    <div class="help-block with-errors"></div>
                                </div>

                                <div class="form-group col-md-12 mb-4">
                                    <input type="email" name="email" class="form-control" id="email" placeholder="{{ __('messages.Email') }}" required>
                                    <div class="help-block with-errors"></div>
                                </div>

                                <div class="form-group col-md-12 mb-4">
                                    <input type="text" name="phone" class="form-control" id="phone" placeholder="{{ __('messages.Phone No') }}" required>
                                    <div class="help-block with-errors"></div>
                                </div>

                                <div class="form-group col-md-12 mb-4">
                                    <textarea name="message" class="form-control" id="message" rows="5" placeholder="{{ __('messages.Message') }}" required></textarea>
                                    <div class="help-block with-errors"></div>
                                </div>

                                <div class="col-lg-12">
                                    <div class="team-contact-form-btn">
                                        <button type="submit" class="btn-default"><span>{{ __('messages.submit now') }}</span></button>
                                        <div id="msgSubmit" class="h3 hidden"></div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <!-- Member Contact Form End -->
                </div>

                <div class="col-lg-6">
                    <!-- Member Working History Content Start -->
                    <div class="member-working-history-content">
                        <!-- Section Title Start -->
                        <div class="section-title">
                            <h3 class="wow fadeInUp {{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ __('messages.history') }}</h3>
                            <h2 class="{{ app()->getLocale() === 'ar' ? 'arabic-title' : 'text-anime-style-3' }}" data-cursor="-opaque">{{ __('messages.Working History') }}</h2>
                        </div>
                        <!-- Section Title End -->

                        <!-- Working History Item Start -->
                        <div class="working-history-item wow fadeInUp">
                            <div class="working-history-content">
                                <h3>2018 - 2019 : <span>{{ __('messages.Senior Orthopedic Surgeon') }}</span></h3>
                                <p class="{{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ __('messages.history_description_1') }}</p>
                            </div>
                        </div>
                        <!-- Working History Item End -->

                        <!-- Working History Item Start -->
                        <div class="working-history-item wow fadeInUp" data-wow-delay="0.25s">
                            <div class="working-history-content">
                                <h3>2020 - 2021 : <span>{{ __('messages.Spine Surgery Specialist') }}</span></h3>
                                <p class="{{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ __('messages.history_description_2') }}</p>
                            </div>
                        </div>
                        <!-- Working History Item End -->

                        <!-- Working History Item Start -->
                        <div class="working-history-item wow fadeInUp" data-wow-delay="0.5s">
                            <div class="working-history-content">
                                <h3>2022 - {{ __('messages.Present') }} : <span>{{ __('messages.Chief of Orthopedics') }}</span></h3>
                                <p class="{{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ __('messages.history_description_3') }}</p>
                            </div>
                        </div>
                        <!-- Working History Item End -->
                    </div>
                    <!-- Member Working History Content End -->
                </div>
            </div>
        </div>
    </div>
    <!-- Member Working History End -->

    <!-- Member Winning Award & Honor Start -->
    <div class="member-winning-award bg-radius-section" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
        <div class="container">
            <div class="row section-row">
                <div class="col-lg-12">
                    <!-- Section Title Start -->
                    <div class="section-title">
                        <h3 class="wow fadeInUp {{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ __('messages.recognition') }}</h3>
                        <h2 class="{{ app()->getLocale() === 'ar' ? 'arabic-title' : 'text-anime-style-3' }}" data-cursor="-opaque">{{ __('messages.Awards & Certifications') }}</h2>
                    </div>
                    <!-- Section Title End -->
                </div>
            </div>

            <div class="row">
                <!-- Winning Award Slider Start -->
                <div class="winning-award-slider">
                    <div class="swiper">
                        <div class="swiper-wrapper">
                            @if(isset($doctor) && $doctor->certificates)
                                @foreach($doctor->certificates as $certificate)
                                    <!-- Certificate Image Slide Start -->
                                    <div class="swiper-slide">
                                        <div class="winning-award-image">
                                            <figure>
                                                <a href="{{ Storage::url($certificate) }}"
                                                   data-fancybox="doctor-certificates"
                                                   data-caption="{{ app()->getLocale() === 'ar' ? __('messages.Certificate') : 'Certificate' }}"
                                                   data-cursor-text="{{ __('messages.View Certificate') }}">
                                                    <img src="{{ Storage::url($certificate) }}" alt="{{ __('messages.Certificate') }}">
                                                </a>
                                            </figure>
                                        </div>
                                    </div>
                                    <!-- Certificate Image Slide End -->
                                @endforeach
                            @endif

                            @if(isset($doctor) && $doctor->awards)
                                @foreach($doctor->awards as $award)
                                    <!-- Award Image Slide Start -->
                                    <div class="swiper-slide">
                                        <div class="winning-award-image">
                                            <figure>
                                                <a href="{{ Storage::url($award) }}"
                                                   data-fancybox="doctor-awards"
                                                   data-caption="{{ app()->getLocale() === 'ar' ? __('messages.Award') : 'Award' }}"
                                                   data-cursor-text="{{ __('messages.View Award') }}">
                                                    <img src="{{ Storage::url($award) }}" alt="{{ __('messages.Award') }}">
                                                </a>
                                            </figure>
                                        </div>
                                    </div>
                                    <!-- Award Image Slide End -->
                                @endforeach
                            @endif

                            @if((!isset($doctor) || (empty($doctor->certificates) && empty($doctor->awards))))
                                <!-- Fallback Image Slides -->
                                <div class="swiper-slide">
                                    <div class="winning-award-image">
                                        <figure>
                                            <img src="{{ asset('assets/images/winning-award-img-1.jpg') }}" alt="{{ __('messages.Award') }}">
                                        </figure>
                                    </div>
                                </div>
                                <div class="swiper-slide">
                                    <div class="winning-award-image">
                                        <figure>
                                            <img src="{{ asset('assets/images/winning-award-img-2.jpg') }}" alt="{{ __('messages.Award') }}">
                                        </figure>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
                <!-- Winning Award Slider End -->
            </div>
        </div>
    </div>
    <!-- Member Winning Award & Honor End -->

    <!-- Doctor Publications Start -->
    <div class="doctor-publications bg-radius-section" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
        <div class="container">
            <div class="row section-row">
                <div class="col-lg-12">
                    <!-- Section Title Start -->
                    <div class="section-title">
                        <h3 class="wow fadeInUp {{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ __('messages.research') }}</h3>
                        <h2 class="{{ app()->getLocale() === 'ar' ? 'arabic-title' : 'text-anime-style-3' }}" data-cursor="-opaque">{{ __('messages.Publications & Research') }}</h2>
                    </div>
                    <!-- Section Title End -->
                </div>
            </div>

            <div class="row">
                @if(isset($doctor) && !empty($doctor->publications_data))
                    @foreach($doctor->publications_data as $index => $publication)
                        <!-- Publication Item Start -->
                        <div class="col-lg-6 mb-4 wow fadeInUp" data-wow-delay="{{ $index * 0.15 }}s">
                            <div class="publication-item">
                                <div class="publication-icon">
                                    <i class="fas fa-file-alt"></i>
                                </div>
                                <div class="publication-content">
                                    <h3 class="{{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ $publication['title'] ?? __('messages.Publication Title') }}</h3>
                                    <div class="publication-meta">
                                        <span class="publication-journal {{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ $publication['journal'] ?? __('messages.Journal Name') }}</span>
                                        <span class="publication-date">{{ $publication['year'] ?? '2023' }}</span>
                                    </div>
                                    <p class="{{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ $publication['abstract'] ?? __('messages.Publication abstract') }}</p>
                                    @if(isset($publication['url']))
                                        <a href="{{ $publication['url'] }}" class="publication-link" target="_blank" rel="noopener noreferrer">
                                            {{ __('messages.Read Full Article') }} <i class="fas {{ app()->getLocale() === 'ar' ? 'fa-arrow-left' : 'fa-external-link-alt' }}"></i>
                                        </a>
                                    @elseif(isset($publication['file']))
                                        <a href="{{ Storage::url($publication['file']) }}" class="publication-link" target="_blank" rel="noopener noreferrer">
                                            {{ __('messages.View Publication') }} <i class="fas {{ app()->getLocale() === 'ar' ? 'fa-arrow-left' : 'fa-file-pdf' }}"></i>
                                        </a>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <!-- Publication Item End -->
                    @endforeach
                @elseif(isset($doctor) && !empty($doctor->publications))
                    @foreach($doctor->publications as $index => $publication)
                        <!-- Publication Item Start (Legacy Format) -->
                        <div class="col-lg-6 mb-4 wow fadeInUp" data-wow-delay="{{ $index * 0.15 }}s">
                            <div class="publication-item">
                                <div class="publication-icon">
                                    <i class="fas fa-file-alt"></i>
                                </div>
                                <div class="publication-content">
                                    <h3 class="{{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ $publication['title'] ?? __('messages.Publication Title') }}</h3>
                                    <div class="publication-meta">
                                        <span class="publication-journal {{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ $publication['journal'] ?? __('messages.Journal Name') }}</span>
                                        <span class="publication-date">{{ $publication['year'] ?? '2023' }}</span>
                                    </div>
                                    <p class="{{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ $publication['abstract'] ?? __('messages.Publication abstract') }}</p>
                                    @if(isset($publication['url']))
                                        <a href="{{ $publication['url'] }}" class="publication-link" target="_blank" rel="noopener noreferrer">
                                            {{ __('messages.Read Full Article') }} <i class="fas {{ app()->getLocale() === 'ar' ? 'fa-arrow-left' : 'fa-external-link-alt' }}"></i>
                                        </a>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <!-- Publication Item End -->
                    @endforeach
                @else
                    <!-- Fallback Publications -->
                    <!-- Publication Item Start -->
                    <div class="col-lg-6 mb-4 wow fadeInUp">
                        <div class="publication-item">
                            <div class="publication-icon">
                                <i class="fas fa-file-alt"></i>
                            </div>
                            <div class="publication-content">
                                <h3 class="{{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ __('messages.publication_title_1') }}</h3>
                                <div class="publication-meta">
                                    <span class="publication-journal {{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ __('messages.journal_name_1') }}</span>
                                    <span class="publication-date">2023</span>
                                </div>
                                <p class="{{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ __('messages.publication_abstract_1') }}</p>
                                <a href="#" class="publication-link">
                                    {{ __('messages.Read Full Article') }} <i class="fas {{ app()->getLocale() === 'ar' ? 'fa-arrow-left' : 'fa-external-link-alt' }}"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    <!-- Publication Item End -->

                    <!-- Publication Item Start -->
                    <div class="col-lg-6 mb-4 wow fadeInUp" data-wow-delay="0.15s">
                        <div class="publication-item">
                            <div class="publication-icon">
                                <i class="fas fa-file-alt"></i>
                            </div>
                            <div class="publication-content">
                                <h3 class="{{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ __('messages.publication_title_2') }}</h3>
                                <div class="publication-meta">
                                    <span class="publication-journal {{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ __('messages.journal_name_2') }}</span>
                                    <span class="publication-date">2022</span>
                                </div>
                                <p class="{{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ __('messages.publication_abstract_2') }}</p>
                                <a href="#" class="publication-link">
                                    {{ __('messages.Read Full Article') }} <i class="fas {{ app()->getLocale() === 'ar' ? 'fa-arrow-left' : 'fa-external-link-alt' }}"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    <!-- Publication Item End -->

                    <!-- Publication Item Start -->
                    <div class="col-lg-6 mb-4 wow fadeInUp" data-wow-delay="0.3s">
                        <div class="publication-item">
                            <div class="publication-icon">
                                <i class="fas fa-file-alt"></i>
                            </div>
                            <div class="publication-content">
                                <h3 class="{{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ __('messages.publication_title_3') }}</h3>
                                <div class="publication-meta">
                                    <span class="publication-journal {{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ __('messages.journal_name_3') }}</span>
                                    <span class="publication-date">2021</span>
                                </div>
                                <p class="{{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ __('messages.publication_abstract_3') }}</p>
                                <a href="#" class="publication-link">
                                    {{ __('messages.Read Full Article') }} <i class="fas {{ app()->getLocale() === 'ar' ? 'fa-arrow-left' : 'fa-external-link-alt' }}"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    <!-- Publication Item End -->

                    <!-- Publication Item Start -->
                    <div class="col-lg-6 mb-4 wow fadeInUp" data-wow-delay="0.45s">
                        <div class="publication-item">
                            <div class="publication-icon">
                                <i class="fas fa-file-alt"></i>
                            </div>
                            <div class="publication-content">
                                <h3 class="{{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ __('messages.publication_title_4') }}</h3>
                                <div class="publication-meta">
                                    <span class="publication-journal {{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ __('messages.journal_name_4') }}</span>
                                    <span class="publication-date">2020</span>
                                </div>
                                <p class="{{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ __('messages.publication_abstract_4') }}</p>
                                <a href="#" class="publication-link">
                                    {{ __('messages.Read Full Article') }} <i class="fas {{ app()->getLocale() === 'ar' ? 'fa-arrow-left' : 'fa-external-link-alt' }}"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    <!-- Publication Item End -->
                @endif
            </div>
        </div>
    </div>
    <!-- Doctor Publications End -->
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Initialize Fancybox for doctor certificates and awards
    Fancybox.bind("[data-fancybox='doctor-certificates'], [data-fancybox='doctor-awards']", {
        // Image options
        Image: {
            zoom: false,  // Disable initial zoom
            wheel: true,
            click: "toggleZoom",
            doubleClick: "toggleZoom",
            wheelLimit: 10,
            zoomFriction: 0.88,
            infinite: true,
        },

        Images: {
            Panzoom: {
                maxScale: 5,
                minScale: 1,  // Changed from 0.5 to 1
                initialScale: 1,  // Ensure initial scale is 1
            },
        },

        // Keyboard shortcuts
        keyboard: {
            Escape: "close",
            Delete: "close",
            Backspace: "close",
            PageUp: "next",
            PageDown: "prev",
            ArrowUp: "prev",
            ArrowDown: "next",
            ArrowRight: "next",
            ArrowLeft: "prev",
            "+"    : "zoomIn",
            "-"    : "zoomOut",
            "0"    : "toggleZoom",
            "1"    : "toggle1to1",
            "f"    : "fullscreen",
            "t"    : "Thumbs.toggle",  // Keyboard shortcut for thumbnails
            "s"    : "Slideshow.toggle",
            "r"    : "rotateCW",
        },

        // Caption settings
        caption: {
            type: "inner",
            position: "bottom",
        },

        // Loading indicator
        showLoading: true,
        spinnerTpl: '<div class="fancybox-spinner"></div>',

        // Error message
        errorTpl: '<div class="fancybox-error"><p>The requested content cannot be loaded. Please try again later.</p></div>',

        // Event handlers
        on: {
            "init": (fancybox) => {
                // Reset any existing Panzoom instance
                if (fancybox.Panzoom) {
                    fancybox.Panzoom.reset();
                }

                const clickedElement = fancybox.options.$trigger;
                if (clickedElement) {
                    const gallery = document.querySelectorAll('[data-fancybox="doctor-certificates"], [data-fancybox="doctor-awards"]');
                    const index = Array.from(gallery).indexOf(clickedElement);
                    if (index >= 0) {
                        fancybox.jumpTo(index);
                    }
                }
            },
            "reveal": (fancybox, slide) => {
                // Ensure proper initial state for each slide
                if (slide.$content) {
                    slide.$content.style.transform = '';
                    if (slide.Panzoom) {
                        slide.Panzoom.reset();
                    }
                }
            },
            "done": (fancybox, slide) => {
                // Reset zoom when slide is fully loaded
                if (slide.Panzoom) {
                    slide.Panzoom.reset();
                }
            },
            "closing": (fancybox) => {
                // Stop slideshow when closing
                if (fancybox.Slideshow) {
                    fancybox.Slideshow.stop();
                }
            },
            "Thumbs.toggle": (fancybox) => {
                // Optional: Add custom behavior when thumbnails are toggled
                console.log("Thumbnails toggled");
            },
        },

        // Info bar
        infobar: true,

        // Animation settings
        animated: true,
        showClass: "fancybox-zoomIn",
        hideClass: "fancybox-zoomOut",

        // Fullscreen API
        fullscreen: {
            autoStart: false,
        },

        // Hash navigation
        hash: false,

        // Focus handling
        focus: true,

        // Mobile settings
        touch: {
            vertical: true,
            momentum: true,
        },

        // Image protection
        protect: true,
    });

    // Add click handler for thumbnail toggle
    $(document).on('click', '[data-fancybox-toggle-thumbs]', function() {
        const fancybox = Fancybox.getInstance();
        if (fancybox && fancybox.Thumbs) {
            fancybox.Thumbs.toggle();
        }
    });
});
</script>
@endpush

@push('styles')
<style>
    /* Custom RTL breadcrumb styling with stronger color rules */
    .rtl-breadcrumb {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        padding: 0;
        margin: 0;
        list-style: none;
        font-family: var(--arabic-font);
    }

    /* Force white color for all elements in RTL breadcrumb */
    .rtl-breadcrumb a,
    .rtl-breadcrumb span,
    .custom-rtl-breadcrumb a,
    .custom-rtl-breadcrumb span {
        color: rgba(255, 255, 255, 0.7) !important;
        text-decoration: none !important;
    }

    /* Override any hover effects */
    .rtl-breadcrumb a:hover,
    .custom-rtl-breadcrumb a:hover {
        color: #fff !important;
    }

    /* Make active item full white */
    .rtl-breadcrumb span:first-child {
        color: #fff !important;
    }

    .rtl-breadcrumb .separator {
        margin: 0 0.5rem;
        color: rgba(255, 255, 255, 0.7) !important;
    }

    /* Override any Bootstrap or theme styles that might be affecting links */
    .page-header a,
    .page-header-box a,
    .page-header .rtl-breadcrumb a {
        color: rgba(255, 255, 255, 0.7) !important;
    }

    /* Ensure no blue color from default link styling */
    .page-header a:not(:hover),
    .page-header-box a:not(:hover) {
        color: rgba(255, 255, 255, 0.7) !important;
    }

    /* Responsive font size for Arabic breadcrumb */
    .rtl-breadcrumb a,
    .rtl-breadcrumb span {
        font-size: 16px !important;
    }

    /* Increase size on smaller screens */
    @media (max-width: 767px) {
        .rtl-breadcrumb a,
        .rtl-breadcrumb span {
            font-size: 18px !important;
        }
    }

    /* Further increase for very small screens */
    @media (max-width: 480px) {
        .rtl-breadcrumb a,
        .rtl-breadcrumb span {
            font-size: 20px !important;
        }
    }

    /* Fix for Arabic title rendering */
    .arabic-title {
        font-family: var(--arabic-font) !important;
        letter-spacing: 0 !important;
        word-spacing: normal !important;
        text-align: right !important;
        animation: none !important;
        display: inline-block !important;
        white-space: nowrap !important;
        direction: rtl !important;
    }

    /* RTL styles for expertise section */
    [dir="rtl"] .about-icon-box-list .section-title h3,
    [dir="rtl"] .about-icon-box-list .section-title h2 {
        text-align: right;
    }

    /* Fix for Arabic content in expertise boxes */
    .arabic-content {
        font-family: var(--arabic-font) !important;
        text-align: right !important;
        direction: rtl !important;
        display: block !important;
        width: 100% !important;
    }

    /* Ensure icon boxes maintain their layout in RTL mode */
    [dir="rtl"] .about-icon-list-item {
        text-align: center;
    }

    [dir="rtl"] .about-icon-list-item .icon-box {
        margin: 0 auto;
    }

    [dir="rtl"] .about-icon-list-content h3 {
        text-align: center;
    }

    /* RTL styles for personal information section */
    [dir="rtl"] .member-info-content .section-title h3,
    [dir="rtl"] .member-info-content .section-title h2,
    [dir="rtl"] .member-info-content .section-title p {
        text-align: right;
    }

    /* RTL styles for member info list */
    [dir="rtl"] .member-info-list ul.rtl-list {
        padding-right: 20px;
        padding-left: 0;
        text-align: right;
    }

    [dir="rtl"] .member-info-list ul.rtl-list li {
        text-align: right;
        font-family: var(--arabic-font);
    }

    /* RTL styles for working hours section */
    [dir="rtl"] .member-working-hour .section-title h3,
    [dir="rtl"] .member-working-hour .section-title h2,
    [dir="rtl"] .member-working-hour .section-title p {
        text-align: right;
    }

    /* RTL styles for working hours list */
    [dir="rtl"] .member-working-hour-list ul.rtl-schedule li {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        text-align: right;
    }

    [dir="rtl"] .member-working-hour-list ul.rtl-schedule li h3 {
        font-family: var(--arabic-font);
        text-align: right;
        margin-right: 0;
    }

    [dir="rtl"] .member-working-hour-list ul.rtl-schedule li span {
        flex-grow: 1;
        margin: 0 10px;
    }

    [dir="rtl"] .member-working-hour-list ul.rtl-schedule li p {
        text-align: left;
    }

    /* RTL styles for awards section */
    [dir="rtl"] .member-winning-award .section-title h3,
    [dir="rtl"] .member-winning-award .section-title h2 {
        text-align: right;
    }

    /* Ensure slider navigation works correctly in RTL mode */
    [dir="rtl"] .swiper-button-next,
    [dir="rtl"] .swiper-button-prev {
        transform: rotate(180deg);
    }

    /* Center images in both LTR and RTL modes */
    .winning-award-image {
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .winning-award-image figure {
        margin: 0 auto;
        text-align: center;
    }

    /* RTL styles for working history section */
    [dir="rtl"] .member-working-history .section-title h3,
    [dir="rtl"] .member-working-history .section-title h2 {
        text-align: right;
    }

    /* RTL styles for contact form */
    [dir="rtl"] .member-contact-form .form-control {
        text-align: right;
    }

    [dir="rtl"] .member-contact-form .team-contact-form-btn {
        text-align: right;
    }

    /* RTL styles for working history content */
    [dir="rtl"] .member-working-history-content .working-history-item {
        text-align: right;
    }

    [dir="rtl"] .member-working-history-content .working-history-content h3 {
        text-align: right;
    }

    [dir="rtl"] .member-working-history-content .working-history-content p {
        text-align: right;
    }

    /* Arabic font for content */
    [dir="rtl"] .arabic-content {
        font-family: var(--arabic-font);
    }

    [dir="rtl"] .arabic-title {
        font-family: var(--arabic-font);
        font-weight: bold;
    }

    /* RTL styles for publications section */
    [dir="rtl"] .doctor-publications .section-title h3,
    [dir="rtl"] .doctor-publications .section-title h2 {
        text-align: right;
    }

    /* RTL styles for publication items */
    [dir="rtl"] .publication-item {
        display: flex;
        flex-direction: row;
        text-align: right;
    }

    [dir="rtl"] .publication-icon {
        margin-right: 0;
        margin-left: 20px;
    }

    [dir="rtl"] .publication-content h3,
    [dir="rtl"] .publication-content p {
        text-align: right;
    }

    [dir="rtl"] .publication-meta {
        justify-content: flex-start;
    }

    [dir="rtl"] .publication-journal {
        margin-right: 0;
        margin-left: 15px;
    }

    [dir="rtl"] .publication-link {
        text-align: right;
    }

    /* Adjust icon spacing in RTL mode */
    [dir="rtl"] .publication-link i {
        margin-right: 0;
        margin-left: 5px;
    }
</style>
@endpush











