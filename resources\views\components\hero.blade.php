<!-- Hero Section Start -->
<div class="hero hero-slider-layout bg-radius-section" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
    <div class="swiper">
        <div class="swiper-wrapper">
            @if($heroSection && $heroSection->background_images)
                @foreach($heroSection->background_images as $image)
                <!-- Hero Slide Start -->
                <div class="swiper-slide">
                    <div class="hero-slide">
                        @if($image)
                        <div class="hero-slider-image">
                            <img src="{{ asset('storage/' . $image) }}" alt="{{ $heroSection->title_en }}">
                        </div>
                        @endif

                        <div class="container">
                            <div class="row align-items-center">
                                <div class="col-lg-7">
                                    <div class="hero-content">
                                        <div class="section-title">
                                            <h1 class="{{ app()->getLocale() === 'ar' ? 'arabic-title' : 'text-anime-style-3' }} text-white" data-cursor="-opaque">
                                                {!! app()->getLocale() === 'en' ? $heroSection->title_en : $heroSection->title_ar !!}
                                            </h1>
                                            <p class="wow fadeInUp text-white-75" data-wow-delay="0.25s">
                                                {!! app()->getLocale() === 'en' ? $heroSection->description_en : $heroSection->description_ar !!}
                                            </p>
                                        </div>

                                        <div class="hero-content-body wow fadeInUp" data-wow-delay="0.5s">
                                            @if($heroSection->button1_url)
                                                <a href="{{ $heroSection->button1_url }}" class="btn-default {{ app()->getLocale() === 'ar' ? 'btn-rtl' : '' }}">
                                                    <span>{{ app()->getLocale() === 'en' ? $heroSection->button1_text_en : $heroSection->button1_text_ar }}</span>
                                                </a>
                                            @endif
                                            @if($heroSection->button2_url)
                                                <a target="_blank" href="{{ $heroSection->button2_url }}" class="btn-default btn-highlighted {{ app()->getLocale() === 'ar' ? 'btn-rtl' : '' }}">
                                                    <span>{{ app()->getLocale() === 'en' ? $heroSection->button2_text_en : $heroSection->button2_text_ar }}</span>
                                                </a>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Hero Slide End -->
                @endforeach
            @endif

            @if($heroSection && $heroSection->background_videos)
                @foreach($heroSection->background_videos as $video)
                <!-- Hero Video Slide Start -->
                <div class="swiper-slide">
                    <div class="hero-slide">
                        @if($video)
                        <div class="hero-slider-video">
                            <video autoplay muted loop playsinline>
                                <source src="{{ asset('storage/' . $video) }}" type="video/mp4">
                            </video>
                        </div>
                        @endif

                        <div class="container">
                            <div class="row align-items-center">
                                <div class="col-lg-7">
                                    <div class="hero-content">
                                        <div class="section-title">
                                            <h1 class="{{ app()->getLocale() === 'ar' ? 'arabic-title' : 'text-anime-style-3' }} text-white" data-cursor="-opaque">
                                                {!! app()->getLocale() === 'en' ? $heroSection->title_en : $heroSection->title_ar !!}
                                            </h1>
                                            <p class="wow fadeInUp text-white-75" data-wow-delay="0.25s">
                                                {!! app()->getLocale() === 'en' ? $heroSection->description_en : $heroSection->description_ar !!}
                                            </p>
                                        </div>

                                        <div class="hero-content-body wow fadeInUp" data-wow-delay="0.5s">
                                            @if($heroSection->button1_url)
                                                <a href="{{ $heroSection->button1_url }}" class="btn-default {{ app()->getLocale() === 'ar' ? 'btn-rtl' : '' }}">
                                                    <span>{{ app()->getLocale() === 'en' ? $heroSection->button1_text_en : $heroSection->button1_text_ar }}</span>
                                                </a>
                                            @endif
                                            @if($heroSection->button2_url)
                                                <a target="_blank" href="{{ $heroSection->button2_url }}" class="btn-default btn-highlighted {{ app()->getLocale() === 'ar' ? 'btn-rtl' : '' }}">
                                                    <span>{{ app()->getLocale() === 'en' ? $heroSection->button2_text_en : $heroSection->button2_text_ar }}</span>
                                                </a>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Hero Video Slide End -->
                @endforeach
            @endif
        </div>
        @if($heroSection && $heroSection->slider_autoplay)
            <div class="hero-pagination"></div>
        @endif
    </div>
</div>
<!-- Hero Section End -->


@push('scripts')
<script>
// Add this to destroy any existing Swiper instance
document.addEventListener('DOMContentLoaded', function() {
    // Get the container element
    const swiperContainer = document.querySelector('.hero-slider-layout .swiper');

    // Destroy existing Swiper instance if it exists
    if (swiperContainer.swiper) {
        swiperContainer.swiper.destroy(true, true);
    }

    // Initialize our custom Swiper
    const heroSwiper = new Swiper('.hero-slider-layout .swiper', {
        slidesPerView: 1,
        speed: 1000,
        spaceBetween: 0,
        loop: true,
        autoplay: @json($heroSection && $heroSection->slider_autoplay ? [
            'delay' => $heroSection->slider_interval ?? 5000,
            'disableOnInteraction' => false,
            'pauseOnMouseEnter' => true
        ] : false),
        pagination: {
            el: '.hero-pagination',
            clickable: true,
        },
        // Ensure our instance takes precedence
        observer: true,
        observeParents: true
    });
});
</script>
@endpush

@push('styles')
<style>
    /* RTL styles for hero section */
    [dir="rtl"] .hero-content .section-title h1,
    [dir="rtl"] .hero-content .section-title p {
        text-align: right;
        font-family: var(--arabic-font);
    }

    /* Fix for Arabic text rendering */
    [dir="rtl"] .text-anime-style-3 {
        font-family: var(--arabic-font) !important;
        letter-spacing: normal !important; /* Remove letter spacing for Arabic */
        word-spacing: normal !important;
        text-align: right !important;
    }

    [dir="rtl"] .hero-content-body {
        text-align: right;
    }

    /* Complete button style override for RTL */
    [dir="rtl"] .btn-default {
        position: relative;
        display: inline-block;
        margin-right: 0;
        margin-left: 15px;
        padding: 2px 2px 2px 25px; /* Padding on left side */
        border-radius: 100px;
        overflow: hidden;
        z-index: 1;
    }

    /* Replace the arrow completely */
    [dir="rtl"] .btn-default::before {
        content: '\f104'; /* FontAwesome left arrow */
        font-family: 'Font Awesome 6 Free';
        font-weight: 900;
        position: absolute;
        top: 50%;
        right: auto;
        left: 15px;
        transform: translateY(-50%);
        z-index: 1;
    }

    /* Keep the text aligned properly */
    [dir="rtl"] .btn-default span {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding-right: 15px; /* Add padding for text */
    }

    [dir="rtl"] .btn-default:last-child {
        margin-left: 0;
    }

    /* Responsive adjustments for RTL */
    @media (max-width: 767px) {
        [dir="rtl"] .hero-content .section-title h1,
        [dir="rtl"] .hero-content .section-title p,
        [dir="rtl"] .hero-content-body {
            text-align: center;
        }

        [dir="rtl"] .btn-default {
            margin-left: 10px;
        }
    }

    /* Fix for Arabic title rendering */
    .arabic-title {
        font-family: var(--arabic-font) !important;
        letter-spacing: 0 !important;
        word-spacing: normal !important;
        text-align: right !important;
        animation: none !important;
        display: inline-block !important;
        white-space: nowrap !important;
        direction: rtl !important;
    }

    /* RTL button styles */
    .btn-rtl {
        direction: rtl;
        text-align: right;
    }

    /* Adjust arrow position for RTL buttons */
    .btn-rtl::before {
        right: auto;
        left: 15px;
        content: '\f104'; /* FontAwesome left arrow */
    }

    /* Adjust padding for RTL buttons */
    .btn-rtl span {
        padding-right: 15px;
        padding-left: 25px;
    }
</style>
@endpush



















