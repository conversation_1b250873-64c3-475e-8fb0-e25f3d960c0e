    <!-- Quality Treatment Section Start -->
    <div class="our-quality bg-radius-section">
        <div class="quality-treatment">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-8 order-md-1 {{ app()->getLocale() == 'ar' ? 'order-md-1' : 'order-md-1' }}">
                        <!-- Quality Treatment Content Start -->
                        <div class="quality-treatment-content" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
                            <!-- Section Title Start -->
                            <div class="section-title">
                                <h3 class="wow fadeInUp">{{ __('messages.specialized_care') }}</h3>
                                <h2 class="{{ app()->getLocale() === 'ar' ? 'arabic-title' : 'text-anime-style-3' }}" data-cursor="-opaque">{{ __('messages.advanced_orthopedic_treatment') }}</h2>
                                <p class="wow fadeInUp" data-wow-delay="0.25s">{{ __('messages.treatment_description') }}</p>
                            </div>
                            <!-- Section Title End -->

                            <!-- Quality Treatment Body Start -->
                            <div class="blue-bullet-list-container wow fadeInUp" data-wow-delay="0.5s">
                                @if(app()->getLocale() == 'ar')
                                    <ul class="blue-bullet-list rtl-bullet-list">
                                        <li>{{ __('messages.advanced_diagnostic') }}</li>
                                        <li>{{ __('messages.minimally_invasive') }}</li>
                                        <li>{{ __('messages.specialized_spine') }}</li>
                                        <li>{{ __('messages.personalized_rehab') }}</li>
                                        <li>{{ __('messages.sports_injury') }}</li>
                                        <li>{{ __('messages.complex_trauma') }}</li>
                                    </ul>
                                @else
                                    <ul class="blue-bullet-list">
                                        <li>{{ __('messages.advanced_diagnostic') }}</li>
                                        <li>{{ __('messages.minimally_invasive') }}</li>
                                        <li>{{ __('messages.specialized_spine') }}</li>
                                        <li>{{ __('messages.personalized_rehab') }}</li>
                                        <li>{{ __('messages.sports_injury') }}</li>
                                        <li>{{ __('messages.complex_trauma') }}</li>
                                    </ul>
                                @endif
                            </div>
                            <!-- Quality Treatment Body End -->

                            <!-- Quality Treatment Footer Start -->
                            <div class="quality-treatment-footer wow fadeInUp" data-wow-delay="0.75s">
                                <a href="#" class="btn-default {{ app()->getLocale() == 'ar' ? 'btn-rtl' : '' }}"><span>{{ __('messages.schedule_consultation') }}</span></a>
                            </div>
                            <!-- Quality Treatment Footer End -->
                        </div>
                        <!-- Quality Treatment Content End -->
                    </div>

                    <div class="col-lg-6 order-md-2 {{ app()->getLocale() == 'ar' ? 'order-md-2' : 'order-md-2' }}">
                        <!-- You can add an image here showing advanced orthopedic treatment or medical facility -->
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Quality Treatment Section End -->

@push('styles')
<style>
    /* Blue Bullet List - New Implementation */
    .blue-bullet-list-container {
        margin: 20px 0;
    }

    .blue-bullet-list {
        list-style: none !important;
        padding-left: 0 !important;
        margin-left: 0 !important;
    }

    .blue-bullet-list li {
        position: relative !important;
        padding-left: 25px !important;
        margin-bottom: 12px !important;
        font-size: 16px !important;
        color: var(--primary-color) !important;
        line-height: 1.5 !important;
    }

    .blue-bullet-list li:before {
        content: "" !important;
        position: absolute !important;
        left: 0 !important;
        top: 7px !important;
        width: 10px !important;
        height: 10px !important;
        background-color: #023047 !important;
        border-radius: 50% !important;
    }

    /* RTL support for Arabic */
    .rtl-bullet-list {
        padding-right: 0 !important;
        padding-left: 0 !important;
        margin-right: 0 !important;
    }

    .rtl-bullet-list li {
        padding-left: 0 !important;
        padding-right: 25px !important;
        text-align: right !important;
    }

    .rtl-bullet-list li:before {
        left: auto !important;
        right: 0 !important;
    }

    /* Mobile responsive adjustments */
    @media (max-width: 768px) {
        .blue-bullet-list li {
            font-size: 14px !important;
            padding-left: 20px !important;
        }

        .rtl-bullet-list li {
            padding-right: 20px !important;
            padding-left: 0 !important;
        }

        .blue-bullet-list li:before {
            width: 8px !important;
            height: 8px !important;
            top: 6px !important;
        }
    }
</style>
@endpush

