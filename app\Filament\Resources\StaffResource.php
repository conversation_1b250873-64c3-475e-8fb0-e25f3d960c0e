<?php

namespace App\Filament\Resources;

use App\Filament\Resources\StaffResource\Pages\CreateStaff;
use App\Filament\Resources\StaffResource\Pages\EditStaff;
use App\Filament\Resources\StaffResource\Pages\ListStaff;
use App\Models\Department;
use App\Models\Staff;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TernaryFilter;
use Filament\Tables\Table;
use Illuminate\Support\Str;

class StaffResource extends Resource
{
    protected static ?string $model = Staff::class;

    // protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationIcon = 'heroicon-o-user';

    public static function form(Form $form): Form
    {
        return $form
            ->schema(Staff::getFormSchema());
    }

    public static function table(Table $table): Table
    {
        return $table
            ->persistFiltersInSession()
            ->filtersTriggerAction(function ($action) {
                return $action->button()->label('Filters');
            })
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID')
                    ->numeric()
                    ->sortable(),

                Tables\Columns\TextColumn::make('department.name_en')
                    ->label('Department')
                    ->sortable()
                    ->searchable()
                    ->badge(),

                Tables\Columns\ImageColumn::make('photo')
                    ->label('Photo')
                    ->circular()
                    ->width(40)
                    ->height(40)
                    ->defaultImageUrl(function ($record) {
                        return 'https://ui-avatars.com/api/?background=random&name=' . urlencode($record->name_en);
                    }),

                Tables\Columns\TextColumn::make('name_en')
                    ->searchable(),

                Tables\Columns\TextColumn::make('name_ar')
                    ->searchable(),

                Tables\Columns\TextColumn::make('title_en')
                    ->searchable()
                    ->description(function ($record) {
                        return Str::of($record->bio_en)->limit(100);
                    })
                    ->wrap(),

                Tables\Columns\TextColumn::make('title_ar')
                    ->searchable()
                    ->description(function ($record) {
                        return Str::of($record->bio_ar)->limit(100);
                    })
                    ->wrap(),

                Tables\Columns\ToggleColumn::make('is_active')
                    ->label('Active')
                    ->onColor('success')
                    ->offColor('danger')
                    ->sortable(),

                // Tables\Columns\TextColumn::make('created_at')
                //     ->dateTime()
                //     ->sortable()
                //     ->toggleable(isToggledHiddenByDefault: true),
                // Tables\Columns\TextColumn::make('updated_at')
                //     ->dateTime()
                //     ->sortable()
                //     ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                TernaryFilter::make('is_active')
                    ->label('Active')
                    ->trueLabel('Active Only')
                    ->falseLabel('Inactive Only')
                    ->native(false),

                SelectFilter::make('department_id')
                    ->label('Department')
                    ->options(Department::all()->pluck('name_en', 'id'))
                    ->searchable()
                    ->multiple()
                    ->preload(),

                SelectFilter::make('title_en')
                    ->label('Title')
                    ->options(Staff::all()->pluck('title_en', 'id'))
                    ->searchable()
                    ->multiple()
                    ->preload(),

                Filter::make('has_photo')
                    ->query(function ($query) {
                        $query->whereNotNull('photo');
                    })
                    ->label('Has Image')
                    ->toggle(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListStaff::route('/'),
            'create' => CreateStaff::route('/create'),
            'edit' => EditStaff::route('/{record}/edit'),
        ];
    }
}









