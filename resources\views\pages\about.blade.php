@extends('layouts.app')  <!-- Extend the base layout -->

{{-- @section('title', '<PERSON> Orthopedics & Spine Specialist Center - About Us Page')  <!-- Page title --> --}}

@section('content')
    <!-- Page Header Start -->
	{{-- <div class="page-header about-page-header bg-radius-section parallaxie" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
		<div class="container">
			<div class="row align-items-center">
				<div class="col-lg-12">
					<!-- Page Header Box Start -->
					<div class="page-header-box">
						<h1 class="{{ app()->getLocale() === 'ar' ? 'arabic-title' : 'text-anime-style-2' }}" data-cursor="-opaque">{{ __('messages.About Us') }}</h1>
						<nav class="wow fadeInUp">
							<ol class="breadcrumb">
								<li class="breadcrumb-item"><a href="./">{{ __('messages.Home') }}</a></li>
								<li class="breadcrumb-item active" aria-current="page">{{ __('messages.about_us') }}</li>
							</ol>
						</nav>
					</div>
					<!-- Page Header Box End -->
				</div>
			</div>
		</div>
	</div> --}}
    <div class="page-header about-page-header bg-radius-section parallaxie" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
		<div class="container">
			<div class="row align-items-center">
				<div class="col-lg-12">
					<!-- Page Header Box Start -->
					<div class="page-header-box">
						<h1 class="{{ app()->getLocale() === 'ar' ? 'arabic-title' : 'text-anime-style-2' }}" data-cursor="-opaque">{{ __('messages.About Us') }}</h1>
						<!-- Custom breadcrumb for RTL/LTR support -->
						@if(app()->getLocale() == 'ar')
							<!-- Arabic RTL breadcrumb -->
							<nav class="wow fadeInUp custom-rtl-breadcrumb">
								<div class="rtl-breadcrumb">
									<a href="{{ url(app()->getLocale()) }}" style="color: rgba(255, 255, 255, 0.7) !important; text-decoration: none !important; font-size: 16px;">{{ __('messages.Home') }}</a>
									<span class="separator" style="color: rgba(255, 255, 255, 0.7) !important; font-size: 16px;">/</span>
									<span style="color: #fff !important; font-size: 16px;">{{ __('messages.About Us') }}</span>
								</div>
							</nav>
						@else
							<!-- English LTR breadcrumb -->
							<nav class="wow fadeInUp">
								<ol class="breadcrumb">
									<li class="breadcrumb-item"><a href="{{ url(app()->getLocale()) }}">{{ __('messages.Home') }}</a></li>
									<li class="breadcrumb-item active" aria-current="page">{{ __('messages.About Us') }}</li>
								</ol>
							</nav>
						@endif
					</div>
					<!-- Page Header Box End -->
				</div>
			</div>
		</div>
	</div>
	<!-- Page Header End -->

    <!-- Page About Us Section Start -->
    <div class="page-about-us bg-radius-section">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-lg-5">
                    <!-- Page About Content Start -->
                    <div class="page-about-content">
                        <!-- Section Title Start -->
                        <div class="section-title">
                            <h3 class="wow fadeInUp {{ app()->getLocale() === 'ar' ? 'text-right' : '' }}">{{ __('messages.about us') }}</h3>
                            <h2 class="{{ app()->getLocale() === 'ar' ? 'arabic-title' : 'text-anime-style-3' }}" data-cursor="-opaque">{{ __('messages.Leading Orthopedic & Spine Specialist Center') }}</h2>
                            <p class="wow fadeInUp {{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}" data-wow-delay="0.25s">{{ __('messages.about_description') }}</p>
                        </div>
                        <!-- Section Title End -->

                        <!-- Page About Content Body Start -->
                        <div class="page-about-content-body">
                            <div class="row">
                                <div class="col-lg-7 col-md-6">
                                    <!-- Page About Content List Start -->
                                    <div class="page-about-content-list">
                                        <div class="page-about-content-item wow fadeInUp" data-wow-delay="0.5s">
                                            <div class="icon-box">
                                                <img src="{{ asset("assets/images/icon-page-about-list-1.svg") }}" alt="">
                                            </div>
                                            <div class="page-about-item-content">
                                                <p class="{{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ __('messages.team of highly skilled and experienced') }}</p>
                                            </div>
                                        </div>

                                        <div class="page-about-content-item wow fadeInUp" data-wow-delay="0.75s">
                                            <div class="icon-box">
                                                <img src="{{ asset("assets/images/icon-page-about-list-2.svg") }}" alt="">
                                            </div>
                                            <div class="page-about-item-content">
                                                <p class="{{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ __('messages.team is composed certified high trained') }}</p>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- Page About Content List End -->
                                </div>

                                <div class="col-lg-5 col-md-6">
                                    <!-- Page About Content Box Start -->
                                    <div class="page-about-content-box wow fadeInUp" data-wow-delay="0.5s">
                                        <div class="page-about-box-title">
                                            <h3 class="{{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ __('messages.Excellence in Orthopedic Care') }}</h3>
                                        </div>
                                        <div class="page-about-box-list">
                                            <ul class="{{ app()->getLocale() === 'ar' ? 'rtl-bullets' : '' }}">
                                                <li class="{{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ __('messages.Specialized') }}</li>
                                                <li class="{{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ __('messages.Advanced') }}</li>
                                                <li class="{{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ __('messages.Experienced') }}</li>
                                                <li class="{{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ __('messages.Patient-Centered') }}</li>
                                            </ul>
                                        </div>
                                    </div>
                                    <!-- Page About Content Box End -->
                                </div>

                                <div class="col-lg-12">
                                    <div class="page-about-body-content wow fadeInUp" data-wow-delay="1s">
                                        <p class="{{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ __('messages.about_facility_description') }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Page About Content Body End -->

                        <!-- Page About Content Btn Start -->
                        <div class="page-about-content-btn wow fadeInUp" data-wow-delay="1.25s">
                            <a target="_blank" href="https://wa.me/97317336601" class="btn-default"><span>{{ __('messages.book appointment') }}</span></a>
                        </div>
                        <!-- Page About Content Btn End -->
                    </div>
                    <!-- Page About Content End -->
                </div>

                <div class="col-lg-7">
                    <!-- Page About Images Start -->
                    <div class="page-about-image">
                        <!-- Page About Image Start -->
                        <div class="page-about-img-1">
                            <figure>
                                <img src="{{ asset("assets/images/page-about-bg-image.jpg") }}" alt="">
                            </figure>
                        </div>
                        <!-- Page About Image End -->

                        <!-- Page About Image Start -->
                        <div class="page-about-img-2">
                            <figure>
                                <img src="{{ asset("assets/images/page-about-image.png") }}" alt="">
                            </figure>
                        </div>
                        <!-- Page About Image End -->
                    </div>
                    <!-- Page About Image End -->
                </div>
            </div>

            <!-- Page Testimonial Box Start -->
            <div class="about-testimonial-box">
                <div class="row">
                    <div class="col-lg-12">
                        <!-- About Testimonial Slider Start -->
                        <div class="about-testimonial-slider">
                            <div class="swiper">
                                <div class="swiper-wrapper">
                                    <!-- About Testimonial Slide Start -->
                                    <div class="swiper-slide">
                                        <div class="about-testimonial-item">
                                            <p class="{{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ __('messages.Advanced Orthopedic Care for Better Mobility') }}</p>
                                        </div>
                                    </div>
                                    <!-- About Testimonial Slide End -->

                                    <!-- About Testimonial Slide Start -->
                                    <div class="swiper-slide">
                                        <div class="about-testimonial-item">
                                            <p class="{{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ __('messages.Specialized Spine Treatment by Expert Physicians') }}</p>
                                        </div>
                                    </div>
                                    <!-- About Testimonial Slide End -->

                                    <!-- About Testimonial Slide Start -->
                                    <div class="swiper-slide">
                                        <div class="about-testimonial-item">
                                            <p class="{{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ __('messages.Comprehensive Musculoskeletal Care Under One Roof') }}</p>
                                        </div>
                                    </div>
                                    <!-- About Testimonial Slide End -->
                                </div>
                                <div class="about-testimonial-btn">
                                    <div class="about-testimonial-button-prev"></div>
                                    <div class="about-testimonial-button-next"></div>
                                </div>
                            </div>
                        </div>
                    <!-- About Testimonial Slider End -->
                    </div>
                </div>
            </div>
            <!-- Page Testimonial Box End -->
        </div>
    </div>
    <!-- Page About Us Section End -->

    <!-- About Icon Box List Start -->
    <div class="about-icon-box-list bg-radius-section" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
        <div class="container">
            <div class="row">
                <div class="col-lg-2 col-md-4 col-6">
                    <!-- About Icon List Item Start -->
                    <div class="about-icon-list-item wow fadeInUp">
                        <div class="icon-box">
                            <img src="{{ asset("assets/images/about-icon-list-item-1.svg") }}" alt="">
                        </div>
                        <div class="about-icon-list-content">
                            <h3 class="{{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ __('messages.sports rehabilitation') }}</h3>
                        </div>
                    </div>
                    <!-- About Icon List Item End -->
                </div>

                <div class="col-lg-2 col-md-4 col-6">
                    <!-- About Icon List Item Start -->
                    <div class="about-icon-list-item wow fadeInUp" data-wow-delay="0.25s">
                        <div class="icon-box">
                            <img src="{{ asset("assets/images/about-icon-list-item-2.svg") }}" alt="">
                        </div>
                        <div class="about-icon-list-content">
                            <h3 class="{{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ __('messages.backpain management') }}</h3>
                        </div>
                    </div>
                    <!-- About Icon List Item End -->
                </div>

                <div class="col-lg-2 col-md-4 col-6">
                    <!-- About Icon List Item Start -->
                    <div class="about-icon-list-item wow fadeInUp" data-wow-delay="0.5s">
                        <div class="icon-box">
                            <img src="{{ asset("assets/images/about-icon-list-item-3.svg") }}" alt="">
                        </div>
                        <div class="about-icon-list-content">
                            <h3 class="{{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ __('messages.orthopedic rehabilitation') }}</h3>
                        </div>
                    </div>
                    <!-- About Icon List Item End -->
                </div>

                <div class="col-lg-2 col-md-4 col-6">
                    <!-- About Icon List Item Start -->
                    <div class="about-icon-list-item wow fadeInUp" data-wow-delay="0.75s">
                        <div class="icon-box">
                            <img src="{{ asset("assets/images/about-icon-list-item-4.svg") }}" alt="">
                        </div>
                        <div class="about-icon-list-content">
                            <h3 class="{{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ __('messages.occupational therapy') }}</h3>
                        </div>
                    </div>
                    <!-- About Icon List Item End -->
                </div>

                <div class="col-lg-2 col-md-4 col-6">
                    <!-- About Icon List Item Start -->
                    <div class="about-icon-list-item wow fadeInUp" data-wow-delay="1s">
                        <div class="icon-box">
                            <img src="{{ asset("assets/images/about-icon-list-item-5.svg") }}" alt="">
                        </div>
                        <div class="about-icon-list-content">
                            <h3 class="{{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ __('messages.home physiotherapy') }}</h3>
                        </div>
                    </div>
                    <!-- About Icon List Item End -->
                </div>

                <div class="col-lg-2 col-md-4 col-6">
                    <!-- About Icon List Item Start -->
                    <div class="about-icon-list-item wow fadeInUp" data-wow-delay="1.25s">
                        <div class="icon-box">
                            <img src="{{ asset("assets/images/about-icon-list-item-6.svg") }}" alt="">
                        </div>
                        <div class="about-icon-list-content">
                            <h3 class="{{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ __('messages.pelvic rehabilitation') }}</h3>
                        </div>
                    </div>
                    <!-- About Icon List Item End -->
                </div>
            </div>
        </div>
    </div>
    <!-- About Icon Box List End -->

        <!-- Mission Vision Start -->
        <div class="mission-vision py-5 my-md-5 my-4">
            <div class="container">
                <div class="row section-row mb-md-5 mb-4">
                    <!-- Section Title Start -->
                    <div class="section-title text-center">
                        <span class="badge bg-light text-dark rounded-pill px-3 py-2 mb-3">{{ __('messages.Excellence in Orthopedics') }}</span>
                        <h2 class="{{ app()->getLocale() === 'ar' ? 'arabic-title text-center' : 'text-anime-style-2' }} fs-md-1 fs-3" data-cursor="-opaque">
                            @if(app()->getLocale() === 'ar')
                                {{ __('messages.Leaders in Orthopedic Care') }}
                            @else
                                <span class="text-success">Leaders in</span> Orthopedic Care
                            @endif
                        </h2>
                    </div>
                    <!-- Section Title End -->
                </div>

                <div class="row gy-4">
                    @php
                        $aboutUs = \App\Models\AboutUs::where('is_active', true)->first();
                    @endphp

                    <div class="col-lg-4 col-md-6">
                        <!-- Mva Item Start -->
                        <div class="our-mva-item wow fadeInUp rounded-4 shadow-sm p-4 h-100 bg-light" dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}">
                            <!-- Icon Box Start -->
                            <div class="icon-box bg-light rounded-circle p-3 d-inline-flex mb-3 {{ app()->getLocale() === 'ar' ? 'align-self-end' : '' }}">
                                <img src="{{ $aboutUs->mission_icon ? Storage::url($aboutUs->mission_icon) : asset('assets/images/icon-our-mission.svg') }}" alt="Orthocare Mission" width="40" height="40">
                            </div>
                            <!-- Icon Box End -->

                            <!-- Mva Content Start -->
                            <div class="mva-item-content">
                                <h3 class="h5 mb-3 {{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ __('messages.Our Mission') }}</h3>
                                <div class="mb-0 fs-sm tiptap-content {{ app()->getLocale() === 'ar' ? 'rtl-content' : '' }}">
                                    {!! app()->getLocale() === 'en' ? $aboutUs->mission_en : $aboutUs->mission_ar !!}
                                </div>
                            </div>
                            <!-- Mva Content End -->
                        </div>
                        <!-- Mva Item End -->
                    </div>

                    <div class="col-lg-4 col-md-6">
                        <!-- Mva Item Start -->
                        <div class="our-mva-item wow fadeInUp rounded-4 shadow-sm p-4 h-100 bg-light" data-wow-delay="0.25s" dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}">
                            <!-- Icon Box Start -->
                            <div class="icon-box bg-light rounded-circle p-3 d-inline-flex mb-3 {{ app()->getLocale() === 'ar' ? 'align-self-end' : '' }}">
                                <img src="{{ $aboutUs->vision_icon ? Storage::url($aboutUs->vision_icon) : asset('assets/images/icon-our-vision.svg') }}" alt="Orthocare Vision" width="40" height="40">
                            </div>
                            <!-- Icon Box End -->

                            <!-- Mva Content Start -->
                            <div class="mva-item-content">
                                <h3 class="h5 mb-3 {{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ __('messages.Our Vision') }}</h3>
                                <div class="mb-0 fs-sm tiptap-content {{ app()->getLocale() === 'ar' ? 'rtl-content' : '' }}">
                                    {!! app()->getLocale() === 'en' ? $aboutUs->vision_en : $aboutUs->vision_ar !!}
                                </div>
                            </div>
                            <!-- Mva Content End -->
                        </div>
                        <!-- Mva Item End -->
                    </div>

                    <div class="col-lg-4 col-md-6 mx-md-auto">
                        <!-- Mva Item Start -->
                        <div class="our-mva-item wow fadeInUp rounded-4 shadow-sm p-4 h-100 bg-light" data-wow-delay="0.5s" dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}">
                            <!-- Icon Box Start -->
                            <div class="icon-box bg-light rounded-circle p-3 d-inline-flex mb-3 {{ app()->getLocale() === 'ar' ? 'align-self-end' : '' }}">
                                <img src="{{ asset('assets/images/icon-our-approch.svg') }}" alt="Orthocare Approach" width="40" height="40">
                            </div>
                            <!-- Icon Box End -->

                            <!-- Mva Content Start -->
                            <div class="mva-item-content">
                                <h3 class="h5 mb-3 {{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ __('messages.Our Approach') }}</h3>
                                <p class="mb-0 fs-sm {{ app()->getLocale() === 'ar' ? 'arabic-content' : '' }}">{{ __('messages.approach_description') }}</p>
                            </div>
                            <!-- Mva Content End -->
                        </div>
                        <!-- Mva Item End -->
                    </div>
                </div>

                <!-- Call To Action Start -->
                <div class="cta-infobar wow fadeInUp rounded-4 shadow-sm p-4 p-md-5 mt-5 mt-md-5 mt-4 border" data-wow-delay="0.75s" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
                    <div class="row align-items-center">
                        <div class="col-lg-6">
                            <!-- Cta Content Start -->
                            <div class="cta-info-content d-flex flex-column flex-sm-row align-items-sm-center">
                                <div class="icon-box bg-light rounded-circle p-3 {{ app()->getLocale() === 'ar' ? 'ms-sm-3' : 'me-sm-3' }} mb-3 mb-sm-0 align-self-sm-start">
                                    <img src="{{ asset('assets/images/icon-cta.svg') }}" alt="Schedule Consultation" width="40" height="40">
                                </div>

                                <div class="cta-content">
                                    <h3 class="h5 mb-2 {{ app()->getLocale() === 'ar' ? 'text-right arabic-content' : '' }}">{{ __('messages.Ready To Experience Expert Orthopedic Care?') }}</h3>
                                    <p class="mb-0 fs-sm {{ app()->getLocale() === 'ar' ? 'text-right arabic-content' : '' }}">{{ __('messages.schedule_consultation_description') }}</p>
                                </div>
                            </div>
                            <!-- Cta Content End -->
                        </div>

                        <div class="col-lg-6 {{ app()->getLocale() === 'ar' ? 'text-lg-start' : 'text-lg-end' }} mt-4 mt-lg-0">
                            <!-- Cta Appointment Button Start -->
                            <div class="cta-appointment-btn">
                                <a href="#" class="btn btn-primary rounded-pill px-3 py-2 fs-sm w-auto">
                                    {{ __('messages.Schedule Consultation') }}
                                    <i class="fas {{ app()->getLocale() === 'ar' ? 'fa-arrow-left me-2' : 'fa-arrow-right ms-2' }}"></i>
                                </a>
                            </div>
                            <!-- Cta Appointment Button End -->
                        </div>
                    </div>
                </div>
                <!-- Call To Action End -->
            </div>
        </div>
        <!-- Mission Vision End -->

    <!-- Our Rehabilitation Section Start -->
    {{-- <div class="our-rehabilitation bg-radius-section">
        <div class="container">
            <div class="row section-row">
                <div class="col-lg-12">
                    <!-- Section Title Start -->
                    <div class="section-title">
                        <h3 class="wow fadeInUp">our services</h3>
                        <h2 class="text-anime-style-3" data-cursor="-opaque">Specialized Medical Services</h2>
                    </div>
                    <!-- Section Title End -->
                </div>
            </div>

            <div class="row">
                <div class="col-lg-4 col-md-6">
                    <!-- Therapy Rehabilitation Item Start -->
                    <div class="therapy-rehabilitation-item wow fadeInUp">
                        <!-- Therapy Rehabilitation Image Start -->
                        <div class="therapy-rehabilitation-image">
                            <a href="#">
                                <figure>
                                    <img src="{{ asset("assets/images/therapy-rehabilitation-img-1.jpg") }}" alt="">
                                </figure>
                            </a>
                        </div>
                        <!-- Therapy Rehabilitation Image End -->

                        <!-- Therapy Rehabilitation Header Start -->
                        <div class="therapy-rehabilitation-header">
                            <!-- Therapy Rehabilitation Content Start -->
                            <div class="therapy-rehabilitation-content">
                                <div class="therapy-rehabilitation-btn">
                                    <a href="#"><img src="{{ asset("assets/images/arrow-long-white.svg") }}" alt=""></a>
                                </div>
                                <div class="therapy-rehabilitation-title">
                                    <h3>Orthopedic Treatment</h3>
                                </div>
                            </div>
                            <!-- Therapy Rehabilitation Content End -->

                            <!-- Therapy Rehabilitation Description Start -->
                            <div class="therapy-rehabilitation-dec">
                                <p>Comprehensive care for bone, joint, and muscle conditions with advanced techniques.</p>
                            </div>
                            <!-- Therapy Rehabilitation Description End -->
                        </div>
                        <!-- Therapy Rehabilitation Header End -->

                        <!-- Therapy Rehabilitation Body Start -->
                        <div class="therapy-rehabilitation-body">
                            <!-- Therapy Rehabilitation List Start -->
                            <div class="therapy-rehabilitation-list">
                                <ul>
                                    <li>Joint Replacement Surgery</li>
                                    <li>Sports Injury Treatment</li>
                                    <li>Fracture Management</li>
                                </ul>
                            </div>
                            <!-- Therapy Rehabilitation List End -->
                        </div>
                        <!-- Therapy Rehabilitation Body End -->
                    </div>
                    <!-- Therapy Rehabilitation Item End -->
                </div>

                <div class="col-lg-4 col-md-6">
                    <!-- Therapy Rehabilitation Item Start -->
                    <div class="therapy-rehabilitation-item wow fadeInUp" data-wow-delay="0.25s">
                        <!-- Therapy Rehabilitation Image Start -->
                        <div class="therapy-rehabilitation-image">
                            <a href="#">
                                <figure>
                                    <img src="{{ asset("assets/images/therapy-rehabilitation-img-2.jpg") }}" alt="">
                                </figure>
                            </a>
                        </div>
                        <!-- Therapy Rehabilitation Image End -->

                        <!-- Therapy Rehabilitation Header Start -->
                        <div class="therapy-rehabilitation-header">
                            <!-- Therapy Rehabilitation Content Start -->
                            <div class="therapy-rehabilitation-content">
                                <div class="therapy-rehabilitation-btn">
                                    <a href="#"><img src="{{ asset("assets/images/arrow-long-white.svg") }}" alt=""></a>
                                </div>
                                <div class="therapy-rehabilitation-title">
                                    <h3>Spine Care</h3>
                                </div>
                            </div>
                            <!-- Therapy Rehabilitation Content End -->

                            <!-- Therapy Rehabilitation Description Start -->
                            <div class="therapy-rehabilitation-dec">
                                <p>Specialized treatment for all spine conditions from conservative care to surgery.</p>
                            </div>
                            <!-- Therapy Rehabilitation Description End -->
                        </div>
                        <!-- Therapy Rehabilitation Header End -->

                        <!-- Therapy Rehabilitation Body Start -->
                        <div class="therapy-rehabilitation-body">
                            <!-- Therapy Rehabilitation List Start -->
                            <div class="therapy-rehabilitation-list">
                                <ul>
                                    <li>Disc Disorders Treatment</li>
                                    <li>Spinal Deformity Correction</li>
                                    <li>Minimally Invasive Surgery</li>
                                </ul>
                            </div>
                            <!-- Therapy Rehabilitation List End -->
                        </div>
                        <!-- Therapy Rehabilitation Body End -->
                    </div>
                    <!-- Therapy Rehabilitation Item End -->
                </div>

                <div class="col-lg-4 col-md-6">
                    <!-- Therapy Rehabilitation Item Start -->
                    <div class="therapy-rehabilitation-item wow fadeInUp" data-wow-delay="0.5s">
                        <!-- Therapy Rehabilitation Image Start -->
                        <div class="therapy-rehabilitation-image">
                            <a href="#">
                                <figure>
                                    <img src="{{ asset("assets/images/therapy-rehabilitation-img-3.jpg") }}" alt="">
                                </figure>
                            </a>
                        </div>
                        <!-- Therapy Rehabilitation Image End -->

                        <!-- Therapy Rehabilitation Header Start -->
                        <div class="therapy-rehabilitation-header">
                            <!-- Therapy Rehabilitation Content Start -->
                            <div class="therapy-rehabilitation-content">
                                <div class="therapy-rehabilitation-btn">
                                    <a href="#"><img src="{{ asset("assets/images/arrow-long-white.svg") }}" alt=""></a>
                                </div>
                                <div class="therapy-rehabilitation-title">
                                    <h3>Rehabilitation Services</h3>
                                </div>
                            </div>
                            <!-- Therapy Rehabilitation Content End -->

                            <!-- Therapy Rehabilitation Description Start -->
                            <div class="therapy-rehabilitation-dec">
                                <p>Comprehensive rehabilitation programs to restore function and improve quality of life.</p>
                            </div>
                            <!-- Therapy Rehabilitation Description End -->
                        </div>
                        <!-- Therapy Rehabilitation Header End -->

                        <!-- Therapy Rehabilitation Body Start -->
                        <div class="therapy-rehabilitation-body">
                            <!-- Therapy Rehabilitation List Start -->
                            <div class="therapy-rehabilitation-list">
                                <ul>
                                    <li>Post-Surgical Rehabilitation</li>
                                    <li>Physical Therapy</li>
                                    <li>Pain Management</li>
                                </ul>
                            </div>
                            <!-- Therapy Rehabilitation List End -->
                        </div>
                        <!-- Therapy Rehabilitation Body End -->
                    </div>
                    <!-- Therapy Rehabilitation Item End -->
                </div>
            </div>
        </div>
    </div> --}}
    <!-- Our Rehabilitation Section End -->

    <!-- Therapy Process Section Start -->
    <div class="therapy-process bg-radius-section">
        <div class="container">
            <div class="row section-row align-items-center" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
                <div class="col-lg-7 {{ app()->getLocale() == 'ar' ? 'pe-lg-4' : '' }}">
                    <!-- Section Title Start -->
                    <div class="section-title {{ app()->getLocale() == 'ar' ? 'text-right arabic-section-title' : '' }}">
                        <h3 class="wow fadeInUp {{ app()->getLocale() == 'ar' ? 'arabic-content' : '' }}">{{ __('messages.patient journey') }}</h3>
                        <h2 class="{{ app()->getLocale() == 'ar' ? 'arabic-title' : 'text-anime-style-3' }}" data-cursor="-opaque">{{ __('messages.Your Path to Recovery') }}</h2>
                    </div>
                    <!-- Section Title End -->
                </div>

                <div class="col-lg-5 {{ app()->getLocale() == 'ar' ? 'ps-lg-4' : '' }}">
                    <!-- Section Btn Start -->
                    <div class="section-btn wow fadeInUp {{ app()->getLocale() == 'ar' ? 'text-start' : 'text-end' }}">
                        <a href="#" class="btn-default"><span>{{ __('messages.make_appointment') }}</span></a>
                    </div>
                    <!-- Section Btn End -->
                </div>
            </div>

            <div class="row align-items-center">
                <div class="col-lg-6">
                    <!-- Therapy Process Content Start -->
                    <div class="therapy-process-content" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
                        <!-- Therapy Process Item Start -->
                        <div class="therapy-process-item wow fadeInUp {{ app()->getLocale() == 'ar' ? 'rtl-item' : '' }}">
                            <div class="icon-box">
                                <img src="{{ asset("assets/images/icon-therapy-process-1.svg") }}" alt="">
                            </div>
                            <div class="therapy-process-item-content">
                                <h3 class="{{ app()->getLocale() == 'ar' ? 'arabic-content' : '' }}">{{ __('messages.initial consultation') }}</h3>
                                <p class="{{ app()->getLocale() == 'ar' ? 'arabic-content' : '' }}">{{ __('messages.Meet with our orthopedic specialists for a comprehensive evaluation of your condition. We\'ll review your medical history, perform necessary examinations, and discuss your symptoms in detail.') }}</p>
                            </div>
                        </div>
                        <!-- Therapy Process Item End -->

                        <!-- Therapy Process Item Start -->
                        <div class="therapy-process-item wow fadeInUp {{ app()->getLocale() == 'ar' ? 'rtl-item' : '' }}" data-wow-delay="0.25s">
                            <div class="icon-box">
                                <img src="{{ asset("assets/images/icon-therapy-process-2.svg") }}" alt="">
                            </div>
                            <div class="therapy-process-item-content">
                                <h3 class="{{ app()->getLocale() == 'ar' ? 'arabic-content' : '' }}">{{ __('messages.diagnosis & imaging') }}</h3>
                                <p class="{{ app()->getLocale() == 'ar' ? 'arabic-content' : '' }}">{{ __('messages.Using advanced diagnostic technology, we\'ll identify the root cause of your condition. This may include X-rays, MRI, CT scans, or other specialized tests to provide a precise diagnosis.') }}</p>
                            </div>
                        </div>
                        <!-- Therapy Process Item End -->

                        <!-- Therapy Process Item Start -->
                        <div class="therapy-process-item wow fadeInUp {{ app()->getLocale() == 'ar' ? 'rtl-item' : '' }}" data-wow-delay="0.5s">
                            <div class="icon-box">
                                <img src="{{ asset("assets/images/icon-therapy-process-3.svg") }}" alt="">
                            </div>
                            <div class="therapy-process-item-content">
                                <h3 class="{{ app()->getLocale() == 'ar' ? 'arabic-content' : '' }}">{{ __('messages.personalized treatment plan') }}</h3>
                                <p class="{{ app()->getLocale() == 'ar' ? 'arabic-content' : '' }}">{{ __('messages.We\'ll create a customized treatment plan that may include surgical or non-surgical options, depending on your condition. Our team will explain all available options and help you make informed decisions about your care.') }}</p>
                            </div>
                        </div>
                        <!-- Therapy Process Item End -->

                        <!-- Therapy Process Item Start -->
                        <div class="therapy-process-item wow fadeInUp {{ app()->getLocale() == 'ar' ? 'rtl-item' : '' }}" data-wow-delay="0.75s">
                            <div class="icon-box">
                                <img src="{{ asset("assets/images/icon-therapy-process-4.svg") }}" alt="">
                            </div>
                            <div class="therapy-process-item-content">
                                <h3 class="{{ app()->getLocale() == 'ar' ? 'arabic-content' : '' }}">{{ __('messages.recovery & rehabilitation') }}</h3>
                                <p class="{{ app()->getLocale() == 'ar' ? 'arabic-content' : '' }}">{{ __('messages.Following treatment, our rehabilitation specialists will guide you through a comprehensive recovery program designed to restore function, improve mobility, and prevent future injuries.') }}</p>
                            </div>
                        </div>
                        <!-- Therapy Process Item End -->
                    </div>
                    <!-- Therapy Process Content End -->
                </div>

                <div class="col-lg-6">
                    <!-- Therapy Process Images Start -->
                    <div class="therapy-process-images">
                        <!-- Therapy Process Image Start -->
                        <div class="therapy-process-img-1">
                            <figure class="image-anime">
                                <img src="{{ asset("assets/images/therapy-process-img-1.jpg") }}" alt="">
                            </figure>
                        </div>
                        <!-- Therapy Process Image End -->

                        <!-- Therapy Process Image Start -->
                        <div class="therapy-process-img-2">
                            <figure class="image-anime">
                                <img src="{{ asset("assets/images/therapy-process-img-2.jpg") }}" alt="">
                            </figure>
                        </div>
                        <!-- Therapy Process Image End -->

                        <!-- Therapy Process Image Start -->
                        <div class="therapy-process-img-3">
                            <figure class="image-anime">
                                <img src="{{ asset("assets/images/therapy-process-img-3.jpg") }}" alt="">
                            </figure>
                        </div>
                        <!-- Therapy Process Image End -->
                    </div>
                    <!-- Therapy Process Images End -->
                </div>
            </div>
        </div>
    </div>
    <!-- Therapy Process Section End -->

    <!-- Our Video Section Start -->
    <div class="our-video bg-radius-section">
        <div class="container">
            <div class="row align-items-center" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
                <div class="col-lg-6 {{ app()->getLocale() == 'ar' ? 'order-lg-2' : '' }}">
                    <!-- Our Video Content Start -->
                    <div class="our-video-content {{ app()->getLocale() == 'ar' ? 'rtl-content' : '' }}">
                        <!-- Section Title Start -->
                        <div class="section-title {{ app()->getLocale() == 'ar' ? 'text-right' : '' }}">
                            <h3 class="wow fadeInUp {{ app()->getLocale() == 'ar' ? 'arabic-content' : '' }}">{{ __('messages.video') }}</h3>
                            <h2 class="{{ app()->getLocale() == 'ar' ? 'arabic-title' : 'text-anime-style-3' }}" data-cursor="-opaque">{{ __('messages.Our Video') }}</h2>
                            <p class="wow fadeInUp {{ app()->getLocale() == 'ar' ? 'arabic-content' : '' }}" data-wow-delay="0.25s">{{ __('messages.video_description') }}</p>
                        </div>
                        <!-- Section Title End -->

                        <!-- Section Btn Start -->
                        <div class="section-btn wow fadeInUp {{ app()->getLocale() == 'ar' ? 'text-start' : '' }}" data-wow-delay="0.5s">
                            <a href="#" class="btn-default {{ app()->getLocale() == 'ar' ? 'btn-rtl' : '' }}"><span>{{ __('messages.make_appointment') }}</span></a>
                        </div>
                        <!-- Section Btn End -->
                    </div>
                    <!-- Our Video Content End -->
                </div>

                <div class="col-lg-6 {{ app()->getLocale() == 'ar' ? 'order-lg-1' : '' }}">
                    <!-- Video Play Button Start -->
                    <div class="video-play-button">
                        <a href="https://www.youtube.com/watch?v=Y-x0efG1seA" class="popup-video" data-cursor-text="{{ app()->getLocale() == 'ar' ? __('messages.Play') : 'Play' }}">
                           <img src="{{ asset("assets/images/video-play-button.svg") }}" alt="">
                        </a>
                    </div>
                    <!-- Video Play Button End -->
                </div>
            </div>
        </div>
    </div>
    <!-- Our Video Section End -->

    <!-- Process Steps Section Start -->
    <div class="process-steps bg-radius-section" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
        <div class="container">
            <div class="row section-row">
                <div class="col-lg-12">
                    <!-- Section Title Start -->
                    <div class="section-title {{ app()->getLocale() == 'ar' ? 'text-right' : '' }}">
                        <h3 class="wow fadeInUp {{ app()->getLocale() == 'ar' ? 'arabic-content' : '' }}">{{ __('messages.process') }}</h3>
                        <h2 class="{{ app()->getLocale() == 'ar' ? 'arabic-title' : 'text-anime-style-3' }}" data-cursor="-opaque">{{ __('messages.Easy Steps To Get Our Services') }}</h2>
                        <p class="wow fadeInUp {{ app()->getLocale() == 'ar' ? 'arabic-content' : '' }}" data-wow-delay="0.25s">{{ __('messages.process_description') }}</p>
                    </div>
                    <!-- Section Title End -->
                </div>
            </div>

            <div class="row process-steps-line align-items-start">
                <div class="col-lg-3 col-md-3">
                    <!-- Process Steps Box Start -->
                    <div class="process-step-box wow fadeInUp {{ app()->getLocale() == 'ar' ? 'rtl-box' : '' }}">
                        <!-- Process Steps Number Start -->
                        <div class="process-step-no">
                            <h2>01</h2>
                        </div>
                        <!-- Process Steps Number End -->

                        <!-- Process Steps Content Start -->
                        <div class="process-step-content">
                            <h3 class="{{ app()->getLocale() == 'ar' ? 'arabic-content' : '' }}">{{ __('messages.select services') }}</h3>
                            <p class="{{ app()->getLocale() == 'ar' ? 'arabic-content' : '' }}">{{ __('messages.select_services_description') }}</p>
                        </div>
                        <!-- Process Steps Content End -->
                    </div>
                    <!-- Process Steps Box End -->
                </div>

                <div class="col-lg-3 col-md-3">
                    <!-- Process Steps Box Start -->
                    <div class="process-step-box wow fadeInUp {{ app()->getLocale() == 'ar' ? 'rtl-box' : '' }}" data-wow-delay="0.25s">
                        <!-- Process Steps Number Start -->
                        <div class="process-step-no">
                            <h2>02</h2>
                        </div>
                        <!-- Process Steps Number End -->

                        <!-- Process Steps Content Start -->
                        <div class="process-step-content">
                            <h3 class="{{ app()->getLocale() == 'ar' ? 'arabic-content' : '' }}">{{ __('messages.make appointment') }}</h3>
                            <p class="{{ app()->getLocale() == 'ar' ? 'arabic-content' : '' }}">{{ __('messages.make_appointment_description') }}</p>
                        </div>
                        <!-- Process Steps Content End -->
                    </div>
                    <!-- Process Steps Box End -->
                </div>

                <div class="col-lg-3 col-md-3">
                    <!-- Process Steps Box Start -->
                    <div class="process-step-box wow fadeInUp {{ app()->getLocale() == 'ar' ? 'rtl-box' : '' }}" data-wow-delay="0.5s">
                        <!-- Process Steps Number Start -->
                        <div class="process-step-no">
                            <h2>03</h2>
                        </div>
                        <!-- Process Steps Number End -->

                        <!-- Process Steps Content Start -->
                        <div class="process-step-content">
                            <h3 class="{{ app()->getLocale() == 'ar' ? 'arabic-content' : '' }}">{{ __('messages.completed payment') }}</h3>
                            <p class="{{ app()->getLocale() == 'ar' ? 'arabic-content' : '' }}">{{ __('messages.completed_payment_description') }}</p>
                        </div>
                        <!-- Process Steps Content End -->
                    </div>
                    <!-- Process Steps Box End -->
                </div>

                <div class="col-lg-3 col-md-3">
                    <!-- Process Steps Box Start -->
                    <div class="process-step-box wow fadeInUp {{ app()->getLocale() == 'ar' ? 'rtl-box' : '' }}" data-wow-delay="0.75s">
                        <!-- Process Steps Number Start -->
                        <div class="process-step-no">
                            <h2>04</h2>
                        </div>
                        <!-- Process Steps Number End -->

                        <!-- Process Steps Content Start -->
                        <div class="process-step-content">
                            <h3 class="{{ app()->getLocale() == 'ar' ? 'arabic-content' : '' }}">{{ __('messages.enjoy your therapy') }}</h3>
                            <p class="{{ app()->getLocale() == 'ar' ? 'arabic-content' : '' }}">{{ __('messages.enjoy_therapy_description') }}</p>
                        </div>
                        <!-- Process Steps Content End -->
                    </div>
                    <!-- Process Steps Box End -->
                </div>
            </div>
        </div>
    </div>
    <!-- Process Steps Section End -->

    <!-- Therapist Team Section Start -->
    @include('components.doctors') <!-- Doctors Section -->
    <!-- Therapist Team Section End -->

    <!-- Our Testimonials Section Start -->
    @include('components.testimonials') <!-- Testimonials Section -->
    <!-- Our Testimonials Section End -->

    <!-- Page About Faqs Start -->
    <div class="page-about-faqs bg-radius-section" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
        <div class="container">
            <div class="row {{ app()->getLocale() == 'ar' ? 'justify-content-start' : '' }}">
                <div class="col-lg-7">
                    <div class="about-faqs-content">
                        <!-- Section Title Start -->
                        <div class="section-title {{ app()->getLocale() == 'ar' ? 'text-right' : '' }}">
                            <h3 class="wow fadeInUp {{ app()->getLocale() == 'ar' ? 'arabic-content' : '' }}">{{ __('messages.faqs') }}</h3>
                            <h2 class="{{ app()->getLocale() == 'ar' ? 'arabic-title' : 'text-anime-style-3' }}" data-cursor="-opaque">{{ __('messages.common_questions') }}</h2>
                        </div>
                        <!-- Section Title End -->

                        <!-- About Faqs Section Start -->
                        <div class="about-faq-section {{ app()->getLocale() == 'ar' ? 'rtl-faq' : '' }}">
                            <!-- FAQ Accordion Start -->
                            <div class="faq-accordion" id="accordion">
                                <!-- FAQ Item Start -->
                                <div class="accordion-item wow fadeInUp">
                                    <h2 class="accordion-header" id="heading1">
                                        <button class="accordion-button {{ app()->getLocale() == 'ar' ? 'arabic-content' : '' }}" type="button" data-bs-toggle="collapse" data-bs-target="#collapse1" aria-expanded="true" aria-controls="collapse1">
                                            {{ __('messages.faq_question_1') }}
                                        </button>
                                    </h2>
                                    <div id="collapse1" class="accordion-collapse collapse show" aria-labelledby="heading1" data-bs-parent="#accordion">
                                        <div class="accordion-body">
                                            <p class="{{ app()->getLocale() == 'ar' ? 'arabic-content' : '' }}">{{ __('messages.faq_answer_1') }}</p>
                                        </div>
                                    </div>
                                </div>
                                <!-- FAQ Item End -->

                                <!-- FAQ Item Start -->
                                <div class="accordion-item wow fadeInUp" data-wow-delay="0.2s">
                                    <h2 class="accordion-header" id="heading2">
                                        <button class="accordion-button collapsed {{ app()->getLocale() == 'ar' ? 'arabic-content' : '' }}" type="button" data-bs-toggle="collapse" data-bs-target="#collapse2" aria-expanded="false" aria-controls="collapse2">
                                            {{ __('messages.faq_question_2') }}
                                        </button>
                                    </h2>
                                    <div id="collapse2" class="accordion-collapse collapse" aria-labelledby="heading2" data-bs-parent="#accordion">
                                        <div class="accordion-body">
                                            <p class="{{ app()->getLocale() == 'ar' ? 'arabic-content' : '' }}">{{ __('messages.faq_answer_2') }}</p>
                                        </div>
                                    </div>
                                </div>
                                <!-- FAQ Item End -->

                                <!-- FAQ Item Start -->
                                <div class="accordion-item wow fadeInUp" data-wow-delay="0.4s">
                                    <h2 class="accordion-header" id="heading3">
                                        <button class="accordion-button collapsed {{ app()->getLocale() == 'ar' ? 'arabic-content' : '' }}" type="button" data-bs-toggle="collapse" data-bs-target="#collapse3" aria-expanded="false" aria-controls="collapse3">
                                            {{ __('messages.faq_question_3') }}
                                        </button>
                                    </h2>
                                    <div id="collapse3" class="accordion-collapse collapse" aria-labelledby="heading3" data-bs-parent="#accordion">
                                        <div class="accordion-body">
                                            <p class="{{ app()->getLocale() == 'ar' ? 'arabic-content' : '' }}">{{ __('messages.faq_answer_3') }}</p>
                                        </div>
                                    </div>
                                </div>
                                <!-- FAQ Item End -->

                                <!-- FAQ Item Start -->
                                <div class="accordion-item wow fadeInUp" data-wow-delay="0.6s">
                                    <h2 class="accordion-header" id="heading4">
                                        <button class="accordion-button collapsed {{ app()->getLocale() == 'ar' ? 'arabic-content' : '' }}" type="button" data-bs-toggle="collapse" data-bs-target="#collapse4" aria-expanded="false" aria-controls="collapse4">
                                            {{ __('messages.faq_question_4') }}
                                        </button>
                                    </h2>
                                    <div id="collapse4" class="accordion-collapse collapse" aria-labelledby="heading4" data-bs-parent="#accordion">
                                        <div class="accordion-body">
                                            <p class="{{ app()->getLocale() == 'ar' ? 'arabic-content' : '' }}">{{ __('messages.faq_answer_4') }}</p>
                                        </div>
                                    </div>
                                </div>
                                <!-- FAQ Item End -->

                                <!-- FAQ Item Start -->
                                <div class="accordion-item wow fadeInUp" data-wow-delay="0.8s">
                                    <h2 class="accordion-header" id="heading5">
                                        <button class="accordion-button collapsed {{ app()->getLocale() == 'ar' ? 'arabic-content' : '' }}" type="button" data-bs-toggle="collapse" data-bs-target="#collapse5" aria-expanded="false" aria-controls="collapse5">
                                            {{ __('messages.faq_question_5') }}
                                        </button>
                                    </h2>
                                    <div id="collapse5" class="accordion-collapse collapse" aria-labelledby="heading5" data-bs-parent="#accordion">
                                        <div class="accordion-body">
                                            <p class="{{ app()->getLocale() == 'ar' ? 'arabic-content' : '' }}">{{ __('messages.faq_answer_5') }}</p>
                                        </div>
                                    </div>
                                </div>
                                <!-- FAQ Item End -->
                            </div>
                            <!-- FAQ Accordion End -->
                        </div>
                        <!-- About Faqs Section End -->
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Page About Faqs End -->

    <div class="cta-box bg-radius-section parallaxie" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <div class="cta-box-content">
                        <!-- Section Title Start -->
                        <div class="section-title {{ app()->getLocale() == 'ar' ? 'text-right' : '' }}">
                            <h3 class="wow fadeInUp {{ app()->getLocale() == 'ar' ? 'arabic-content' : '' }}">{{ app()->getLocale() == 'ar' ? __('messages.book_appointment') : 'Book Appointment' }}</h3>
                            <h2 class="{{ app()->getLocale() == 'ar' ? 'arabic-title' : 'text-anime-style-3' }}" data-cursor="-opaque">{{ app()->getLocale() == 'ar' ? __('messages.first_step_healthier') : 'Take The First Step Towards a Healthier Mind And Body' }}</h2>
                            <p class="wow fadeInUp {{ app()->getLocale() == 'ar' ? 'arabic-content' : '' }}" data-wow-delay="0.25s">{{ app()->getLocale() == 'ar' ? __('messages.journey_healthier') : 'Your journey to a healthier mind and body begins here. At Physiocare, we offer a holistic approach that combines expert psychotherapy with comprehensive physical health support to help you achieve overall well-being. Whether you\'re seeking to manage stress.' }}</p>
                        </div>
                        <!-- Section Title End -->

                        <!-- Section Btn Start -->
                        <div class="section-btn wow fadeInUp {{ app()->getLocale() == 'ar' ? 'text-right' : '' }}" data-wow-delay="0.5s">
                            <a href="#" class="btn-default"><span>{{ app()->getLocale() == 'ar' ? __('messages.make_appointment') : 'make an appointment' }}</span></a>
                        </div>
                        <!-- Section Btn End -->
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('styles')
<style>
    /* Improved RTL text alignment for mission/vision/approach titles */
    [dir="rtl"] .our-mva-item h3,
    .our-mva-item:has(.arabic-content) h3,
    [dir="rtl"] .our-mva-item p,
    .our-mva-item:has(.arabic-content) p {
        text-align: right !important;
        width: 100% !important;
        display: block !important;
    }

    /* Fix for Arabic title alignment */
    .arabic-content {
        text-align: right !important;
        width: 100% !important;
        display: block !important;
        font-family: var(--arabic-font) !important;
        letter-spacing: 0 !important;
        word-spacing: normal !important;
        direction: rtl !important;
    }

    /* Ensure the icon box is properly positioned */
    [dir="rtl"] .our-mva-item .icon-box,
    .our-mva-item:has(.arabic-content) .icon-box {
        margin-left: auto !important;
        margin-right: 0 !important;
        align-self: flex-start !important;
    }

    /* Fix for the entire mission/vision/approach container */
    [dir="rtl"] .our-mva-item,
    .our-mva-item:has(.arabic-content) {
        display: flex !important;
        flex-direction: column !important;
        align-items: flex-end !important;
        text-align: right !important;
    }

    /* Fix for the content container */
    [dir="rtl"] .mva-item-content,
    .our-mva-item:has(.arabic-content) .mva-item-content {
        width: 100% !important;
        text-align: right !important;
    }

    /* Ensure tiptap content is properly aligned */
    .rtl-content p,
    .rtl-content ul,
    .rtl-content ol,
    .rtl-content li,
    .rtl-content div {
        text-align: right !important;
        direction: rtl !important;
        font-family: var(--arabic-font) !important;
        width: 100% !important;
    }

    /* RTL styles for therapy process section */
    [dir="rtl"] .therapy-process .section-title,
    [dir="rtl"] .therapy-process .section-title h3,
    [dir="rtl"] .therapy-process .section-title h2 {
        text-align: right !important;
    }

    /* Adjust title position in Arabic version */
    [dir="rtl"] .therapy-process .arabic-section-title {
        padding-right: 15px; /* Add padding to move title closer to left edge */
        margin-right: 0;
    }

    /* Make title more compact in Arabic */
    [dir="rtl"] .therapy-process .arabic-title {
        margin-top: 5px;
        margin-bottom: 5px;
    }

    /* RTL therapy process items */
    [dir="rtl"] .therapy-process-item,
    .therapy-process-item.rtl-item {
        display: flex;
        flex-direction: row-reverse;
        text-align: right;
    }

    [dir="rtl"] .therapy-process-item .icon-box,
    .therapy-process-item.rtl-item .icon-box {
        margin-right: 0;
        margin-left: 20px;
    }

    [dir="rtl"] .therapy-process-item-content,
    .therapy-process-item.rtl-item .therapy-process-item-content {
        text-align: right;
    }

    /* Arabic content styling */
    .therapy-process .arabic-content {
        font-family: var(--arabic-font) !important;
        letter-spacing: 0 !important;
        word-spacing: normal !important;
        text-align: right !important;
        direction: rtl !important;
    }

    /* Arabic title styling */
    .therapy-process .arabic-title {
        font-family: var(--arabic-font) !important;
        letter-spacing: 0 !important;
        word-spacing: normal !important;
        text-align: right !important;
        direction: rtl !important;
        display: block !important;
    }

    /* Fix for button alignment in RTL */
    [dir="rtl"] .therapy-process .section-btn.text-start {
        text-align: left !important;
        padding-left: 15px; /* Add padding to move button closer to right edge */
    }

    /* Adjust spacing in RTL mode */
    @media (max-width: 991px) {
        [dir="rtl"] .therapy-process .arabic-section-title {
            padding-right: 0;
            margin-bottom: 20px;
        }

        [dir="rtl"] .therapy-process .section-btn.text-start {
            text-align: right !important;
            padding-left: 0;
            padding-right: 15px;
        }
    }

    /* RTL styles for video section */
    [dir="rtl"] .our-video-content .section-title h3,
    [dir="rtl"] .our-video-content .section-title h2,
    [dir="rtl"] .our-video-content .section-title p {
        text-align: right !important;
        font-family: var(--arabic-font) !important;
    }

    [dir="rtl"] .our-video-content .section-btn {
        text-align: left !important;
    }

    /* Fix for Arabic title rendering */
    .our-video-content .arabic-title {
        font-family: var(--arabic-font) !important;
        letter-spacing: 0 !important;
        word-spacing: normal !important;
        text-align: right !important;
        animation: none !important;
        display: inline-block !important;
        white-space: nowrap !important;
        direction: rtl !important;
    }

    /* RTL content styling */
    .our-video-content.rtl-content {
        margin-left: 0 !important;
        margin-right: 40px !important;
    }

    /* Responsive adjustments */
    @media (max-width: 991px) {
        .our-video-content.rtl-content {
            margin-right: 0 !important;
            text-align: right !important;
        }
    }

    /* Fix for the video play button in RTL mode */
    [dir="rtl"] .our-video::after {
        right: auto;
        left: 0;
        background: radial-gradient(50.03% 56.03% at 50% 49.99%, rgba(255, 255, 255, 0) 57.33%, #FFFFFF 100%);
    }

    /* RTL styles for process steps section */
    [dir="rtl"] .process-steps .section-title h3,
    [dir="rtl"] .process-steps .section-title h2,
    [dir="rtl"] .process-steps .section-title p {
        text-align: right !important;
        font-family: var(--arabic-font) !important;
    }

    /* Fix for Arabic title rendering */
    .process-steps .arabic-title {
        font-family: var(--arabic-font) !important;
        letter-spacing: 0 !important;
        word-spacing: normal !important;
        text-align: right !important;
        animation: none !important;
        display: inline-block !important;
        white-space: nowrap !important;
        direction: rtl !important;
    }

    /* RTL process step box styling */
    [dir="rtl"] .process-step-box {
        text-align: right !important;
    }

    [dir="rtl"] .process-step-content h3,
    [dir="rtl"] .process-step-content p {
        text-align: right !important;
        font-family: var(--arabic-font) !important;
    }

    /* Maintain the process steps line in RTL mode */
    [dir="rtl"] .process-steps-line::before {
        left: auto;
        right: 0;
    }

    /* Adjust the process step number position in RTL mode */
    [dir="rtl"] .process-step-no {
        margin-right: 0;
        margin-left: auto;
    }

    /* Ensure the process steps line connects properly in RTL mode */
    @media (min-width: 768px) {
        [dir="rtl"] .process-steps-line::before {
            left: auto;
            right: 15%;
            width: 70%;
        }
    }

    /* Responsive adjustments */
    @media (max-width: 767px) {
        [dir="rtl"] .process-step-box {
            padding-right: 0;
            padding-left: 0;
        }
    }

    /* RTL styles for FAQ section */
    [dir="rtl"] .page-about-faqs .section-title h3,
    [dir="rtl"] .page-about-faqs .section-title h2 {
        text-align: right !important;
        font-family: var(--arabic-font) !important;
    }

    /* Fix for Arabic title rendering */
    .page-about-faqs .arabic-title {
        font-family: var(--arabic-font) !important;
        letter-spacing: 0 !important;
        word-spacing: normal !important;
        text-align: right !important;
        animation: none !important;
        display: inline-block !important;
        white-space: nowrap !important;
        direction: rtl !important;
    }

    /* RTL FAQ accordion styling */
    [dir="rtl"] .about-faq-section .accordion-header .accordion-button {
        text-align: right !important;
        padding: 15px 15px 15px 45px !important;
        font-family: var(--arabic-font) !important;
    }

    [dir="rtl"] .about-faq-section .accordion-item .accordion-body {
        text-align: right !important;
        padding: 15px 15px 15px 45px !important;
    }

    /* Adjust the accordion arrow position in RTL mode */
    [dir="rtl"] .about-faq-section .accordion-item .accordion-button::after,
    [dir="rtl"] .about-faq-section .accordion-item .accordion-button.collapsed::after {
        right: auto !important;
        left: 15px !important;
    }

    /* Ensure the accordion content is properly aligned in RTL */
    [dir="rtl"] .about-faq-section .accordion-body p {
        text-align: right !important;
        font-family: var(--arabic-font) !important;
    }

    /* Fix for accordion button focus in RTL */
    [dir="rtl"] .about-faq-section .accordion-button:focus {
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25) !important;
        border-color: #86b7fe !important;
        z-index: 3 !important;
    }

    /* Position the FAQ section on the left side in RTL mode */
    [dir="rtl"] .page-about-faqs .row {
        justify-content: flex-start !important;
    }

    /* Override any potential Bootstrap RTL offsets */
    [dir="rtl"] .page-about-faqs .col-lg-7 {
        margin-right: 0 !important;
        margin-left: auto !important;
    }

    /* Ensure proper spacing in RTL mode */
    [dir="rtl"] .about-faq-section.rtl-faq {
        margin-right: 0 !important;
    }

    /* Responsive adjustments */
    @media (max-width: 767px) {
        [dir="rtl"] .about-faq-section .accordion-header .accordion-button,
        [dir="rtl"] .about-faq-section .accordion-item .accordion-body {
            padding: 12px 12px 12px 40px !important;
        }
    }

    /* RTL styles for CTA box section */
    [dir="rtl"] .cta-box .section-title h3,
    [dir="rtl"] .cta-box .section-title h2,
    [dir="rtl"] .cta-box .section-title p {
        text-align: right !important;
        font-family: var(--arabic-font) !important;
    }

    /* Fix for Arabic title rendering in CTA box */
    .cta-box .arabic-title {
        font-family: var(--arabic-font) !important;
        letter-spacing: 0 !important;
        word-spacing: normal !important;
        text-align: right !important;
        animation: none !important;
        display: inline-block !important;
        white-space: nowrap !important;
        direction: rtl !important;
    }

    /* Ensure the CTA box content is properly aligned in RTL */
    [dir="rtl"] .cta-box-content {
        text-align: right !important;
    }

    /* Maintain button position in RTL mode */
    [dir="rtl"] .cta-box .section-btn {
        text-align: right !important;
    }

    /* Ensure the CTA box maintains its position in RTL mode */
    [dir="rtl"] .cta-box .container,
    [dir="rtl"] .cta-box .row,
    [dir="rtl"] .cta-box .col-lg-12 {
        position: relative !important;
        left: auto !important;
        right: auto !important;
    }
</style>
@endpush

<style>
    /* Hover effects for mission/vision and CTA */
    .our-mva-item,
    .cta-infobar {
        transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
        border: 1px solid transparent;
        position: relative;
        overflow: hidden;
    }

    .our-mva-item:hover,
    .cta-infobar:hover {
        background-color: rgba(var(--primary-color-rgb), 0.08) !important;
        transform: translateY(-8px);
        box-shadow: 0 15px 30px rgba(var(--primary-color-rgb), 0.15);
        border-color: rgba(var(--primary-color-rgb), 0.2);
    }

    .our-mva-item:hover .icon-box,
    .cta-infobar:hover .icon-box {
        background-color: rgba(var(--primary-color-rgb), 0.15);
        transform: scale(1.1);
    }

    .our-mva-item:hover h3,
    .cta-infobar:hover h3 {
        color: var(--primary-color);
    }

    .our-mva-item::after,
    .cta-infobar::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 3px;
        background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
        transform: scaleX(0);
        transition: transform 0.5s ease;
    }

    .our-mva-item:hover::after,
    .cta-infobar:hover::after {
        transform: scaleX(1);
    }

    .icon-box {
        transition: all 0.4s ease;
    }

    .our-mva-item:hover .tiptap-content,
    .our-mva-item:hover p,
    .cta-infobar:hover p {
        color: rgba(var(--primary-color-rgb), 0.8);
    }
</style>

<script>
    // Fix scroll-to-top button specifically for about page
    document.addEventListener('DOMContentLoaded', function() {
        // Ensure the back-to-top button is visible and properly styled
        const backToTop = document.querySelector('.back-to-top');
        if (backToTop) {
            // Make sure the button is visible
            backToTop.style.display = 'block';
            backToTop.style.opacity = '1';
            backToTop.style.visibility = 'visible';

            // Ensure proper z-index
            backToTop.style.zIndex = '999';

            // Add click event listener
            backToTop.addEventListener('click', function(e) {
                e.preventDefault();
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });

            // Show/hide based on scroll position
            window.addEventListener('scroll', function() {
                if (window.scrollY > 300) {
                    backToTop.classList.add('active');
                } else {
                    backToTop.classList.remove('active');
                }
            }, { passive: true });
        } else {
            console.error('Back to top button not found on about page');

            // If button doesn't exist, create it
            const newBackToTop = document.createElement('div');
            newBackToTop.className = 'back-to-top';
            newBackToTop.innerHTML = `
                <svg class="back-to-top-circle" width="100%" height="100%" viewBox="-1 -1 102 102">
                    <path class="back-to-top-path" d="M50,1 a49,49 0 0,1 0,98 a49,49 0 0,1 0,-98" />
                </svg>
            `;
            document.body.appendChild(newBackToTop);

            // Style and add event listener
            newBackToTop.style.display = 'block';
            newBackToTop.style.zIndex = '999';
            newBackToTop.addEventListener('click', function() {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });

            // Show/hide based on scroll position
            window.addEventListener('scroll', function() {
                if (window.scrollY > 300) {
                    newBackToTop.classList.add('active');
                } else {
                    newBackToTop.classList.remove('active');
                }
            }, { passive: true });
        }
    });
</script>








































