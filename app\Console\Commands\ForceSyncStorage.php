<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\StorageSyncService;

// class ForceSyncStorage extends Command
// {
//     protected $signature = 'storage:force-sync';
//     protected $description = 'Force sync all files from storage/app/public to external storage';

//     protected $storageSync;

//     public function __construct(StorageSyncService $storageSync)
//     {
//         parent::__construct();
//         $this->storageSync = $storageSync;
//     }

//     public function handle()
//     {
//         $this->info('Starting force sync...');

//         if ($this->storageSync->syncAllFiles()) {
//             $this->info('Storage sync completed successfully!');
//         } else {
//             $this->error('Storage sync failed. Check logs for details.');
//         }
//     }
// }
