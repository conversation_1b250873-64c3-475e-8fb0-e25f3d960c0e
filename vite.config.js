import { defineConfig } from 'vite';
import laravel, { refreshPaths } from 'laravel-vite-plugin';

export default defineConfig({
    plugins: [
        laravel({
            input: ['resources/css/app.css', 'resources/js/app.js'],
            refresh: [
                ...refreshPaths,
                'app/Http/Livewire/**',
                'resources/views/livewire/**',
                'app/Filament/**/*.php',
            ],
            // Adjust this to match your server path
            publicDirectory: 'public',
            buildDirectory: 'build'
        }),
    ],
    build: {
        // Ensure manifest is generated
        manifest: true,
        // Output to public/build
        outDir: 'public/build',
        // Enable rollup options
        rollupOptions: {
            output: {
                // Ensure assets have consistent names
                manualChunks: undefined
            }
        }
    }
});











