<!-- Add these in the head section or before closing body -->
<script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

@push('styles')
<style>
    /* RTL styles for skeleton section */
    [dir="rtl"] .skeleton-tabs .nav-link {
        text-align: right;
        font-family: var(--arabic-font);
        padding-right: 15px;
        width: 100%;
        display: block;
    }

    /* Fix for the navigation menu in RTL mode */
    [dir="rtl"] .nav-pills {
        text-align: right;
        padding-right: 0;
    }

    [dir="rtl"] .skeleton-tabs {
        direction: rtl;
    }

    /* Make sure the mobile dropdown is also right-aligned */
    [dir="rtl"] .form-select {
        text-align: right;
        direction: rtl;
    }

    [dir="rtl"] .skeleton-content {
        text-align: right;
    }

    [dir="rtl"] .section-title h2,
    [dir="rtl"] .section-title p,
    [dir="rtl"] .skeleton-content h3,
    [dir="rtl"] .skeleton-content p {
        text-align: right;
        font-family: var(--arabic-font);
    }

    /* Fix content title and description alignment for RTL */
    [dir="rtl"] .content-title {
        text-align: right !important;
        font-family: var(--arabic-font) !important;
    }

    [dir="rtl"] .content-description {
        text-align: right;
        font-family: var(--arabic-font);
    }

    /* Adjust content wrapper for RTL - keep normal flex direction */
    [dir="rtl"] .content-wrapper {
        flex-direction: row;
        justify-content: space-between;
    }

    /* Flip the action button for RTL */
    [dir="rtl"] .action-btn {
        flex-direction: row-reverse;
    }

    [dir="rtl"] .action-btn svg {
        transform: scaleX(-1);
    }

    /* Fix tooltip placement in RTL mode */
    [dir="rtl"] .tooltip {
        direction: rtl;
    }
</style>
@endpush

<section class="skeleton-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 text-center">
                <div class="section-title">
                    <h2>{{ __('messages.Human Body Structure') }}</h2>
                    <p>{{ __('messages.Explore our interactive skeleton model') }}</p>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Desktop View (Left Side Tabs) -->
            <div class="col-lg-3 d-none d-lg-block">
                <div class="nav flex-column nav-pills skeleton-tabs" role="tablist">
                    <button class="nav-link active" data-bs-toggle="pill" data-bs-target="#head" type="button" data-part="head">{{ __('messages.Head & Neck') }}</button>
                    <button class="nav-link" data-bs-toggle="pill" data-bs-target="#spine" type="button" data-part="spine">{{ __('messages.Spine') }}</button>
                    <button class="nav-link" data-bs-toggle="pill" data-bs-target="#shoulder" type="button" data-part="shoulder">{{ __('messages.Shoulder') }}</button>
                    <button class="nav-link" data-bs-toggle="pill" data-bs-target="#arm" type="button" data-part="arm">{{ __('messages.Arms & Hands') }}</button>
                    <button class="nav-link" data-bs-toggle="pill" data-bs-target="#hip" type="button" data-part="hip">{{ __('messages.Hip') }}</button>
                    <button class="nav-link" data-bs-toggle="pill" data-bs-target="#knee" type="button" data-part="knee">{{ __('messages.Knee') }}</button>
                    <button class="nav-link" data-bs-toggle="pill" data-bs-target="#foot" type="button" data-part="foot">{{ __('messages.Foot & Ankle') }}</button>
                </div>
            </div>

            <!-- Mobile View (Top Tabs) -->
            <div class="col-12 d-lg-none mb-4">
                <div class="skeleton-tabs-mobile">
                    <select class="form-select" onchange="switchTab(this.value)">
                        <option value="head">{{ __('messages.Head & Neck') }}</option>
                        <option value="spine">{{ __('messages.Spine') }}</option>
                        <option value="shoulder">{{ __('messages.Shoulder') }}</option>
                        <option value="arm">{{ __('messages.Arms & Hands') }}</option>
                        <option value="hip">{{ __('messages.Hip') }}</option>
                        <option value="knee">{{ __('messages.Knee') }}</option>
                        <option value="foot">{{ __('messages.Foot & Ankle') }}</option>
                    </select>
                </div>
            </div>

            <!-- Content Area -->
            <div class="col-lg-9">
                <div class="tab-content">
                    <div class="skeleton-image position-relative">
                        <img src="{{ asset('assets/images/result.svg') }}" alt="Skeleton Model" class="img-fluid skeleton-svg mx-auto d-block">

                        <!-- Interactive Pulse Points -->
                        <div class="pulse-points">
                            <!-- Head & Neck points -->
                            <div class="pulse-point head-point"
                                 onclick="window.location.href='{{ route('bodypart.show', ['locale' => app()->getLocale(), 'part' => 'neck']) }}'"
                                 data-tab="head"
                                 data-bs-toggle="tooltip"
                                 data-bs-placement="right"
                                 title="Explore Head & Neck Treatments"
                                 style="cursor: pointer;">
                                <div class="pulse-dot"></div>
                            </div>

                            <!-- Spine points -->
                            <div class="pulse-point spine-point"
                                 onclick="window.location.href='{{ route('bodypart.show', ['locale' => app()->getLocale(), 'part' => 'spine']) }}'"
                                 data-tab="spine"
                                 data-bs-toggle="tooltip"
                                 data-bs-placement="right"
                                 title="Explore Spine Treatments"
                                 style="cursor: pointer;">
                                <div class="pulse-dot"></div>
                            </div>

                            <!-- Shoulder points -->
                            <div class="pulse-point shoulder-point"
                                 onclick="window.location.href='{{ route('bodypart.show', ['locale' => app()->getLocale(), 'part' => 'shoulder']) }}'"
                                 data-tab="shoulder"
                                 data-bs-toggle="tooltip"
                                 data-bs-placement="right"
                                 title="Explore Shoulder Treatments"
                                 style="cursor: pointer;">
                                <div class="pulse-dot"></div>
                            </div>

                            <!-- Arms points -->
                            <div class="pulse-point arm-point"
                                 onclick="window.location.href='{{ route('bodypart.show', ['locale' => app()->getLocale(), 'part' => 'hand-wrist']) }}'"
                                 data-tab="arm"
                                 data-bs-toggle="tooltip"
                                 data-bs-placement="right"
                                 title="Explore Arms & Hands Treatments"
                                 style="cursor: pointer;">
                                <div class="pulse-dot"></div>
                            </div>

                            <!-- Hip points -->
                            <div class="pulse-point hip-point"
                                 onclick="window.location.href='{{ route('bodypart.show', ['locale' => app()->getLocale(), 'part' => 'hip']) }}'"
                                 data-tab="hip"
                                 data-bs-toggle="tooltip"
                                 data-bs-placement="right"
                                 title="Explore Hip Treatments"
                                 style="cursor: pointer;">
                                <div class="pulse-dot"></div>
                            </div>

                            <!-- Knee points -->
                            <div class="pulse-point knee-point"
                                 onclick="window.location.href='{{ route('bodypart.show', ['locale' => app()->getLocale(), 'part' => 'knee']) }}'"
                                 data-tab="knee"
                                 data-bs-toggle="tooltip"
                                 data-bs-placement="right"
                                 title="Explore Knee Treatments"
                                 style="cursor: pointer;">
                                <div class="pulse-dot"></div>
                            </div>

                            <!-- Foot points -->
                            <div class="pulse-point foot-point"
                                 onclick="window.location.href='{{ route('bodypart.show', ['locale' => app()->getLocale(), 'part' => 'foot-ankle']) }}'"
                                 data-tab="foot"
                                 data-bs-toggle="tooltip"
                                 data-bs-placement="right"
                                 title="Explore Foot & Ankle Treatments"
                                 style="cursor: pointer;">
                                <div class="pulse-dot"></div>
                            </div>
                        </div>
                    </div>
                    <div class="tab-pane fade show active" id="head">
                        <div class="skeleton-content">
                            <div class="skeleton-info mt-4">
                                <div class="info-content">
                                    <h3 class="content-title">{{ __('messages.Head & Neck') }}</h3>
                                    <div class="content-wrapper">
                                        <p class="content-description">{{ __('messages.Common conditions affecting the head and neck region...') }}</p>
                                        <a href="{{ route('bodypart.show', ['locale' => app()->getLocale(), 'part' => 'neck']) }}" class="action-btn">
                                            <span>{{ __('messages.Learn More') }}</span>
                                            <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M12 4L10.59 5.41L16.17 11H4V13H16.17L10.59 18.59L12 20L20 12L12 4Z" fill="currentColor"/>
                                            </svg>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="tab-pane fade" id="spine">
                        <div class="skeleton-content">
                            <div class="skeleton-info mt-4">
                                <div class="info-content">
                                    <h3 class="content-title">{{ __('messages.Spine') }}</h3>
                                    <div class="content-wrapper">
                                        <p class="content-description">{{ __('messages.Common conditions affecting the spine and vertebral column...') }}</p>
                                        <a href="{{ route('bodypart.show', ['locale' => app()->getLocale(), 'part' => 'spine']) }}" class="action-btn">
                                            <span>{{ __('messages.Learn More') }}</span>
                                            <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M12 4L10.59 5.41L16.17 11H4V13H16.17L10.59 18.59L12 20L20 12L12 4Z" fill="currentColor"/>
                                            </svg>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="tab-pane fade" id="shoulder">
                        <div class="skeleton-content">
                            <div class="skeleton-info mt-4">
                                <div class="info-content">
                                    <h3 class="content-title">{{ __('messages.Shoulder') }}</h3>
                                    <div class="content-wrapper">
                                        <p class="content-description">{{ __('messages.Common conditions affecting the shoulder joint and surrounding tissues...') }}</p>
                                        <a href="{{ route('bodypart.show', ['locale' => app()->getLocale(), 'part' => 'shoulder']) }}" class="action-btn">
                                            <span>{{ __('messages.Learn More') }}</span>
                                            <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M12 4L10.59 5.41L16.17 11H4V13H16.17L10.59 18.59L12 20L20 12L12 4Z" fill="currentColor"/>
                                            </svg>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="tab-pane fade" id="arm">
                        <div class="skeleton-content">
                            <div class="skeleton-info mt-4">
                                <div class="info-content">
                                    <h3 class="content-title">{{ __('messages.Arms & Hands') }}</h3>
                                    <div class="content-wrapper">
                                        <p class="content-description">{{ __('messages.Common conditions affecting the upper extremities including arms, wrists, and hands...') }}</p>
                                        <a href="{{ route('bodypart.show', ['locale' => app()->getLocale(), 'part' => 'hand-wrist']) }}" class="action-btn">
                                            <span>{{ __('messages.Learn More') }}</span>
                                            <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M12 4L10.59 5.41L16.17 11H4V13H16.17L10.59 18.59L12 20L20 12L12 4Z" fill="currentColor"/>
                                            </svg>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="tab-pane fade" id="hip">
                        <div class="skeleton-content">
                            <div class="skeleton-info mt-4">
                                <div class="info-content">
                                    <h3 class="content-title">{{ __('messages.Hip') }}</h3>
                                    <div class="content-wrapper">
                                        <p class="content-description">{{ __('messages.Common conditions affecting the hip joint and surrounding areas...') }}</p>
                                        <a href="{{ route('bodypart.show', ['locale' => app()->getLocale(), 'part' => 'hip']) }}" class="action-btn">
                                            <span>{{ __('messages.Learn More') }}</span>
                                            <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M12 4L10.59 5.41L16.17 11H4V13H16.17L10.59 18.59L12 20L20 12L12 4Z" fill="currentColor"/>
                                            </svg>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="tab-pane fade" id="knee">
                        <div class="skeleton-content">
                            <div class="skeleton-info mt-4">
                                <div class="info-content">
                                    <h3 class="content-title">{{ __('messages.Knee') }}</h3>
                                    <div class="content-wrapper">
                                        <p class="content-description">{{ __('messages.Common conditions affecting the knee joint and surrounding structures...') }}</p>
                                        <a href="{{ route('bodypart.show', ['locale' => app()->getLocale(), 'part' => 'knee']) }}" class="action-btn">
                                            <span>{{ __('messages.Learn More') }}</span>
                                            <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M12 4L10.59 5.41L16.17 11H4V13H16.17L10.59 18.59L12 20L20 12L12 4Z" fill="currentColor"/>
                                            </svg>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="tab-pane fade" id="foot">
                        <div class="skeleton-content">
                            <div class="skeleton-info mt-4">
                                <div class="info-content">
                                    <h3 class="content-title">{{ __('messages.Foot & Ankle') }}</h3>
                                    <div class="content-wrapper">
                                        <p class="content-description">{{ __('messages.Common conditions affecting the foot, ankle, and lower extremities...') }}</p>
                                        <a href="{{ route('bodypart.show', ['locale' => app()->getLocale(), 'part' => 'foot-ankle']) }}" class="action-btn">
                                            <span>{{ __('messages.Learn More') }}</span>
                                            <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M12 4L10.59 5.41L16.17 11H4V13H16.17L10.59 18.59L12 20L20 12L12 4Z" fill="currentColor"/>
                                            </svg>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
    .skeleton-section {
        padding: 60px 0;
    }

    .skeleton-tabs .nav-link {
        color: #333;
        border-radius: 8px;
        margin-bottom: 10px;
        text-align: right;
        padding: 12px 20px;
        transition: all 0.3s ease;
    }

    .skeleton-tabs .nav-link:hover {
        background-color: #f8f9fa;
    }

    .skeleton-tabs .nav-link.active {
        background-color: var(--primary-color);
        color: white;
    }

    .skeleton-tabs-mobile {
        position: sticky;
        top: 0;
        z-index: 100;
        background: white;
        padding: 10px 0;
    }

    .skeleton-tabs-mobile .form-select {
        border-radius: 8px;
        padding: 12px;
        font-size: 16px;
        border: 1px solid #ddd;
    }

    .skeleton-content {
        background: white;
        border-radius: 12px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    }

    .skeleton-image {
        text-align: center;
        position: relative;
        max-width: 500px;
        margin: 0 auto;
    }

    .pulse-points {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
    }

    .pulse-point {
        position: absolute;
        width: 40px;
        height: 40px;
        pointer-events: auto;
        cursor: pointer;
        display: none; /* Hidden by default */
    }

    .pulse-dot, .pulse-dot::after {
        width: 15px;
        height: 15px;
        background-color: var(--primary-color);
        border-radius: 50%;
    }

    .pulse-dot::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% {
            transform: translate(-50%, -50%) scale(1);
            opacity: 0.8;
        }
        70% {
            transform: translate(-50%, -50%) scale(8);
            opacity: 0;
        }
        100% {
            transform: translate(-50%, -50%) scale(8);
            opacity: 0;
        }
    }

    /* Show pulse point for active tab */
    .highlight-head .head-point,
    .highlight-spine .spine-point,
    .highlight-shoulder .shoulder-point,
    .highlight-arm .arm-point,
    .highlight-hip .hip-point,
    .highlight-knee .knee-point,
    .highlight-foot .foot-point {
        display: block;
    }

    /* Position each pulse point */
    .head-point {
        top: 13%;
        left: 52%;
        transform: translateX(-50%);
    }

    .spine-point {
        top: 33%;
        left: 53%;
        transform: translateX(-50%);
    }

    .shoulder-point {
        top: 19%;
        left: 59%;
    }

    .arm-point {
        top: 34%;
        left: 62%;
    }

    .hip-point {
        top: 48%;
        left: 59%;
        transform: translateX(-50%);
    }

    .knee-point {
        top: 70%;
        left: 41%;
    }

    .foot-point {
        top: 92%;
        left: 59%;
    }

    /* Tooltip customization */
    .tooltip {
        font-size: 14px;
    }

    .tooltip-inner {
        background-color: var(--primary-color);
        padding: 8px 12px;
    }

    .skeleton-image img {
        max-height: 500px;
        width: auto;
    }

    /* SVG Highlighting Styles */
    .skeleton-svg {
        max-height: 500px;
        width: auto;
    }

    .body-highlight {
        opacity: 0;
        transition: opacity 0.3s ease;
        fill: var(--primary-color);
        fill-opacity: 0.2;
        stroke: var(--primary-color);
        stroke-width: 1.5;
    }

    /* Highlight states for each body part */
    .highlight-head #head-highlight,
    .highlight-spine #spine-highlight,
    .highlight-shoulder #shoulder-highlight,
    .highlight-arm #arm-highlight,
    .highlight-hip #hip-highlight,
    .highlight-knee #knee-highlight,
    .highlight-foot #foot-highlight {
        opacity: 1;
    }

    #base-skeleton {
        fill: #e0e0e0;
        stroke: #999;
        stroke-width: 0.5;
    }

    @media (max-width: 991px) {
        .skeleton-section {
            padding: 40px 0;
        }

        .skeleton-image img {
            max-height: 400px;
        }
    }

    @media (max-width: 767px) {
        .skeleton-image img {
            max-height: 300px;
        }
    }

    /* Body Part Popup Styles */
    .body-part-popup {
        position: absolute;
        background: white;
        padding: 20px;
        border-radius: 12px;
        box-shadow: 0 4px 25px rgba(0, 0, 0, 0.15);
        max-width: 300px;
        z-index: 1000;
        opacity: 0;
        visibility: hidden;
        transform: translateY(10px);
        transition: all 0.3s ease;
        border-left: 4px solid var(--primary-color);
    }

    .body-part-popup.show {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
    }

    .popup-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
    }

    .popup-title {
        font-size: 18px;
        font-weight: 600;
        color: var(--primary-color);
    }

    .popup-close {
        cursor: pointer;
        padding: 4px;
        line-height: 1;
        border-radius: 50%;
        transition: background 0.2s;
    }

    .popup-close:hover {
        background: rgba(0, 0, 0, 0.05);
    }

    .popup-content {
        margin-bottom: 15px;
        color: #666;
        line-height: 1.5;
    }

    .popup-button {
        display: inline-block;
        padding: 8px 16px;
        background: var(--primary-color);
        color: white;
        border-radius: 6px;
        text-decoration: none;
        font-size: 14px;
        font-weight: 500;
        transition: all 0.2s;
    }

    .popup-button:hover {
        background: var(--primary-color-dark);
        transform: translateY(-1px);
    }

    /* Mobile Responsive Styles */
    @media (max-width: 768px) {
        .body-part-popup {
            position: fixed;
            left: 50% !important;
            top: 50% !important;
            transform: translate(-50%, -50%);
            width: 90%;
            max-width: 320px;
            margin: 0 auto;
        }

        .body-part-popup.show {
            transform: translate(-50%, -50%);
        }

        .popup-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .popup-overlay.show {
            opacity: 1;
            visibility: visible;
        }
    }



    .content-title {
        /* color: #000000;  */
        color: var(--primary-color);
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 16px;
        letter-spacing: -0.02em;
    }

    .content-wrapper {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 24px;
    }

    .content-description {
        margin: 0;
        font-size: 1.05rem;
        line-height: 1.6;
        color: #4B5563;
        flex: 1;
    }

    .action-btn {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 12px 24px;
        background: var(--primary-color);
        color: white;
        border-radius: 12px;
        font-weight: 500;
        font-size: 0.95rem;
        text-decoration: none;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        border: 2px solid var(--primary-color);
        white-space: nowrap;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        position: relative;
        overflow: hidden;
    }

    .action-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(
            45deg,
            rgba(255, 255, 255, 0.1),
            rgba(255, 255, 255, 0.2)
        );
        transform: translateX(-100%) skewX(-15deg);
        transition: transform 0.5s ease;
    }

    .action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
        color: white;
    }

    .action-btn:hover::before {
        transform: translateX(100%) skewX(-15deg);
    }

    .action-btn svg {
        transition: transform 0.3s ease;
    }

    .action-btn:hover svg {
        transform: translateX(4px);
    }

    /* Update glass morphism effect for light mode */
    @supports (backdrop-filter: blur(10px)) {
    }

    /* Dark mode support - only if you want to keep it */
    @media (prefers-color-scheme: dark) {

        .content-title {
            /* color: #000000;   */
            /* Keep black heading in dark mode */
            color: var(--primary-color);
        }

        .content-description {
            color: #4B5563;  /* Keep original text color */
        }
    }

    /* Mobile responsive design */
    @media (max-width: 768px) {

        .content-title {
            font-size: 1.25rem;
            margin-bottom: 12px;
        }

        .content-wrapper {
            flex-direction: column;
            align-items: stretch;
            gap: 16px;
        }

        .content-description {
            font-size: 1rem;
        }

        .action-btn {
            width: 100%;
            justify-content: center;
            padding: 14px 20px;
            font-size: 1rem;
        }
    }

    /* Tablet responsive design */
    @media (min-width: 769px) and (max-width: 1024px) {
        .content-wrapper {
            gap: 20px;
        }
    }

    /* Animation for content loading */
    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }


</style>

<script>
    function switchTab(tabId) {
        // Remove all highlight classes
        const svgContainer = document.querySelector('.skeleton-image');
        svgContainer.classList.remove(
            'highlight-head',
            'highlight-spine',
            'highlight-shoulder',
            'highlight-arm',
            'highlight-hip',
            'highlight-knee',
            'highlight-foot'
        );

        // Add highlight class for selected part
        svgContainer.classList.add(`highlight-${tabId}`);

        // Bootstrap's tab API to switch tabs
        const tab = new bootstrap.Tab(document.querySelector(`[data-bs-target="#${tabId}"]`));
        tab.show();

        // Hide all tooltips when switching tabs
        const tooltips = bootstrap.Tooltip.getInstance('[data-bs-toggle="tooltip"]');
        if (tooltips) {
            tooltips.hide();
        }
    }

    // Initialize highlighting for the first tab
    document.addEventListener('DOMContentLoaded', () => {
        document.querySelector('.skeleton-image').classList.add('highlight-head');

        // Add click handlers for desktop tabs
        document.querySelectorAll('.nav-link').forEach(tab => {
            tab.addEventListener('click', (e) => {
                const part = e.target.getAttribute('data-part');
                switchTab(part);
            });
        });

        // Initialize tooltips
        const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]');
        [...tooltipTriggerList].map(tooltipTriggerEl => new bootstrap.Tooltip(tooltipTriggerEl, {
            trigger: 'hover',
            placement: 'right'
        }));

        // Add click handlers for pulse points
        document.querySelectorAll('.pulse-point').forEach(point => {
            point.addEventListener('click', (e) => {
                const tab = e.currentTarget.getAttribute('data-tab');
                switchTab(tab);
            });
        });
    });
</script>









<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Define pulse points positions for different breakpoints
        const pulsePositions = {
            head: {
                default: { top: '13%', left: '52%' },
                tablet: { top: '10%', left: '48%' },
                mobile: { top: '11%', left: '53%' }
            },
            spine: {
                default: { top: '33%', left: '53%' },
                tablet: { top: '27%', left: '48%' },
                mobile: { top: '32%', left: '53%' }
            },
            shoulder: {
                default: { top: '19%', left: '59%' },
                tablet: { top: '17%', left: '23%' },
                mobile: { top: '18%', left: '56%' }
            },
            arm: {
                default: { top: '34%', left: '62%' },
                tablet: { top: '32%', left: '13%' },
                mobile: { top: '33%', left: '59%' }
            },
            hip: {
                default: { top: '48%', left: '59%' },
                tablet: { top: '47%', left: '33%' },
                mobile: { top: '47%', left: '56%' }
            },
            knee: {
                default: { top: '70%', left: '41%' },
                tablet: { top: '67%', left: '43%' },
                mobile: { top: '69%', left: '41%' }
            },
            foot: {
                default: { top: '92%', left: '59%' },
                tablet: { top: '87%', left: '43%' },
                mobile: { top: '91%', left: '57%' }
            }
        };

        // Function to update pulse point positions based on screen size
        function updatePulsePositions() {
            const width = window.innerWidth;
            let breakpoint = 'default';

            if (width <= 576) { // Mobile
                breakpoint = 'mobile';
            } else if (width <= 992) { // Tablet
                breakpoint = 'tablet';
            }

            // Update each pulse point position
            Object.keys(pulsePositions).forEach(part => {
                const point = document.querySelector(`.${part}-point`);
                if (point) {
                    const position = pulsePositions[part][breakpoint];
                    point.style.top = position.top;
                    point.style.left = position.left;
                }
            });
        }

        // Initial position update
        updatePulsePositions();

        // Update positions on window resize
        let resizeTimeout;
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(updatePulsePositions, 100);
        });

        // Function to update positions based on SVG scale
        function updatePositionsBasedOnSVG() {
            const svg = document.querySelector('.skeleton-svg');
            if (!svg) return;

            const svgRect = svg.getBoundingClientRect();
            const scale = svgRect.width / svg.naturalWidth;

            // Update positions based on scale if needed
            Object.keys(pulsePositions).forEach(part => {
                const point = document.querySelector(`.${part}-point`);
                if (point) {
                    // You can add additional scaling logic here if needed
                    // Example:
                    // point.style.transform = `scale(${Math.max(0.8, Math.min(1.2, scale))})`;
                }
            });
        }

        // Observe SVG size changes
        const resizeObserver = new ResizeObserver(() => {
            updatePositionsBasedOnSVG();
        });

        const svg = document.querySelector('.skeleton-svg');
        if (svg) {
            resizeObserver.observe(svg);
        }

        // Function to dynamically update pulse point position
        function updatePulsePoint(partId, position) {
            const point = document.querySelector(`.${partId}-point`);
            if (point && position) {
                if (position.top) point.style.top = position.top;
                if (position.left) point.style.left = position.left;
                if (position.right) point.style.right = position.right;
                if (position.bottom) point.style.bottom = position.bottom;
            }
        }

        // Example usage:
        // updatePulsePoint('head', { top: '10%', left: '50%' });
    });
</script>















<script>
    document.addEventListener('DOMContentLoaded', () => {
        const pulsePoints = document.querySelectorAll('.pulse-point');

        pulsePoints.forEach(point => {
            point.addEventListener('click', (e) => {
                // First switch the tab
                const tab = e.currentTarget.getAttribute('data-tab');
                switchTab(tab);

                // Hide tooltip before navigation
                const tooltip = bootstrap.Tooltip.getInstance(point);
                if (tooltip) {
                    tooltip.hide();
                }

                // Small delay before navigation to allow tab switch animation
                setTimeout(() => {
                    // Let the onclick handler handle the navigation
                }, 200);
            });
        });
    });
</script>























