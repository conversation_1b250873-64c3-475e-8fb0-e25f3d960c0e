<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class LanguageMiddleware
{
    public function handle(Request $request, Closure $next): Response
    {
        // Skip locale check for admin routes
        if (str_starts_with($request->path(), 'admin')) {
            return $next($request);
        }

        // Get locale from URL
        $locale = $request->segment(1);

        // Check if locale is valid
        if (in_array($locale, ['en', 'ar'])) {
            app()->setLocale($locale);
            session(['language' => $locale]); // Optional: store in session
        } else {
            // If no valid locale is present, redirect to default locale
            return redirect('/' . config('app.locale') . $request->getRequestUri());
        }

        return $next($request);
    }
}




