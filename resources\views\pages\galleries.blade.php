@extends('layouts.app')  <!-- Extend the base layout -->


@section('content')
    <!-- Hero Section with Parallax -->
    {{-- <div class="page-header gallery-page-header bg-radius-section parallaxie" data-bg-image="{{ asset('assets/images/gallery-hero-bg.jpg') }}">
        <div class="overlay-gradient"></div>
        <div class="container position-relative">
            <div class="row align-items-center min-vh-40">
                <div class="col-lg-12">
                    <div class="page-header-box text-center">
                        <h1 class="display-4 text-white text-anime-style-2 mb-3" data-cursor="-opaque">
                            {{ __('messages.Our Gallery') }}
                        </h1>
                        <p class="lead text-white-75 mb-4 wow fadeInUp" data-wow-delay="0.2s">
                            {{ __('messages.Explore our collection of medical excellence and patient care') }}
                        </p>
                        <nav class="wow fadeInUp" data-wow-delay="0.3s">
                            <ol class="breadcrumb justify-content-center bg-transparent">
                                <li class="breadcrumb-item">
                                    <a href="{{ url(app()->getLocale()) }}" class="text-white-50">
                                        <i class="fas fa-home me-1"></i>{{ __('messages.home') }}
                                    </a>
                                </li>
                                <li class="breadcrumb-item active text-white" aria-current="page">
                                    {{ __('messages.Gallery') }}
                                </li>
                            </ol>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div> --}}
	<div class="page-header gallery-page-header bg-radius-section parallaxie" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
		<div class="container">
			<div class="row align-items-center">
				<div class="col-lg-12">
					<!-- Page Header Box Start -->
					<div class="page-header-box">
						<h1 class="{{ app()->getLocale() === 'ar' ? 'arabic-title' : 'text-anime-style-2' }}" data-cursor="-opaque">{{ __('messages.Our Gallery') }}</h1>
						<!-- Custom breadcrumb for RTL/LTR support -->
						@if(app()->getLocale() == 'ar')
							<!-- Arabic RTL breadcrumb -->
							<nav class="wow fadeInUp custom-rtl-breadcrumb">
								<div class="rtl-breadcrumb">
									<a href="{{ url(app()->getLocale()) }}" style="color: rgba(255, 255, 255, 0.7) !important; text-decoration: none !important; font-size: 16px;">{{ __('messages.Home') }}</a>
									<span class="separator" style="color: rgba(255, 255, 255, 0.7) !important; font-size: 16px;">/</span>
									<span style="color: #fff !important; font-size: 16px;">{{ __('messages.Our Gallery') }}</span>
								</div>
							</nav>
						@else
							<!-- English LTR breadcrumb -->
							<nav class="wow fadeInUp">
								<ol class="breadcrumb">
									<li class="breadcrumb-item"><a href="{{ url(app()->getLocale()) }}">{{ __('messages.Home') }}</a></li>
									<li class="breadcrumb-item active" aria-current="page">{{ __('messages.Our Gallery') }}</li>
								</ol>
							</nav>
						@endif
					</div>
					<!-- Page Header Box End -->
				</div>
			</div>
		</div>
	</div>

    <!-- Gallery Section -->
    <section class="gallery-section py-5">
        <div class="container">
            <!-- Filter Buttons -->
            <div class="gallery-filter mb-5">
                <div class="d-flex justify-content-center flex-wrap gap-3">
                    <button class="btn btn-filter active" data-filter="all">
                        <span class="btn-text">{{ __('messages.All') }}</span>
                        <span class="filter-counter badge bg-white text-primary">0</span>
                    </button>
                    <button class="btn btn-filter" data-filter="image">
                        <i class="fas fa-image me-2"></i>
                        <span class="btn-text">{{ __('messages.Images') }}</span>
                        <span class="filter-counter badge bg-white text-primary">0</span>
                    </button>
                    <button class="btn btn-filter" data-filter="video">
                        <i class="fas fa-video me-2"></i>
                        <span class="btn-text">{{ __('messages.Videos') }}</span>
                        <span class="filter-counter badge bg-white text-primary">0</span>
                    </button>
                </div>
            </div>

            <!-- Gallery Grid -->
            @php
                // Group galleries by date
                $groupedGalleries = $galleries->groupBy(function($gallery) {
                    $date = \Carbon\Carbon::parse($gallery->created_at);
                    if ($date->isToday()) {
                        return 'Today';
                    } elseif ($date->isYesterday()) {
                        return 'Yesterday';
                    } else {
                        return $date->format('d F Y');
                    }
                });
            @endphp

            <div class="gallery-container">
                @forelse($groupedGalleries as $date => $items)
                    <div class="date-group mb-5">
                        <h3 class="date-header mb-4">{{ $date }}</h3>
                        <div class="page-gallery-grid">
                            @foreach($items as $gallery)
                                <div class="page-gallery-item gallery-item" data-category="{{ $gallery->type }}">
                                    <div class="page-gallery-card">
                                        @if($gallery->type === 'image')
                                            <a href="{{ asset($gallery->getMediaUrl()) }}"
                                               data-fancybox="gallery-{{ \Str::slug($date) }}"
                                               class="gallery-link image-anime"
                                               data-caption="{{ app()->getLocale() === 'en' ? $gallery->title_en : $gallery->title_ar }}"
                                               data-cursor-text="{{ __('messages.View Image') }}">
                                                <div class="page-gallery-img-wrapper">
                                                    <!-- Type Badge -->
                                                    <div class="gallery-type-badge">
                                                        <i class="fas fa-image me-2"></i>{{ __('messages.Image') }}
                                                    </div>
                                                    <img src="{{ asset($gallery->getMediaUrl()) }}"
                                                         alt="{{ app()->getLocale() === 'en' ? $gallery->title_en : $gallery->title_ar }}"
                                                         class="page-gallery-img"
                                                         loading="lazy"
                                                         onerror="this.onerror=null; this.src='https://ui-avatars.com/api/?background=random&name={{ urlencode($gallery->title_en) }}'">
                                                    <div class="page-gallery-overlay">
                                                        <i class="fas fa-search-plus page-gallery-icon"></i>
                                                    </div>
                                                </div>
                                            </a>
                                        @else
                                            <a href="{{ $gallery->getVideoUrl() }}"
                                               data-fancybox="gallery-{{ \Str::slug($date) }}"
                                               class="gallery-link image-anime"
                                               data-type="{{ $gallery->getVideoType() }}"
                                               data-caption="{{ app()->getLocale() === 'en' ? $gallery->title_en : $gallery->title_ar }}"
                                               data-cursor-text="{{ __('messages.Play Video') }}">
                                                <div class="page-gallery-img-wrapper">
                                                    <!-- Type Badge -->
                                                    <div class="gallery-type-badge">
                                                        <i class="fas fa-video me-2"></i>{{ __('messages.Video') }}
                                                    </div>
                                                    <img src="{{ $gallery->getThumbnailUrl() ?
                                                              asset($gallery->getThumbnailUrl()) :
                                                              'https://ui-avatars.com/api/?background=random&name=' . urlencode($gallery->title_en) }}"
                                                         alt="{{ app()->getLocale() === 'en' ? $gallery->title_en : $gallery->title_ar }}"
                                                         class="page-gallery-img"
                                                         loading="lazy"
                                                         onerror="this.onerror=null; this.src='https://ui-avatars.com/api/?background=random&name={{ urlencode($gallery->title_en) }}'">
                                                    <div class="page-gallery-overlay">
                                                        <i class="fas fa-play page-gallery-icon"></i>
                                                    </div>
                                                </div>
                                            </a>
                                        @endif
                                        <div class="page-gallery-caption">
                                            <h5 class="page-gallery-title">{{ app()->getLocale() === 'en' ? $gallery->title_en : $gallery->title_ar }}</h5>
                                            <p class="page-gallery-desc">{!! \Str::limit(strip_tags(app()->getLocale() === 'en' ? $gallery->description_en : $gallery->description_ar), 100) !!}</p>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @empty
                    <div class="col-12">
                        <div class="empty-state text-center py-5">
                            <div class="empty-state-icon mb-4">
                                <i class="fas fa-images"></i>
                            </div>
                            <h3 class="empty-state-title">{{ __('messages.No Content Yet') }}</h3>
                            <p class="empty-state-description">{{ __('messages.No galleries found') }}</p>
                        </div>
                    </div>
                @endforelse
            </div>
        </div>
    </section>
@endsection

@push('styles')
<style>
    /* Hero Section Enhancement */
    .gallery-page-header {
        position: relative;
        background-size: cover;
        background-position: center;
        background-attachment: fixed;
    }

    .overlay-gradient {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(0,0,0,0.85) 0%, rgba(0,0,0,0.3) 100%);
    }

    .min-vh-40 {
        min-height: 45vh;
    }

    .page-header-box {
        padding: 2rem;
    }

    .text-anime-style-2 {
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    }

    /* Enhanced Filter Buttons */
    .gallery-filter {
        position: relative;
        margin-top: -50px;
        z-index: 10;
        background: rgba(255, 255, 255, 0.95);
        padding: 1.5rem;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        backdrop-filter: blur(10px);
    }

    .btn-filter {
        position: relative;
        padding: 0.85rem 2rem;
        border-radius: 50px;
        background: var(--bs-white);
        color: var(--primary-color);
        border: 2px solid var(--primary-color);
        transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
        font-weight: 600;
        letter-spacing: 0.5px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.05);
    }

    .btn-filter:hover, .btn-filter.active {
        background: var(--primary-color);
        color: white;
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(var(--primary-rgb), 0.25);
    }

    .btn-filter .filter-counter {
        position: absolute;
        top: -10px;
        right: -10px;
        min-width: 24px;
        height: 24px;
        border-radius: 12px;
        font-size: 0.8rem;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        border: 2px solid var(--primary-color);
    }

    /* Gallery Grid Layout */
    .page-gallery-grid {
        margin: 0 -15px;
    }

    .page-gallery-item {
        padding: 15px;
        margin-bottom: 30px;
    }

    /* Gallery Card Styles */
    .page-gallery-card {
        position: relative;
        background: var(--white-color);
        border-radius: 24px;
        overflow: hidden;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    .page-gallery-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 45px rgba(0, 0, 0, 0.15);
    }

    /* Image Wrapper */
    .page-gallery-img-wrapper {
        position: relative;
        width: 100%;
        padding-bottom: 100%; /* 1:1 Aspect Ratio */
        overflow: hidden;
        background: #f8f9fa;
    }

    .page-gallery-img {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.6s cubic-bezier(0.165, 0.84, 0.44, 1);
    }

    .page-gallery-card:hover .page-gallery-img {
        transform: scale(1.1);
    }

    /* Overlay Styles */
    .page-gallery-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(to bottom, rgba(0,0,0,0.2), rgba(0,0,0,0.7));
        opacity: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.4s ease;
        backdrop-filter: blur(5px);
    }

    .page-gallery-card:hover .page-gallery-overlay {
        opacity: 1;
    }

    .page-gallery-icon {
        color: white;
        font-size: 2rem;
        transform: translateY(20px);
        transition: all 0.4s ease;
    }

    .page-gallery-card:hover .page-gallery-icon {
        transform: translateY(0);
    }

    /* Caption Styles */
    .page-gallery-caption {
        padding: 1.5rem;
        background: white;
    }

    .page-gallery-title {
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        line-height: 1.4;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
    }

    .page-gallery-desc {
        font-size: 0.9rem;
        color: var(--body-color);
        margin-bottom: 0;
        line-height: 1.6;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
    }

    /* Responsive Adjustments */
    @media (max-width: 1200px) {
        .page-gallery-card {
            border-radius: 20px;
        }
    }

    @media (max-width: 768px) {
        .page-gallery-caption {
            padding: 1.25rem;
        }

        .page-gallery-title {
            font-size: 1rem;
            -webkit-line-clamp: 1;
        }

        .page-gallery-desc {
            font-size: 0.85rem;
            -webkit-line-clamp: 2;
        }
    }

    /* Enhanced Video Duration Badge */
    .video-duration {
        position: absolute;
        bottom: 15px;
        right: 15px;
        background: rgba(0,0,0,0.85);
        color: white;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 500;
        backdrop-filter: blur(5px);
        box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        z-index: 2;
    }

    /* Enhanced Caption Styles */
    .gallery-caption {
        padding: 1.75rem;
        background: white;
        position: relative;
    }

    .gallery-caption::before {
        content: '';
        position: absolute;
        top: 0;
        left: 20px;
        right: 20px;
        height: 1px;
        background: linear-gradient(to right, transparent, rgba(0,0,0,0.1), transparent);
    }

    .gallery-title {
        font-size: 1.2rem;
        font-weight: 700;
        margin-bottom: 0.75rem;
        color: var(--bs-dark);
        line-height: 1.4;
    }

    .gallery-desc {
        font-size: 0.95rem;
        color: var(--bs-gray-600);
        margin: 0;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        line-height: 1.6;
    }

    /* Enhanced Empty State */
    .empty-state {
        padding: 5rem 2rem;
        background: linear-gradient(135deg, #f8f9fa, #fff);
        border-radius: 30px;
        box-shadow: 0 10px 40px rgba(0,0,0,0.05);
    }

    .empty-state-icon {
        font-size: 5rem;
        color: var(--primary-color);
        opacity: 0.5;
        margin-bottom: 2rem;
    }

    .empty-state-title {
        font-size: 1.75rem;
        color: var(--bs-gray-800);
        margin-bottom: 1rem;
        font-weight: 700;
    }

    .empty-state-description {
        color: var(--bs-gray-600);
        max-width: 500px;
        margin: 0 auto;
        font-size: 1.1rem;
        line-height: 1.6;
    }

    /* Responsive Enhancements */
    @media (max-width: 1200px) {
        .gallery-title {
            font-size: 1.1rem;
        }
        .gallery-desc {
            font-size: 0.9rem;
        }
    }

    @media (max-width: 991px) {
        .min-vh-40 {
            min-height: 35vh;
        }
        .gallery-filter {
            margin-top: -30px;
        }
    }

    @media (max-width: 768px) {
        .gallery-filter {
            margin-top: -20px;
            padding: 1rem;
        }
        .btn-filter {
            padding: 0.75rem 1.5rem;
            font-size: 0.9rem;
        }
        .gallery-caption {
            padding: 1.25rem;
        }
    }

    /* Animation Classes */
    .wow {
        visibility: hidden;
    }

    .fadeInUp {
        animation-name: fadeInUp;
        animation-duration: 1s;
        animation-fill-mode: both;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translate3d(0, 30px, 0);
        }
        to {
            opacity: 1;
            transform: translate3d(0, 0, 0);
        }
    }

    /* Enhanced Date Design */
    .date-header {
        position: relative;
        color: var(--primary-color);
        font-size: 1.5rem;
        font-weight: 600;
        margin-left: 1rem;
        padding-left: 1rem;
        border-left: 4px solid var(--primary-color);
    }

    .date-group {
        opacity: 0;
        transform: translateY(20px);
        animation: fadeInUp 0.6s ease forwards;
    }

    @keyframes fadeInUp {
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .gallery-container {
        position: relative;
    }

    /* Adjust gallery item sizes */
    .gallery-item {
        transition: all 0.4s ease-in-out;
    }

    @media (min-width: 1200px) {
        .gallery-item {
            flex: 0 0 25%;
            max-width: 25%;
        }
    }

    @media (max-width: 1199px) {
        .gallery-item {
            flex: 0 0 33.333%;
            max-width: 33.333%;
        }
    }

    @media (max-width: 991px) {
        .gallery-item {
            flex: 0 0 50%;
            max-width: 50%;
        }
    }

    @media (max-width: 576px) {
        .gallery-item {
            flex: 0 0 100%;
            max-width: 100%;
        }
    }
</style>
@endpush

@push('scripts')
<script>
$(document).ready(function() {
    // Fancybox Configuration
    Fancybox.bind("[data-fancybox]", {
        // Image options
        Image: {
            zoom: false,  // Disable initial zoom
            wheel: true,
            click: "toggleZoom",
            doubleClick: "toggleZoom",
            wheelLimit: 10,
            zoomFriction: 0.88,
            infinite: true,
        },

        Images: {
            Panzoom: {
                maxScale: 5,
                minScale: 1,  // Changed from 0.5 to 1
                initialScale: 1,  // Ensure initial scale is 1
            },
        },

        // Video specific options
        Video: {
            ratio: 16/9,
            autoplay: false,
            click: "play",
            controls: true,
            volume: 1,
        },

        Youtube: {
            ratio: 16/9,
            autoplay: false,
            controls: 1,
            showinfo: 0,
            rel: 0,
            modestbranding: 1,
        },

        Vimeo: {
            ratio: 16/9,
            autoplay: false,
            controls: true,
            transparent: false,
            dnt: true,
        },

        // Slideshow options
        Slideshow: {
            autostart: false,
            interval: 4000,
            repeat: true,
            pauseOnHover: false,  // Added to prevent unexpected pausing
        },

        // Toolbar configuration with slideshow more accessible
        Toolbar: {
            display: {
                left: ["infobar", "prev"],
                middle: [
                    "slideshow",  // Moved slideshow to beginning for better accessibility
                    "zoomIn",
                    "zoomOut",
                    "toggle1to1",
                    "toggleZoom",
                    "rotateCCW",
                    "rotateCW",
                    "flipX",
                    "flipY",
                    "thumbs",  // Added thumbnails toggle
                    "fullscreen",
                ],
                right: ["next", "close"],
            },
            items: {
                thumbs: {
                    tpl: '<button class="f-button" title="Toggle thumbnails" data-fancybox-toggle-thumbs><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path fill="none" d="M0 0h24v24H0z"/><path d="M4 4h4v4H4V4zm6 0h4v4h-4V4zm6 0h4v4h-4V4zM4 10h4v4H4v-4zm6 0h4v4h-4v-4zm6 0h4v4h-4v-4zM4 16h4v4H4v-4zm6 0h4v4h-4v-4zm6 0h4v4h-4v-4z" fill="currentColor"/></svg></button>',
                },
            },
        },

        // Thumbnails
        Thumbs: {
            type: "classic",
            showOnStart: true,
            key: "t",
            minCount: 1,
            autoStart: true,
        },

        // Gesture options
        gesture: {
            enabled: true,
            friction: 0.88,
            minScale: 0.5,
            maxScale: 5,
        },

        // Keyboard shortcuts
        keyboard: {
            Escape: "close",
            Delete: "close",
            Backspace: "close",
            PageUp: "next",
            PageDown: "prev",
            ArrowUp: "prev",
            ArrowDown: "next",
            ArrowRight: "next",
            ArrowLeft: "prev",
            "+"    : "zoomIn",
            "-"    : "zoomOut",
            "0"    : "toggleZoom",
            "1"    : "toggle1to1",
            "f"    : "fullscreen",
            "t"    : "Thumbs.toggle",  // Keyboard shortcut for thumbnails
            "s"    : "Slideshow.toggle",
            "r"    : "rotateCW",
        },

        // Caption settings
        caption: {
            type: "inner",
            position: "bottom",
        },

        // Loading indicator
        showLoading: true,
        spinnerTpl: '<div class="fancybox-spinner"></div>',

        // Error message
        errorTpl: '<div class="fancybox-error"><p>The requested content cannot be loaded. Please try again later.</p></div>',

        // Event handlers
        on: {
            "init": (fancybox) => {
                // Reset any existing Panzoom instance
                if (fancybox.Panzoom) {
                    fancybox.Panzoom.reset();
                }

                const clickedElement = fancybox.options.$trigger;
                if (clickedElement) {
                    const gallery = document.querySelectorAll('[data-fancybox="gallery"]');
                    const index = Array.from(gallery).indexOf(clickedElement);
                    if (index >= 0) {
                        fancybox.jumpTo(index);
                    }
                }
            },
            "reveal": (fancybox, slide) => {
                // Ensure proper initial state for each slide
                if (slide.$content) {
                    slide.$content.style.transform = '';
                    if (slide.Panzoom) {
                        slide.Panzoom.reset();
                    }
                }
            },
            "done": (fancybox, slide) => {
                // Reset zoom when slide is fully loaded
                if (slide.Panzoom) {
                    slide.Panzoom.reset();
                }
            },
            "closing": (fancybox) => {
                // Stop slideshow when closing
                if (fancybox.Slideshow) {
                    fancybox.Slideshow.stop();
                }
            },
            "Thumbs.toggle": (fancybox) => {
                // Optional: Add custom behavior when thumbnails are toggled
                console.log("Thumbnails toggled");
            },
        },

        // Info bar
        infobar: true,

        // Animation settings
        animated: true,
        showClass: "fancybox-zoomIn",
        hideClass: "fancybox-zoomOut",

        // Fullscreen API
        fullscreen: {
            autoStart: false,
        },

        // Hash navigation
        hash: false,

        // Focus handling
        focus: true,

        // Mobile settings
        touch: {
            vertical: true,
            momentum: true,
        },

        // Image protection
        protect: true,
    });

    // Add click handler for thumbnail toggle
    $(document).on('click', '[data-fancybox-toggle-thumbs]', function() {
        const fancybox = Fancybox.getInstance();
        if (fancybox && fancybox.Thumbs) {
            fancybox.Thumbs.toggle();
        }
    });

    // Gallery filtering function
    function filterGallery(category) {
        $('.date-group').each(function() {
            const $group = $(this);
            const $items = $group.find('.gallery-item');
            let hasVisibleItems = false;

            $items.each(function() {
                const $item = $(this);
                const itemCategory = $item.data('category');

                if (category === 'all' || itemCategory === category) {
                    $item.fadeIn(400).removeClass('hidden');
                    hasVisibleItems = true;
                } else {
                    $item.fadeOut(400).addClass('hidden');
                }
            });

            if (hasVisibleItems) {
                $group.fadeIn(400);
            } else {
                $group.fadeOut(400);
            }
        });

        updateCounters();
    }

    function updateCounters() {
        // Count all visible items
        const all = $('.gallery-item:not(.hidden)').length;
        // Count visible items with image category
        const images = $('.gallery-item[data-category="image"]:not(.hidden)').length;
        // Count visible items with video category
        const videos = $('.gallery-item[data-category="video"]:not(.hidden)').length;

        // Update the counter displays
        $('[data-filter="all"] .filter-counter').text(all);
        $('[data-filter="image"] .filter-counter').text(images);
        $('[data-filter="video"] .filter-counter').text(videos);
    }

    // Click handler for filter buttons
    $('.btn-filter').on('click', function(e) {
        e.preventDefault();
        const category = $(this).data('filter');

        // Update active state
        $('.btn-filter').removeClass('active');
        $(this).addClass('active');

        // Apply filter
        filterGallery(category);
    });

    // Initialize counters on page load
    updateCounters();
});
</script>
@endpush  <!-- End of push scripts to the stack -->







































































