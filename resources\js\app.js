// import '@popperjs/core';
import 'bootstrap';
// Import the video plugin
import './tinymce-video-plugin';

// Add this to your window.addEventListener('DOMContentLoaded') handler
window.addEventListener('DOMContentLoaded', function() {
    if (typeof window.tinySettingsCopy !== 'undefined') {
        window.tinySettingsCopy.forEach(function(settings) {
            if (!settings.plugins.includes('videoTemplate')) {
                settings.plugins += ' videoTemplate';
            }
        });
    }
});


