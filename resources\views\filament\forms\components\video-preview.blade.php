<x-dynamic-component
    :component="$getFieldWrapperView()"
    :id="$getId()"
    :label="$getLabel()"
    :label-sr-only="$isLabelHidden()"
    :helper-text="$getHelperText()"
    :hint="$getHint()"
    :hint-icon="$getHintIcon()"
    :required="$isRequired()"
    :state-path="$getStatePath()"
>
    <div x-data="{ state: $wire.entangle('{{ $getStatePath() }}') }">
        @if($getState())
            <div class="flex items-center justify-center space-y-2">
                <video
                    controls
                    class="w-full rounded-lg shadow-sm"
                    style="max-width: 400px;"
                >
                    <source src="{{ Storage::url('gallery/videos/' . $getState()) }}" type="video/mp4">
                    Your browser does not support the video tag.
                </video>
            </div>

            <div class="flex items-center justify-center space-x-2 mt-2">
                @unless($isDisabled())
                    <button
                        type="button"
                        x-on:click="state = null"
                        class="filament-link text-danger-600 hover:text-danger-500"
                    >
                        {{ __('forms::components.file_upload.buttons.remove.label') }}
                    </button>
                @endunless

                <a
                    href="{{ Storage::url('gallery/videos/' . $getState()) }}"
                    target="_blank"
                    class="filament-link text-primary-600 hover:text-primary-500"
                >
                    {{ __('forms::components.file_upload.buttons.download.label') }}
                </a>
            </div>
        @else
            <div class="flex items-center justify-center px-4 py-2 space-x-1 text-sm border border-gray-300 border-dashed rounded-lg">
                <x-filament::button
                    :wire:click="'upload(\'' . $getStatePath() . '\')'"
                    size="sm"
                >
                    {{ __('forms::components.file_upload.buttons.upload.label') }}
                </x-filament::button>
            </div>
        @endif

        <input
            type="file"
            {{ $getExtraInputAttributeBag() }}
            style="display: none;"
        />
    </div>
</x-dynamic-component>

