<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('blog_articles', function (Blueprint $table) {
            $table->id();

            // Basic Information
            $table->string('title_en');
            $table->string('title_ar')->nullable();
            $table->string('slug')->unique();
            $table->longText('content_en');
            $table->longText('content_ar')->nullable();
            $table->text('excerpt_en')->nullable();
            $table->text('excerpt_ar')->nullable();

            // Categories and Tags
            $table->string('category_en')->nullable();
            $table->string('category_ar')->nullable();
            $table->json('tags')->nullable();

            // Media
            $table->string('featured_image')->nullable();
            $table->json('gallery_images')->nullable();
            $table->json('attachments')->nullable();

            // Author Information
            $table->string('author_name_en')->nullable();
            $table->string('author_name_ar')->nullable();
            $table->string('author_title_en')->nullable();
            $table->string('author_title_ar')->nullable();
            $table->string('author_image')->nullable();

            // Medical Specific Fields
            $table->text('medical_disclaimer_en')->nullable();
            $table->text('medical_disclaimer_ar')->nullable();
            $table->json('related_conditions')->nullable();
            $table->json('references')->nullable();

            // SEO
            $table->string('meta_title_en')->nullable();
            $table->string('meta_title_ar')->nullable();
            $table->text('meta_description_en')->nullable();
            $table->text('meta_description_ar')->nullable();
            $table->json('meta_keywords')->nullable();

            // Publishing
            $table->timestamp('published_at')->nullable();
            $table->boolean('is_featured')->default(false);
            $table->boolean('is_active')->default(true);
            $table->integer('display_order')->default(0);
            $table->integer('reading_time')->nullable(); // in minutes
            $table->integer('view_count')->default(0);

            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('blog_articles');
    }
};
