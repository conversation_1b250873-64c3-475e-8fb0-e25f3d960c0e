/* ----------------------------------------------------------------------------------------
* Author        : Awaiken
* Template Name : Carefirst - Physiotherapy HTML Template
* File          : CSS File
* Version       : 1.0
* ---------------------------------------------------------------------------------------- */
/* INDEX
----------------------------------------------------------------------------------------
01. Global Variables
02. General css
03. Header css
04. Hero css
05. Our Benefits css
06. About Us css
07. Our Services css
08. Care Rehabilitation css
09. Quality Treatment css
10. How It Work css
11. Our Pricing css
12. Therapist Team css
13. Our Testimonial css
14. Our Blog css
15. Footer css
16. About us Page css
17. Services Page css
18. Service Single css
19. Blog Archive css
20. Blog Single css
21. Team Page css
22. Team Single css
23. Video Gallery Page css
24.	FAQs Page css
25. Contact us Page css
26. Make Appointment Page css
27. 404 Page css
28. Responsive css
-------------------------------------------------------------------------------------- */

/************************************/
/*** 	 01. Global Variables	  ***/
/************************************/

:root{
	--primary-color			: #023047;
	--secondary-color		: #E5EAEC;
	/* --text-color			: #606778; */
	--text-color			: #0088cc;
	--accent-color			: #033E5B;
	--white-color			: #FFFFFF;
	--black-color 			: #000000;
	--divider-color			: #FFFFFF33;
	--dark-divider-color	: #D4F0F533;
	--error-color			: rgb(230, 87, 87);
	--default-font			: "Sora", sans-serif;
	--accent-font			: "Marcellus", serif;
	--primary-color-rgb		: 2, 48, 71; /* This is for #023047 */
}

/************************************/
/*** 	   02. General css		  ***/
/************************************/

body{
	font-family: var(--default-font);
	font-size: 16px;
	font-weight: 400;
	line-height: 1.5em;
	color: var(--text-color);
	background-color: var(--white-color);
}

p{
	line-height: 1.5em;
	margin-bottom: 1.6em;
}

h1,
h2,
h3,
h4,
h5,
h6{
	margin :0;
	font-family: var(--accent-font);
	font-weight: 400;
	line-height: 1.2em;
	color: var(--primary-color);
}

figure{
	margin: 0;
}

img{
	max-width: 100%;
}

a{
	text-decoration: none;
}

a:hover{
	text-decoration: none;
	outline: 0;
}

a:focus{
	text-decoration: none;
	outline: 0;
}

html,
body{
	width: 100%;
	overflow-x: clip;
}

.container{
	max-width: 1300px;
}

.container,
.container-fluid,
.container-lg,
.container-md,
.container-sm,
.container-xl,
.container-xxl{
    padding-right: 15px;
    padding-left: 15px;
}

.image-anime{
	position: relative;
	overflow: hidden;
}

.image-anime:after{
	content: "";
	position: absolute;
    width: 200%;
    height: 0%;
    left: 50%;
    top: 50%;
    background-color: rgba(255,255,255,.3);
    transform: translate(-50%,-50%) rotate(-45deg);
    z-index: 1;
}

.image-anime:hover:after{
    height: 250%;
    transition: all 600ms linear;
    background-color: transparent;
}

.reveal{
	position: relative;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    visibility: hidden;
    overflow: hidden;
}

.reveal img{
    height: 100%;
    width: 100%;
    -o-object-fit: cover;
    object-fit: cover;
    -webkit-transform-origin: left;
    transform-origin: left;
}

.row{
    margin-right: -15px;
    margin-left: -15px;
}

.row > *{
	padding-right: 15px;
	padding-left: 15px;
}

.row.no-gutters{
    margin-right: 0px;
    margin-left: 0px;
}

.row.no-gutters > *{
    padding-right: 0px;
    padding-left: 0px;
}

.btn-default{
	position: relative;
	display: inline-flex;
	line-height: 1.2em;
	background: var(--secondary-color);
	text-transform: capitalize;
	border-radius: 100px;
	padding: 2px 25px 2px 2px;
	border: none;
	overflow: hidden;
	transition: all 0.5s ease-in-out;
}

.btn-default:hover{
	background-color: transparent;
}

.btn-default::before{
	content: '';
	position: absolute;
	top: 50%;
	right: 0;
	bottom: 0;
	width: 10px;
	height: 10px;
	background-image: url('../images/arrow-blue.svg');
	background-repeat: no-repeat;
	background-position: center center;
	background-size: auto;
	transform: translate(-10px, -50%);
	transition: all 0.4s ease-in-out;
	z-index: 1;
}

.btn-default:hover:before{
	filter: brightness(0) invert(1);
	transform: translate(-10px, -50%);
}

.btn-default::after{
	content: '';
    display: block;
    position: absolute;
	top: 0;
    left: 0;
    bottom: 0;
	width: 0;
	height: 100%;
	border-radius: 100px;
    background: var(--primary-color);
    transition: all 0.4s ease-in-out;
	z-index: 0;
}

.btn-default:hover::after{
	width: 100%;
}

.btn-default span{
	position: relative;
	display: inline-block;
	font-size: 18px;
	font-weight: 500;
	line-height: 1.2em;
	background-color: var(--primary-color);
	color: var(--secondary-color);
	border-radius: 100px;
	padding: 15px 20px;
	overflow: hidden;
	z-index: 1;
	transition: all 0.5s ease-in-out;
	z-index: 1;
}

.btn-default:hover span{
	background-color: transparent;
	color: var(--primary-color);
}

.btn-default span::after{
	content: '';
    display: block;
    position: absolute;
	top: 0;
    left: 0;
    bottom: 0;
	width: 0;
	height: 100%;
	border-radius: 100px;
    background: var(--secondary-color);
    transition: all 0.4s ease-in-out;
	z-index: -1;
}

.btn-default:hover span::after{
	width: 100%;
}

.btn-default.btn-highlighted{
	background-color: var(--primary-color);
}

.btn-default.btn-highlighted:hover:after{
	background-color: var(--secondary-color);
}

.btn-default.btn-highlighted::before{
	background-image: url('../images/arrow-white.svg');
}

.btn-default.btn-highlighted:hover:before{
	filter: brightness(0.1) invert(0);
}

.btn-default.btn-highlighted span{
	background-color: var(--secondary-color);
	color: var(--primary-color);
}

.btn-default.btn-highlighted:hover span{
	background-color: transparent;
	color: var(--secondary-color);
}

.btn-default.btn-highlighted span::after{
	background-color: var(--primary-color);
}

.portal-btn {
    position: relative;
    display: inline-flex;
    align-items: center;
    background: var(--primary-color);
    border-radius: 100px;
    padding: 2px 40px 2px 2px;  /* Increased right padding */
    border: none;
    overflow: hidden;
    transition: all 0.4s ease-in-out;
}

.portal-btn:hover {
    background: var(--secondary-color);
}

.portal-btn::before {
    content: '\f101';  /* Unicode for double arrow right (fa-angle-double-right) */
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    position: absolute;
    top: 50%;
    right: 15px;  /* Fixed position from right */
    font-size: 20px;
    color: var(--secondary-color);
    transform: translateY(-50%);  /* Only transform Y axis */
    transition: all 0.3s ease-in-out;
    z-index: 2;
}

.portal-btn:hover::before {
    color: var(--primary-color);
}

.portal-btn span {
    position: relative;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 600;
    color: var(--primary-color);
    background: var(--secondary-color);
    border-radius: 100px;
    padding: 12px 20px;
    z-index: 1;
    transition: all 0.3s ease-in-out;
}

.portal-btn:hover span {
    background: var(--primary-color);
    color: var(--secondary-color);
}

.portal-btn::after {
    content: '';
    position: absolute;
    inset: 0;
    width: 0;
    height: 100%;
    background: var(--secondary-color);
    border-radius: 100px;
    transition: all 0.4s ease-in-out;
    z-index: 0;
}

.portal-btn:hover::after {
    width: 100%;
}

#magic-cursor{
	position: absolute;
    width: 10px !important;
    height: 10px !important;
    pointer-events: none;
    z-index: 1000000;
}

#ball{
	position: fixed;
	display: block;
	left: 0;
	top: 0;
	transform: translate(-50%, -50%);
	width: 8px !important;
	height: 8px !important;
	background: var(--accent-color);
	margin: 0;
	border-radius: 50%;
	pointer-events: none;
	opacity:1 !important;
}

.preloader{
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 1000;
	background-color: var(--accent-color);
	display: flex;
	align-items: center;
	justify-content: center;
}

.loading-container,
.loading{
	height: 100px;
	position: relative;
	width: 100px;
	border-radius: 100%;
}

.loading-container{
	margin: 40px auto;
}

.loading{
	border: 1px solid transparent;
	border-color: transparent var(--white-color) transparent var(--white-color);
	animation: rotate-loading 1.5s linear 0s infinite normal;
	transform-origin: 50% 50%;
}

.loading-container:hover .loading,
.loading-container .loading{
	transition: all 0.5s ease-in-out;
}

#loading-icon{
	position: absolute;
	top: 50%;
	left: 50%;
	max-width: 66px;
	transform: translate(-50%, -50%);
}

@keyframes rotate-loading{
	0%{
		transform: rotate(0deg);
	}

	100%{
		transform: rotate(360deg);
	}
}

.bg-radius-section{
	position: relative;
	border-radius: 50px 50px 0 0;
	margin-top: -50px;
}

.section-row{
	margin-bottom: 60px;
}

.section-row .section-title{
	margin-bottom: 0;
}

.section-btn{
	text-align: end;
}

.section-title{
	margin-bottom: 40px;
}

.section-title h3{
	display: inline-block;
	position: relative;
	font-family: var(--default-font);
	font-size: 14px;
    font-weight: 400;
    text-transform: capitalize;
    color: var(--text-color);
	padding-left: 10px;
    margin-bottom: 10px;
}

.section-title h3::before{
	content: '/';
	position: absolute;
	top: 0;
	left: 0;
	color: var(--primary-color);
	width: 6px;
	height: 15px;
}

.section-title h1{
	font-size: 66px;
	margin-bottom: 0;
}

.section-title h2{
	font-size: 54px;
	margin-bottom: 0;
}

.section-title p{
	margin-top: 20px;
	margin-bottom: 0;
}

.help-block.with-errors ul{
	margin: 0;
	text-align: left;
}

.help-block.with-errors ul li{
	color: var(--error-color);
	font-weight: 500;
	font-size: 14px;
}

/************************************/
/**** 	   03. Header css		 ****/
/************************************/

.topbar{
	padding: 15px 0 65px;
	background-color: var(--primary-color);
}

.topbar-contact-info{
	text-align: left;
}

.topbar-contact-info ul{
	display: flex;
	flex-wrap: wrap;
	list-style: none;
	line-height: 1em;
	padding: 0;
	margin: 0;
	gap: 30px;
}

.topbar-contact-info ul li a{
	color: var(--white-color);
	display: flex;
	align-items: center;
}

.topbar-contact-info ul li a img{
	max-width: 20px;
	margin-right: 10px;
}

.topbar-social-links{
	text-align: right;
}

.topbar-social-links ul{
	list-style: none;
	line-height: 1em;
	padding: 0;
	margin: 0;
}

.topbar-social-links ul li{
	display: inline-block;
	margin-right: 20px;
	transition: all 0.3s ease-in-out;
}

.topbar-social-links ul li:last-child{
	margin-right: 0;
}

.topbar-social-links ul li a{
	color: var(--white-color);
	transition: all 0.3s ease-in-out;
}

.topbar-social-links ul li a:hover{
	color: var(--text-color);
}

.topbar-social-links ul li a i{
	font-size: 20px;
	color: inherit
}

header.main-header{
	background-color: transparent;
	border-bottom: 1px solid var(--divider-color);
	position: relative;
	z-index: 100;
	margin-top: -50px;
}

header.main-header .header-sticky{
	position: relative;
	top: 0;
	z-index: 100;
}

header.main-header .header-sticky.hide{
	transform: translateY(-100%);
	transition: transform 0.3s ease-in-out;
	border-radius: 0;
}

header.main-header .header-sticky.active{
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	border-radius: 0;
    transform: translateY(0);
	/* background: transparent; */
	background: var(--accent-color);
	border-bottom: 1px solid var(--divider-color);
	backdrop-filter: blur(30px);
	-webkit-backdrop-filter: blur(30px);
	transition: transform 0.5s ease-in-out, opacity 0.5s ease-in-out, background 0.5s ease-in-out;
}

.navbar{
	padding: 20px 0;
	align-items: center;
}

.navbar-brand{
	padding: 0;
	margin: 0;
}

.main-menu .nav-menu-wrapper{
	flex: 1;
	text-align: center;
}

.main-menu .nav-menu-wrapper > ul{
	align-items: center;
	display: inline-flex;
}

.main-menu ul li{
	margin: 0 9px;
	position: relative;
}

.main-menu ul li a{
	font-size: 16px;
	font-weight: 500;
	padding: 14px 15px !important;
	color: var(--white-color);
	text-transform: capitalize;
	transition: all 0.3s ease-in-out;
}

.main-menu ul li.submenu > a:after{
	content: '\f107';
	font-family: 'FontAwesome';
	font-weight: 900;
	font-size: 14px;
	margin-left: 8px;
}

.main-menu ul li a:hover,
.main-menu ul li a:focus{
	color: var(--text-color);
}

.main-menu ul ul{
	visibility: hidden;
	opacity: 0;
	transform: scaleY(0.8);
	transform-origin: top;
	padding: 0;
	margin: 0;
	list-style: none;
	width: 220px;
	border-radius: 20px;
	position: absolute;
	left: 0;
	top: 100%;
	overflow: hidden;
	background-color: var(--primary-color);
	transition: all 0.3s ease-in-out;
	text-align: left;
}

.main-menu ul li.submenu:first-child ul{
    width: 220px;
}

.main-menu ul ul ul{
	left: 100%;
	top: 0;
	text-align: left;
}

.main-menu ul ul li{
	margin: 0;
	padding: 0;
}

.main-menu ul ul li a{
	color: var(--white-color);
	padding: 8px 20px !important;
	transition: all 0.3s ease-in-out;
}

.main-menu ul li:hover > ul{
	visibility: visible;
	opacity: 1;
	transform: scaleY(1);
    padding: 5px 0;
}

.main-menu ul ul li a:hover,
.main-menu ul ul li a:focus{
	color: var(--text-color);
	background-color: transparent;
	padding: 8px 20px 8px 23px !important;
}

.header-btn .btn-default{
	padding: 2px 30px 2px 2px;
}

.header-btn .btn-default::before{
	background-image: url('../images/-icon-phone-header-btn.svg');
	width: 15px;
	height: 15px;
}

.responsive-menu,
.navbar-toggle{
	display: none;
}

.responsive-menu{
	top: 0;
	position: relative;
}

.slicknav_btn{
	background: var(--primary-color);
	padding: 0;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 38px;
	height: 38px;
	margin: 0;
	border-radius: 8px;
}

.slicknav_icon .slicknav_icon-bar{
	display: block;
	width: 100%;
	height: 3px;
	width: 22px;
	background-color: var(--white-color);
	border-radius: 6px;
	margin: 4px auto !important;
	transition: all 0.1s ease-in-out;
}

.slicknav_icon .slicknav_icon-bar:first-child{
	margin-top: 0 !important;
}

.slicknav_icon .slicknav_icon-bar:last-child{
	margin-bottom: 0 !important;
}

.navbar-toggle a.slicknav_btn.slicknav_open .slicknav_icon span.slicknav_icon-bar:nth-child(1){
    transform: rotate(-45deg) translate(-5px, 5px);
}

.navbar-toggle a.slicknav_btn.slicknav_open .slicknav_icon span.slicknav_icon-bar:nth-child(2){
    opacity: 0;
}

.navbar-toggle a.slicknav_btn.slicknav_open .slicknav_icon span.slicknav_icon-bar:nth-child(3){
    transform: rotate(45deg) translate(-5px, -5px);
}

.slicknav_menu{
	position: absolute;
    width: 100%;
	padding: 0;
	background: var(--primary-color);
}

.slicknav_menu ul{
	margin: 5px 0;
}

.slicknav_menu ul ul{
	margin: 0;
}

.slicknav_nav .slicknav_row,
.slicknav_nav li a{
	position: relative;
	font-size: 16px;
	font-weight: 500;
	text-transform: capitalize;
	padding: 10px 20px;
	color: var(--white-color);
	line-height: normal;
	margin: 0;
	border-radius: 0 !important;
	transition: all 0.3s ease-in-out;
}

.slicknav_nav a:hover,
.slicknav_nav a:focus,
.slicknav_nav .slicknav_row:hover{
	background-color: transparent;
	color: var(--text-color);
}

.slicknav_menu ul ul li a{
    padding: 10px 20px 10px 30px;
}

.slicknav_arrow{
	font-size: 0 !important;
}

.slicknav_arrow:after{
	content: '\f107';
	font-family: 'FontAwesome';
	font-weight: 900;
	font-size: 12px;
	margin-left: 8px;
	color: var(--white-color);
	position: absolute;
	right: 15px;
    top: 15px;
	transition: all 0.3s ease-out;
}

.slicknav_open > a .slicknav_arrow:after{
    transform: rotate(-180deg);
	color: var(--text-color);
}

/************************************/
/***        04. Hero css	      ***/
/************************************/

.hero{
	background: var(--primary-color) url('../images/hero-bg.jpg') no-repeat;
	background-position: center center;
	background-size: cover;
	overflow: hidden;
	padding: 240px 0 210px;
	margin-top: -100px;
}

.hero::before{
	content: '';
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	background-color: var(--black-color);
	opacity: 50%;
	width: 100%;
	height: 100%;
}

.hero.hero-video{
	overflow: hidden;
}

.hero.hero-video .hero-bg-video{
	position: absolute;
	top: 0;
	right: 0;
	left: 0;
	bottom: 0;
	width: 100%;
	height: 100%;
}

.hero.hero-video .hero-bg-video::before{
	content: '';
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	background-color: var(--black-color);
	opacity: 50%;
	width: 100%;
	height: 100%;
}

.hero.hero-video .hero-bg-video video{
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.hero.hero-slider-layout{
	background: none;
	padding: 0;
	z-index: 0;
}

.hero.hero-slider-layout .hero-slide{
	position: relative;
    padding: 240px 0 210px;
}

.hero.hero-slider-layout .hero-slide::before{
	content: '';
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	background-color: var(--black-color);
	opacity: 50%;
	width: 100%;
	height: 100%;
	z-index: 1;
}

.hero.hero-slider-layout .hero-slide .hero-slider-image{
	position: absolute;
	top: 0;
	right: 0;
	left: 0;
	bottom: 0;
}

.hero.hero-slider-layout .hero-slide .hero-slider-image img{
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.hero.hero-slider-layout .hero-pagination{
	position: absolute;
    bottom: 120px;
	text-align: center;
	z-index: 1;
}

.hero.hero-slider-layout .hero-pagination .swiper-pagination-bullet{
    width: 12px;
    height: 12px;
    background: var(--white-color);
    opacity: 1;
    transition: all 0.3s ease-in-out;
    margin: 0 5px;
}

.hero.hero-slider-layout .hero-pagination .swiper-pagination-bullet-active{
    background-color: var(--accent-color);
}

.hero-content{
	position: relative;
	width: 100%;
	max-width: 610px;
	z-index: 1;
}

.hero-content .section-title h1{
	color: var(--white-color);
}

.hero-content .section-title p{
	font-size: 18px;
	color: var(--white-color);
}

.hero-content-body .btn-default.btn-highlighted{
	margin-left: 30px;
}

/************************************/
/***      05. Our Benefits css	  ***/
/************************************/

.our-benefits{
	background-color: var(--primary-color);
	padding: 50px 0 100px;
}

.benefits-item{
	display: flex;
	align-items: start;
	border-right: 1px solid var(--divider-color);
	padding-right: 15px;
}

.our-benefits .col-lg-4:last-child .benefits-item{
	border: none;
	padding-right: 0;
}

.benefits-item .icon-box{
	position: relative;
	margin-right: 20px;
}

.benefits-item .icon-box::before{
	content: '';
	position: absolute;
	bottom: -10px;
	right: -10px;
	background-color: var(--accent-color);
	border-radius: 50%;
	width: 30px;
	height: 30px;
}

.benefits-item .icon-box img{
	position: relative;
	width: 35px;
	z-index: 1;
}

.benefits-content{
	width: calc(100% - 55px);
}

.benefits-content h3{
	font-size: 22px;
	text-transform: capitalize;
	color: var(--secondary-color);
	margin-bottom: 5px;
}

.benefits-content p{
	color: var(--white-color);
	margin: 0;
}

/************************************/
/***       06. About Us css 	  ***/
/************************************/

.about-us{
	background-color: var(--white-color);
	padding: 100px 0;
}

.about-content{
	position: relative;
	z-index: 1;
}

.about-content-body{
	margin-bottom: 40px;
}

.about-list-item{
	display: flex;
	align-items: center;
	margin-bottom: 20px;
}

.about-list-item:last-child{
	margin-bottom: 0;
}

.about-list-item .icon-box{
	position: relative;
	margin-right: 20px;
}

.about-list-item .icon-box::before{
	content: '';
	position: absolute;
	bottom: -5px;
	right: -5px;
	background-color: var(--secondary-color);
	border-radius: 50%;
	width: 30px;
	height: 30px;
}

.about-list-item .icon-box img{
	position: relative;
	max-width: 40px;
	z-index: 1;
}

.about-list-content{
	width: calc(100% - 60px);
}

.about-list-content p{
	font-weight: 600;
	color: var(--primary-color);
	text-transform: capitalize;
	margin: 0;
}

.about-trusted-customer{
	background-color: var(--white-color);
	box-shadow: 0px 0px 30px 0px #0000001A;
	border-radius: 10px;
	padding: 25px;
}

.trusted-customer-image{
	margin-bottom: 15px;
}

.trusted-customer-image img{
	max-width: 160px;
}

.trusted-customer-rating{
	margin-bottom: 5px;
}

.trusted-customer-rating ul{
	list-style: none;
	padding: 0;
	margin: 0;
}

.trusted-customer-rating ul li i{
	font-size: 14px;
	color: #FF7D05;
}

.trusted-customer-content p{
	text-transform: capitalize;
	margin: 0;
}

.about-us-images{
	position: relative;
	display: flex;
	flex-wrap: wrap;
	gap: 30px;
	z-index: 1;
}

.about-image{
	width: calc(50% - 15px);
}

.about-image figure{
	box-shadow: 0px 0px 30px 0px #00000033;
}

.about-image.img-box-1 figure,
.about-image.img-box-1 img{
	border-radius: 500px 0 0 0;
}

.about-image.img-box-2 figure,
.about-image.img-box-2 img{
	border-radius: 0 500px 0 0;
}

.about-image.img-box-3 figure,
.about-image.img-box-3 img{
	border-radius: 0 0 0 500px;
}

.about-image.img-box-4 figure,
.about-image.img-box-4 img{
	border-radius: 0 0 500px 0;
}

.about-circle-logo{
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	background-color: var(--white-color);
	box-shadow: 0px 0px 30px 0px #0000004D;
	border-radius: 50%;
	width: 210px;
	height: 210px;
	display: flex;
	align-items: center;
	justify-content: center;
	overflow: hidden;
    transition: all 0.6s ease-in-out;
	z-index: 1;
}

.about-us-images:hover .about-circle-logo{
	background-color: transparent;
}

.about-circle-logo::before{
    content: '';
    display: block;
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: var(--primary-color);
    top: 0;
    left: 0;
    transform: scale(0);
    transition: all 0.5s ease-in-out;
    border-radius: 50%;
}

.about-us-images:hover .about-circle-logo::before{
	transform: scale(1.1);
}

.about-us-images .about-circle-logo img{
	max-width: 200px;
	transition: all 0.4s ease-in-out;
}
@media only screen and (max-width: 767px) {
    .about-us-images .about-circle-logo img{
        max-width: 118px !important;
    }
}

.about-us-images:hover .about-circle-logo img{
	filter: brightness(0) invert(1);
}

/************************************/
/***     07. Our Services css 	  ***/
/************************************/

.our-services{
	position: relative;
	background: var(--secondary-color);
	padding: 100px 0;
}

.our-services::before{
	content: '';
	position: absolute;
	top: -380px;
	left: 0;
	right: 0;
	background: url('../images/service-bg-shape.svg') no-repeat;
	background-position: top center;
	background-size: cover;
	width: 100%;
	height: 50%;
	z-index: 0;
}

.our-services::after{
	content: '';
	position: absolute;
	top: 40px;
	right: 0;
	background: url('../images/service-bg-cricle.svg') no-repeat;
	background-position: top right;
	background-size: auto;
	width: 100%;
	height: 100%;
	z-index: 0;
}

.our-services .section-row{
	position: relative;
	z-index: 1;
}

.service-item{
	position: relative;
	height: calc(100% - 30px);
	margin-bottom: 30px;
	z-index: 1;
}

.service-item-image{
	position: relative;
	box-shadow: 0px 0px 30px 0px #00000026;
	border-radius: 10px;
	overflow: hidden;
}

.service-item-image::before{
	content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: var(--black-color);
    opacity: 50%;
    width: 100%;
    height: 100%;
	z-index: 1;
}

.service-item-image img{
	aspect-ratio: 1 / 0.98;
	object-fit: cover;
	border-radius: 10px;
	transition: all 0.4s ease-in-out;
}

.service-item:hover .service-item-image img{
	transform: scale(1.1);
}

.service-item .icon-box{
	position: absolute;
	top: 0;
	left: 30px;
	border-radius: 0 0 10px 10px;
	backdrop-filter: blur(10px);
	-webkit-backdrop-filter: blur(10px);
	width: 80px;
	height: 80px;
	display: flex;
	align-items: center;
	justify-content: center;
	overflow: hidden;
	transition: all 0.5s ease-in-out;
	z-index: 2;
}

.service-item .icon-box::before{
	content: '';
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	background: var(--white-color);
	opacity: 20%;
	width: 100%;
	height: 100%;
}

.service-item .icon-box::after{
    content: '';
    display: block;
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: var(--primary-color);
    top: 0;
    left: 0;
    transform: scale(0);
    transition: all 0.4s ease-in-out;
    border-radius: 0 0 10px 10px;
}

.service-item:hover .icon-box::after{
	transform: scale(1.1);
}

.service-item .icon-box img{
	position: relative;
	max-width: 56px;
	transition: all 0.4s ease-in-out;
	z-index: 1;
}

.service-item:hover .icon-box img{
	filter: brightness(0) invert(1);
}

.service-body{
	position: absolute;
	bottom: 30px;
	left: 30px;
	right: 10px;
	z-index: 2;
}

.service-content{
	margin-bottom: 20px;
}

.service-content h3{
	font-size: 22px;
	text-transform: capitalize;
	color: var(--white-color);
	margin-bottom: 20px;
}

.service-content p{
	color: var(--white-color);
	margin: 0;
}

.service-btn a{
	position: relative;
	border-radius: 50%;
	width: 36px;
	height: 36px;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease-in-out;
	overflow: hidden;
}

.service-btn a::before{
	content: '';
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	background: var(--secondary-color);
	opacity: 30%;
	width: 100%;
	height: 100%;
	transition: all 0.3s ease-in-out;
}

.service-btn a:hover:after{
	content: '';
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	background: var(--primary-color);
	width: 100%;
	height: 100%;
}

.service-btn a img{
	position: relative;
	max-width: 16px;
	z-index: 1;
}

.more-service-btn{
	position: relative;
	text-align: center;
	margin-top: 20px;
	z-index: 1;
}

.more-service-btn a{
	font-family: var(--accent-font);
	font-size: 24px;
	color: var(--white-color);
}

.more-service-btn a img{
	max-width: 24px;
	margin-left: 20px;
}

.bg-section{
	position: relative;
	background-color: var(--primary-color);
	padding: 150px 0;
	border-radius: 50px 50px 0 0;
	margin-top: -300px;
}

/*************************************/
/***  08. Care Rehabilitation css  ***/
/*************************************/

.care-rehabilitation{
	background-color: var(--white-color);
	padding: 100px 0 120px;
}

.rehab-benefits-item{
	background-color: var(--primary-color);
	border-radius: 10px;
	text-align: center;
	height: calc(100% - 30px);
	margin-bottom: 30px;
	padding: 40px 30px;
}

.rehab-benefits-item .icon-box{
	position: relative;
	width: 50px;
	height: 50px;
	margin: 0 auto;
	margin-bottom: 20px;
}

.rehab-benefits-item .icon-box::before{
    content: '';
    position: absolute;
    bottom: -5px;
    right: -5px;
    background-color: var(--accent-color);
    border-radius: 50%;
    width: 36px;
    height: 36px;
}

.rehab-benefits-item .icon-box img{
	position: relative;
	max-width: 50px;
	z-index: 1;
}

.rehab-benefits-content h3{
	font-size: 18px;
	text-transform: capitalize;
	color: var(--white-color);
}

/************************************/
/***   09. Quality Treatment css  ***/
/************************************/

.our-quality{
	background-color: var(--secondary-color);
}

.quality-treatment {
	/* background-color: red;  */
    /* Temporary test color */
	background: linear-gradient(90deg, var(--secondary-color) 50%, transparent 50%), url('../images/quality-treatment-img.jpg');
	background-repeat: no-repeat;
	background-position: right center;
	background-size: auto;
	padding: 100px 0 150px;
	overflow: hidden;
}

.quality-treatment::before{
	content: '';
	position: absolute;
	bottom: 50px;
	right: 0;
	background: url('../images/quality-treatment-img-shape.svg') no-repeat;
	background-position: bottom right;
	background-size: auto;
	width: 100%;
	height: 100%;
}

.quality-treatment::after{
	content: '';
	position: absolute;
	bottom: 0;
	right: 50%;
	transform: translateX(99%);
	background: var(--secondary-color);
	border-radius: 0 0 200px 0;
	width: 200px;
	height: 100%;
}

.quality-treatment-content{
	position: relative;
	width: 100%;
	max-width: 600px;
	z-index: 1;
}

.quality-treatment-body{
	margin-bottom: 40px;
}

.quality-treatment-body ul{
	list-style: none;
	padding: 0;
	margin: 0;
}

.quality-treatment-body ul li{
	position: relative;
    color: var(--primary-color);
    text-transform: capitalize;
    padding-left: 30px;
	margin-bottom: 15px;
}

.quality-treatment-body ul li:last-child{
	margin-bottom: 0;
}

.quality-treatment-body ul li:before{
    content: '\f058';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    font-size: 18px;
    color: var(--primary-color);
    display: inline-block;
    line-height: normal;
    position: absolute;
    top: 2px;
    left: 0;
}

.quality-treatment-footer .btn-default{
	background-color: var(--white-color);
}

.quality-treatment-footer .btn-default span::after{
	background-color: var(--white-color);
}

/************************************/
/***      10. How It Work css     ***/
/************************************/

.how-it-work{
	background: var(--white-color) url('../images/how-work-bg.svg') no-repeat;
	background-position: left top;
	background-size: auto;
	padding: 100px 0 150px;
}

.how-work-images{
	background: url('../images/how-work-image-bg-shape.svg') no-repeat;
	background-position: right -30px bottom;
	background-size: auto;
	padding: 0 40px 40px 0;
}

.how-work-image-box-1{
	border-radius: 10px 0 0 10px;
	overflow: hidden;
}

.how-work-image-box-2{
	border-radius: 0 10px 10px 0;
	overflow: hidden;
}

.how-work-img-4,
.how-work-img-3,
.how-work-img-2,
.how-work-img-1{
	width: 100%;
}

.how-work-img-4 figure,
.how-work-img-3 figure,
.how-work-img-2 figure,
.how-work-img-1 figure{
	display: block;
}

.how-work-img-4 img,
.how-work-img-3 img,
.how-work-img-2 img,
.how-work-img-1 img{
	width: 100%;
	object-fit: cover;
}

.how-work-img-1 img{
	aspect-ratio: 1 / 1.1;
}

.how-work-img-2 img{
	aspect-ratio: 1 / 1.12;
}

.how-work-img-3 img{
	aspect-ratio: 1 / 2.28;
}

.how-work-img-4 img{
	aspect-ratio: 1 / 0.57;
}

.how-work-accordion .accordion-item{
	margin-bottom: 25px;
	border-radius: 10px;
	overflow: hidden;
}

.how-work-accordion .accordion-item:last-child{
	margin-bottom: 0;
}

.how-work-accordion .accordion-header{
	position: relative;
}

.how-work-accordion .accordion-item .icon-box{
	position: absolute;
	left: 30px;
	top: 50%;
	transform: translateY(-50%);
	background-color: var(--secondary-color);
	border-radius: 50%;
	width: 60px;
	height: 60px;
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1;
}

.how-work-accordion .accordion-item .accordion-header .icon-box img{
	max-width: 36px;
}

.how-work-accordion .accordion-header .accordion-button{
	position: relative;
	font-family: var(--accent-font);
	font-size: 22px;
	line-height: 1.2em;
	background-color: transparent;
	color: var(--primary-color);
	padding: 15px 30px 15px 110px;
	transition: all 0.3s ease-in-out;
	z-index: 0;
}

.how-work-accordion .accordion-button:not(.collapsed){
	padding: 30px 30px 30px 110px;
   background-color: var(--primary-color);
   color: var(--white-color);
}

.how-work-accordion .accordion-header .accordion-button.collapsed{
	background-color: var(--secondary-color);
	color: var(--primary-color);
}

.how-work-accordion .accordion-item .accordion-button::after,
.how-work-accordion .accordion-item .accordion-button.collapsed::after{
	content: '\f077';
	font-family: "Font Awesome 6 Free";
	position: absolute;
	right: 0;
	top: 50%;
	bottom: auto;
	transform: translate(-30px, -50%);
	display: flex;
	align-items: center;
	justify-content: center;
	font-weight: 900;
	font-size: 20px;
	width: 20px;
	height: 20px;
	padding: 5px;
	color: var(--white-color);
	transition: all 0.3s ease-in-out;
}

.how-work-accordion .accordion-item .accordion-button.collapsed::after{
	transform: translate(-30px, -50%) rotate(-180deg);
	color: var(--primary-color);
}

.how-work-accordion .accordion-item .accordion-body{
	background-color: var(--primary-color);
	padding: 0px 30px 20px 30px;
}

.how-work-accordion .accordion-item .accordion-body p{
    color: var(--white-color);
	margin: 0;
}

/************************************/
/***    11. Our Pricing css    ***/
/************************************/

.our-pricing{
	background: url('../images/pricing-bg.jpg') no-repeat;
	background-position: center center;
	background-size: cover;
	padding: 130px 0 120px;
	overflow: hidden;
}

.our-pricing::before{
	content: '';
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	background: linear-gradient(252.64deg, rgba(2, 48, 71, 0) 50.13%, rgba(0, 0, 0, 0.9) 77.06%, #021C29 96.7%);
	width: 100%;
	height: 100%;
}

.our-pricing-content{
	position: relative;
	z-index: 1;
}

.our-pricing-content .section-title p,
.our-pricing-content .section-title h3,
.our-pricing-content .section-title h2{
	color: var(--white-color);
}

.our-pricing-content .section-btn{
	text-align: left;
}

.pricing-item{
	position: relative;
	border-radius: 10px;
	backdrop-filter: blur(10px);
	-webkit-backdrop-filter: blur(10px);
	height: calc(100% - 30px);
	margin-bottom: 30px;
	padding: 30px;
}

.pricing-item::before{
	content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: var(--white-color);
    opacity: 20%;
	border-radius: 10px;
    width: 100%;
    height: 100%;
}

.pricing-item.highlighted-box::after{
	content: 'Recommended';
	position: absolute;
	top: 0;
	left: 30px;
	transform: translateY(-100%);
	background-color: var(--white-color);
	color: var(--primary-color);
	font-size: 10px;
	line-height: normal;
	border-radius: 5px 5px 0 0;
	padding: 8px;
	z-index: 1;
}

.pricing-header{
	position: relative;
	display: flex;
	align-items: center;
	justify-content: space-between;
	border-bottom: 1px solid var(--white-color);
	padding-bottom: 20px;
	margin-bottom: 20px;
	z-index: 1;
}

.pricing-title{
	margin-right: 10px;
}

.pricing-title h3{
	font-size: 26px;
	text-transform: capitalize;
	color: var(--white-color);
}

.pricing-tag h2{
	font-family: var(--default-font);
	font-size: 20px;
	font-weight: 600;
	color: var(--white-color);
}

.pricing-tag h2 sub{
	font-size: 14px;
	font-weight: 300;
	bottom: 0;
}

.pricing-tag p{
	font-size: 10px;
	color: var(--white-color);
	margin: 0;
}

.pricing-body{
	position: relative;
	z-index: 1;
}

.pricing-content{
	margin-bottom: 20px;
}

.pricing-content p{
	color: var(--white-color);
	margin: 0;
}

.pricing-list{
	margin-bottom: 20px;
}

.pricing-list ul{
	list-style: none;
	padding: 0;
	margin: 0;
}

.pricing-list ul li{
	position: relative;
	color: var(--white-color);
    margin-bottom: 15px;
    padding-left: 25px;
}

.pricing-list ul li:last-child{
	margin-bottom: 0;
}

.pricing-list ul li::before{
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
	transform: translateY(-50%);
    background: url(../images/icon-pricing-list.svg) no-repeat;
    background-position: left center;
    background-size: cover;
    width: 16px;
    height: 16px;
}

.pricing-btn{
	padding: 8px 0;
}

.pricing-btn a{
	position: relative;
	font-family: var(--accent-font);
	font-size: 20px;
	font-weight: 400;
	text-transform: capitalize;
	color: var(--white-color);
	padding-right: 50px;
	transition: all 0.3s ease-in-out;
}

.pricing-btn a:hover{
	color: var(--primary-color);
}

.pricing-btn a::before{
	content: '';
	position: absolute;
	top: 50%;
	right: 0;
	transform: translateY(-50%);
	background-color: var(--primary-color);
	background-image: url('../images/arrow-readmore-btn.svg');
	background-repeat: no-repeat;
	background-position: center center;
	background-size: 16px auto;
	border-radius: 10px;
	width: 40px;
	height: 40px;
}

/************************************/
/***    12. Therapist Team css    ***/
/************************************/

.therapist-team{
	background-color: var(--white-color);
	padding: 100px 0 120px;
}

.team-member-item{
	height: calc(100% - 30px);
	margin-bottom: 30px;
}

.team-image{
    position: relative;
    overflow: hidden;
	border-radius: 10px;
	margin-bottom: 20px;
}

.team-image img{
	width: 100%;
	aspect-ratio: 1/1.2;
	object-fit: cover;
	transition: all 0.5s ease-in-out;
}

.team-member-item:hover .team-image img{
    transform: scale(1.1);
}

.team-social-icon{
	position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
	margin: 0 auto;
	transform: translateY(100%);
	text-align: center;
	z-index: 1;
    transition: all 0.5s ease-in-out;
}

.team-member-item:hover .team-social-icon{
	bottom: 20px;
	transform: translateY(0);
}

.team-social-icon ul{
	display: inline-block;
	list-style: none;
	line-height: normal;
	margin: 0;
	padding: 15px 30px;
	background: var(--white-color);
	border-radius: 20px;
}

.team-social-icon ul li{
	display: inline-block;
    text-align: center;
    margin-right: 20px;
}

.team-social-icon ul li:last-child{
	margin-right: 0;
}

.team-social-icon ul li a{
    display: block;
}

.team-social-icon ul li a i{
    color: var(--primary-color);
    font-size: 22px;
	transition: all 0.3s ease-in-out;
}

.team-social-icon ul li a:hover i{
	color: var(--text-color);
}

.team-content{
	text-align: center;
}

.team-content h3{
	font-size: 22px;
	text-transform: capitalize;
	margin-bottom: 5px;
}

.team-content p{
	text-transform: capitalize;
	margin: 0;
}

/* View Profile Button Styles */
.view-profile-btn {
    position: absolute;
    top: 50%; /* Changed from 20px to 50% */
    left: 50%;
    transform: translate(-50%, -50%); /* Changed to center both vertically and horizontally */
    background: var(--secondary-color);
    color: var(--primary-color);
    padding: 12px 25px;
    border-radius: 100px;
    font-size: 14px;
    font-weight: 600;
    text-transform: capitalize;
    letter-spacing: 0.5px;
    opacity: 0;
    visibility: hidden;
    transition: all 0.4s ease-in-out;
    z-index: 3;
    white-space: nowrap;
    box-shadow: 0 4px 15px rgba(var(--primary-color-rgb), 0.15);
    overflow: hidden;
    display: flex;
    align-items: center;
    gap: 8px;
}

.view-profile-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.4),
        transparent
    );
    transition: all 0.6s ease;
}

.view-profile-btn::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background: var(--primary-color);
    transition: all 0.4s ease-in-out;
    z-index: -1;
    border-radius: 100px;
}

.view-profile-btn:hover {
    color: var(--secondary-color);
    transform: translate(-50%, -50%); /* Keep centered on hover */
}

.view-profile-btn:hover::before {
    left: 100%;
}

.view-profile-btn:hover::after {
    width: 100%;
}

.view-profile-btn i {
    font-size: 16px;
    transition: transform 0.3s ease;
}

.view-profile-btn:hover i {
    transform: translateX(3px);
}

.team-member-item:hover .view-profile-btn {
    opacity: 1;
    visibility: visible;
    transform: translate(-50%, -50%); /* Keep centered when appearing */
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .view-profile-btn {
        padding: 10px 20px;
        font-size: 13px;
    }
}

@media (max-width: 480px) {
    .view-profile-btn {
        padding: 8px 18px;
        font-size: 12px;
    }

    .view-profile-btn i {
        font-size: 14px;
    }
}

/************************************/
/***  	13. Our Testimonial css   ***/
/************************************/

.our-testimonial{
	background: var(--primary-color) url('../images/testimonial-bg.svg') no-repeat;
	background-position: center right;
	background-size: contain;
	padding: 100px 0 150px;
}

.our-testimonial .section-title{
	position: sticky;
	top: 20px;
	margin-bottom: 0;
}

.our-testimonial .section-title p,
.our-testimonial .section-title h2,
.our-testimonial .section-title h3::before,
.our-testimonial .section-title h3{
	color: var(--white-color);
}

.our-testimonial .swiper-wrapper{
	cursor: none;
}

.testimonial-item{
	background: var(--white-color);
	border-radius: 10px;
	padding: 30px;
}

.testimonial-item .testimonial-header{
	margin-bottom: 30px;
}

.testimonial-header .testimonial-rating{
	margin-bottom: 20px;
}

.testimonial-header .testimonial-rating i{
	font-size: 16px;
	color: #F56700;
	margin-right: 2px;
}

.testimonial-header .testimonial-rating i:last-child{
	margin-right: 0;
}

.testimonial-header .testimonial-content p{
	color: var(--text-color);
	margin: 0;
}

.testimonial-body{
	display: flex;
	align-items: center;
}

.testimonial-body .author-image{
	margin-right: 15px;
}

.testimonial-body .author-image img{
	width: 50px;
	height: 50px;
	aspect-ratio: 1/1;
	object-fit: cover;
	border-radius: 50%;
}

.testimonial-body .author-content h3{
	font-size: 20px;
	text-transform: capitalize;
	margin-bottom: 5px;
}

.testimonial-body .author-content{
	width: calc(100% - 66px);
}

.testimonial-body .author-content p{
	color: var(--text-color);
	text-transform: capitalize;
	margin: 0;
}

.testimonial-btn{
	display: flex;
	align-items: center;
	justify-content: left;
	margin-top: 40px;
}

.testimonial-slider .testimonial-button-next,
.testimonial-slider .testimonial-button-prev{
	position: relative;
	width: 40px;
	height: 40px;
	background-color: var(--secondary-color);
	border-radius: 6px;
	transition: all 0.4s ease-in-out;
}

.testimonial-slider .testimonial-button-next{
	margin-left: 20px;
}


.testimonial-slider .testimonial-button-next:hover,
.testimonial-slider .testimonial-button-prev:hover{
	background-color: var(--white-color);
}

.testimonial-slider .testimonial-button-next::before,
.testimonial-slider .testimonial-button-prev::before{
	content: '';
	position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: url("../images/arrow-blue-readmore-btn.svg") no-repeat center center;
    background-size: 15px auto;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.4s ease-in-out;
}

.testimonial-slider .testimonial-button-prev::before{
	transform: rotate(180deg);
}

/************************************/
/***   	  14. Our Blog css        ***/
/************************************/

.our-blog{
	background-color: var(--white-color);
	padding: 100px 0 120px;
}

.blog-item{
	height: calc(100% - 30px);
    margin-bottom: 30px;
}

.post-featured-image{
	position: relative;
	overflow: hidden;
	border-radius: 10px;
	margin-bottom: 30px;
}

.post-featured-image a{
	display: block;
	cursor: none;
	border-radius: 10px;
}

.post-featured-image img{
	aspect-ratio: 1/0.72;
	object-fit: cover;
	border-radius: 10px;
	transition: all 0.5s ease-out;
}

.blog-item:hover .post-featured-image img{
	transform: scale(1.1);
}

.blog-item .post-tags{
	position: absolute;
	top: 20px;
	right: 20px;
	z-index: 1;
}

.blog-item .post-tags a{
	position: relative;
	backdrop-filter: blur(5px);
	-webkit-backdrop-filter: blur(5px);
	color: var(--white-color);
	border-radius: 5px;
	padding: 6px 10px;
	font-size: 12px;
	text-transform: capitalize;
	cursor: pointer;
	overflow: hidden;
}

.blog-item .post-tags a::before{
	content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: var(--white-color);
    opacity: 30%;
    width: 100%;
    height: 100%;
}

.article-meta{
	margin-bottom: 20px;
}

.article-meta ul{
	list-style: none;
	padding: 0;
	margin: 0;
}

.article-meta ul li a{
	font-size: 14px;
	color: var(--text-color);
}

.post-item-body{
	margin-bottom: 20px;
}

.post-item-body h2{
	font-size: 22px;
	line-height: 1.4em;
}

.post-item-body h2 a{
	color: inherit;
}

.post-item-footer .readmore-btn{
	position: relative;
    text-transform: capitalize;
	font-weight: 500;
    color: var(--primary-color);
    padding-right: 20px;
    transition: 0.3s ease-in-out;
}

.post-item-footer .readmore-btn::after{
    content: '';
    position: absolute;
    top: 50%;
    right: 0;
    bottom: 0;
	transform: translateY(-50%);
    background-image: url("../images/arrow-blue-readmore-btn.svg");
    background-repeat: no-repeat;
    background-position: center center;
    width: 12px;
    height: 12px;
    transition: 0.3s ease-in-out;
}

/************************************/
/***        15. Footer css  	  ***/
/************************************/

footer.main-footer{
	background: var(--primary-color) url('../images/footer-bg.svg') no-repeat;
	background-position: bottom center;
	background-size: contain;
	padding: 80px 0;
	z-index: 2;
}

.about-footer{
	border-bottom: 1px solid var(--divider-color);
	padding-bottom: 50px;
	margin-bottom: 50px;
}

.footer-logo img{
	width: 100%;
	max-width: 220px;
}

.about-footer-content h3{
	font-family: var(--default-font);
	font-size: 22px;
	font-weight: 400;
	color: var(--white-color);
	margin-bottom: 20px;
}

.about-footer-content p{
	color: var(--white-color);
	margin-bottom: 0;
	margin-right: 30px;
}

.about-footer-list{
	display: flex;
	flex-wrap: wrap;
	row-gap: 50px;
	column-gap: 100px;
}

.footer-links{
	width: calc(33% - 66.66px);
}

.footer-links h3{
	display: inline-block;
	background-color: var(--secondary-color);
	color: var(--primary-color);
	border-radius: 100px;
	font-family: var(--default-font);
    font-size: 16px;
    font-weight: 400;
    text-transform: capitalize;
	padding: 5px 20px;
	margin-bottom: 20px;
}

.footer-links ul{
	list-style: none;
	margin: 0;
	padding: 0;
}

.footer-links ul li{
	position: relative;
	display: inline-block;
	color: var(--white-color);
	text-transform: capitalize;
	transition: all 0.3s ease-in-out;
	padding-left: 12px;
	margin-right: 15px;
}

.footer-links ul li::before{
	content: '';
	position: absolute;
	left: 0;
	top: 50%;
	bottom: 0;
	transform: translateY(-50%);
	background-color: var(--secondary-color);
	border-radius: 50%;
	width: 6px;
	height: 6px;
}

.footer-links ul li:last-child{
	margin-right: 0;
}

.footer-links ul li a{
	color: inherit;
}

.footer-links ul li:hover{
	color: var(--text-color);
}

.footer-links.service-links{
	width: calc(50% - 66.66px);
}

.footer-links.social-links{
	width: calc(17% - 66.66px);
	text-align: center;
}

.footer-links.social-links ul{
	list-style: none;
}

.footer-links.social-links ul li{
	padding-left: 0;
}

.footer-links.social-links ul li::before{
	display: none;
}

.footer-links.social-links ul li a i{
	color: var(--white-color);
	font-size: 22px;
	transition: all 0.3s ease-in-out;
}

.footer-links.social-links ul li:hover a i{
	color: var(--text-color);
}

.footer-links.working-links{
	width: calc(60% - 66.66px);
}

.footer-links.footer-contact-details{
	width: calc(40% - 66.66px);
}

.footer-contact-box{
	display: flex;
	align-items: center;
}

.footer-contact-details .footer-info-box{
	display: flex;
	margin-right: 25px;
}

.footer-contact-details .footer-info-box:last-child{
	margin-right: 0;
}

.footer-info-box .icon-box{
	margin-right: 10px;
}

.footer-info-box .icon-box img{
	max-width: 20px;
}

.footer-info-box-content{
	width: calc(100% - 30px);
}

.footer-info-box-content p{
	color: var(--white-color);
	margin-bottom: 0;
}

.footer-links.terms-condition-links{
	width: 100%;
	text-align: center;
}

.footer-copyright-text{
	margin-bottom: 10px;
}

.footer-copyright-text p{
	color: var(--white-color);
	text-transform: capitalize;
	margin: 0;
}

.footer-terms-condition ul{
	list-style: none;
	padding: 0;
	margin: 0;
}

.footer-terms-condition ul li{
	display: inline-block;
}

.footer-terms-condition ul li a{
	color: var(--white-color);
	text-transform: capitalize;
	text-decoration: underline;
}

/************************************/
/***     16. About us Page css	  ***/
/************************************/

.page-header{
    background: var(--primary-color) url(../images/hero-bg.jpg) no-repeat;
    background-position: center center;
    background-size: cover;
    overflow: hidden;
    padding: 220px 0;
    margin-top: -100px;
}

.page-header::before{
	content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: var(--black-color);
    opacity: 50%;
    width: 100%;
    height: 100%;
}

.page-header::after{
	content: '';
    position: absolute;
    right: 0;
    bottom: 0;
    background-image: url('../images/page-header-bg-shape-1.svg');
	background-repeat: no-repeat;
	background-position: right bottom;
	background-size: auto;
	width: 100%;
    height: 100%;
}

.page-header-box{
	position: relative;
	text-align: center;
	z-index: 1;
}

.page-header-box h1{
	display: inline-block;
	font-size: 66px;
	color: var(--white-color);
	margin-bottom: 5px;
}

.page-header-box ol{
	margin: 0;
	padding: 0;
	justify-content: center;
	margin-top: 10px;
}

.page-header-box ol li.breadcrumb-item{
	font-size: 18px;
	font-weight: 400;
	color: var(--white-color);
	text-transform: capitalize;
}

.page-header-box ol li.breadcrumb-item.active{
	color: var(--secondary-color);
}

.page-header-box ol li.breadcrumb-item a{
    color: inherit;
}

.page-header-box ol .breadcrumb-item+.breadcrumb-item::before{
    color: var(--white-color);
}

.page-about-us{
	background-color: var(--white-color);
	padding: 100px 0 150px;
	padding-left: calc(((100vw - 1300px) / 2) - 0px);
}

.page-about-us .container-fluid{
	padding: 0;
}

/* Start Spacing for Gallery Section */
.our-gallery.bg-radius-section {
    margin-top: 100px; /* Default spacing for desktop */
    position: relative;
}

/* Responsive margins */
@media only screen and (max-width: 1200px) {
    .our-gallery.bg-radius-section {
        margin-top: 80px;
    }
}

@media only screen and (max-width: 991px) {
    .our-gallery.bg-radius-section {
        margin-top: 70px;
    }
}

@media only screen and (max-width: 768px) {
    .our-gallery.bg-radius-section {
        margin-top: 60px;
    }
}

@media only screen and (max-width: 576px) {
    .our-gallery.bg-radius-section {
        margin-top: 50px;
    }
}
/* End Spacing for Gallery Section */

/* Modern Gallery Carousel Styles */
.gallery-slider {
    padding: 20px 0 40px;
    position: relative;
    overflow: visible; /* Changed from hidden to visible */
}

.gallery-card {
    position: relative;
    background: var(--white-color);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: auto; /* Changed from 100% to auto */
}

/* Doctor Publications Section Styles */
.doctor-publications {
    padding: 100px 0;
    background: linear-gradient(180deg, rgba(var(--primary-color-rgb), 0.03) 0%, rgba(var(--primary-color-rgb), 0) 100%);
}

.publication-item {
    display: flex;
    background-color: var(--white-color);
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    padding: 30px;
    height: 100%;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.publication-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
}

.publication-item:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 5px;
    height: 100%;
    background-color: var(--primary-color);
    border-radius: 5px 0 0 5px;
}

.publication-icon {
    flex: 0 0 60px;
    height: 60px;
    background-color: rgba(var(--primary-color-rgb), 0.1);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
}

.publication-icon i {
    font-size: 24px;
    color: var(--primary-color);
}

.publication-content {
    flex: 1;
}

.publication-content h3 {
    font-family: 'DM Sans', sans-serif;
    font-size: 20px;
    font-weight: 700;
    color: var(--heading-color);
    margin-bottom: 10px;
    line-height: 1.4;
}

.publication-meta {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 15px;
    font-family: 'Inter', sans-serif;
}

.publication-journal {
    font-size: 14px;
    color: var(--primary-color);
    font-weight: 600;
    margin-right: 15px;
}

.publication-date {
    font-size: 14px;
    color: var(--text-color);
    opacity: 0.8;
}

.publication-content p {
    font-family: 'Inter', sans-serif;
    font-size: 15px;
    line-height: 1.6;
}

/* RTL Accordion Styles */
[dir="rtl"] .how-work-accordion .accordion-item .accordion-button::after,
[dir="rtl"] .how-work-accordion .accordion-item .accordion-button.collapsed::after {
    right: auto !important;
    left: 20px !important;
    transform: translate(0, -50%) !important;
}

[dir="rtl"] .how-work-accordion .accordion-item .accordion-button.collapsed::after {
    transform: translate(0, -50%) rotate(-180deg) !important;
}

/* Override for Arabic mode */
html[lang="ar"] .how-work-accordion .accordion-item .icon-box {
    left: auto !important;
    right: 10px !important;
}

html[lang="ar"] .how-work-accordion .accordion-header .accordion-button {
    padding: 15px 80px 15px 50px !important;
    text-align: right !important;
}

html[lang="ar"] .how-work-accordion .accordion-item .accordion-button::after,
html[lang="ar"] .how-work-accordion .accordion-item .accordion-button.collapsed::after {
    right: auto !important;
    left: 20px !important;
}

/* Increase text padding to prevent overlap with icon */
html[lang="ar"] .how-work-accordion .accordion-header .accordion-button {
    padding-right: 80px !important;
}

/* Complete RTL override for accordion items */
html[lang="ar"] .how-work-accordion .accordion-item {
    position: relative;
    border-radius: 10px !important;
    overflow: visible !important; /* Changed from hidden to visible */
}

html[lang="ar"] .how-work-accordion .accordion-header {
    position: relative;
    overflow: visible !important;
}

html[lang="ar"] .how-work-accordion .accordion-header .accordion-button {
    padding: 15px 90px 15px 50px !important;
    text-align: right !important;
    font-family: var(--arabic-font) !important;
    overflow: visible !important;
}

html[lang="ar"] .how-work-accordion .accordion-button:not(.collapsed) {
    padding: 30px 90px 30px 50px !important;
    background-color: var(--primary-color);
    color: var(--white-color);
    overflow: visible !important;
}

html[lang="ar"] .how-work-accordion .accordion-item .icon-box {
    position: absolute !important;
    left: auto !important;
    right: 15px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    background-color: var(--secondary-color) !important;
    border-radius: 50% !important;
    width: 60px !important;
    height: 60px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    z-index: 10 !important;
    overflow: visible !important;
    margin: 0 !important;
    box-shadow: 0 0 0 5px var(--secondary-color) !important; /* Added shadow to ensure full circle */
}

/* Fix for icon box positioning */
html[lang="ar"] .accordion-item:first-child .icon-box {
    top: calc(50% + 5px) !important; /* Adjust for first item */
}

/* Ensure the icon box is fully visible */
html[lang="ar"] .how-work-accordion .accordion-item .icon-box::before {
    content: '';
    position: absolute;
    top: -5px;
    right: -5px;
    bottom: -5px;
    left: -5px;
    background-color: var(--secondary-color);
    border-radius: 50%;
    z-index: -1;
}

html[lang="ar"] .how-work-accordion .accordion-item .icon-box img {
    max-width: 36px !important;
    position: relative !important;
    z-index: 2 !important;
}

/* Ensure proper spacing around the accordion */
html[lang="ar"] .how-work-accordion {
    margin: 0 !important;
    padding: 0 !important;
}

/* Add extra space to the right of the accordion item */
html[lang="ar"] .how-work-accordion .accordion-item {
    padding-right: 10px !important;
    margin-right: 10px !important;
}

/* Fix for expanded state */
html[lang="ar"] .how-work-accordion .accordion-item .icon-box img {
    max-width: 36px !important;
    position: relative !important;
    z-index: 2 !important;
}

/* Ensure proper spacing in collapsed and expanded states */
html[lang="ar"] .how-work-accordion .accordion-button.collapsed {
    background-color: var(--secondary-color) !important;
    color: var(--primary-color) !important;
}

/* Improved RTL Accordion Styles with better spacing */
html[lang="ar"] .how-work-accordion .accordion-item .icon-box {
    position: absolute !important;
    left: auto !important;
    right: 15px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    background-color: var(--secondary-color) !important;
    border-radius: 50% !important;
    width: 60px !important;
    height: 60px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    z-index: 10 !important;
    overflow: visible !important;
    margin: 0 !important;
    box-shadow: 0 0 0 5px var(--secondary-color) !important;
}

/* Ensure proper spacing for accordion items in RTL */
html[lang="ar"] .how-work-accordion .accordion-item {
    position: relative !important;
    margin-bottom: 20px !important;
    overflow: visible !important;
}

/* Responsive adjustments for RTL accordion */
@media (max-width: 767px) {
    html[lang="ar"] .how-work-accordion .accordion-item .icon-box {
        width: 50px !important;
        height: 50px !important;
        right: 10px !important;
    }

    html[lang="ar"] .how-work-accordion .accordion-header .accordion-button {
        padding: 15px 70px 15px 40px !important;
    }

    html[lang="ar"] .how-work-accordion .accordion-item .icon-box img {
        max-width: 30px !important;
    }
}

/* Responsive Styles */
@media (max-width: 991px) {
    .doctor-publications {
        padding: 70px 0;
    }

    .publication-item {
        padding: 25px;
    }

    .publication-content h3 {
        font-size: 18px;
    }
}

@media (max-width: 767px) {
    .doctor-publications {
        padding: 50px 0;
    }

    .publication-item {
        flex-direction: column;
        padding: 20px;
    }

    .publication-icon {
        margin-right: 0;
        margin-bottom: 15px;
    }

    .publication-content h3 {
        font-size: 17px;
    }

    .publication-meta {
        flex-direction: column;
    }

    .publication-journal {
        margin-right: 0;
        margin-bottom: 5px;
    }
}

@media (max-width: 575px) {
    .publication-content h3 {
        font-size: 16px;
    }

    .publication-content p {
        font-size: 14px;
    }
}
    max-height: 400px; /* Added max-height */
    border: 1px solid rgba(0, 0, 0, 0.05);
    margin: 10px 5px; /* Added margin for spacing */
}

.gallery-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 45px rgba(0, 0, 0, 0.15);
}

.gallery-img-wrapper {
    position: relative;
    padding-top: 56.25%; /* 16:9 Aspect Ratio */
    overflow: hidden;
    background: #f8f9fa;
    max-height: 250px; /* Added max-height */
}

.gallery-img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.gallery-card:hover .gallery-img {
    transform: scale(1.1);
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        to bottom,
        rgba(0, 0, 0, 0.2),
        rgba(0, 0, 0, 0.7)
    );
    opacity: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.4s ease;
    backdrop-filter: blur(5px);
}

.gallery-card:hover .gallery-overlay {
    opacity: 1;
}

.gallery-icon {
    color: #fff;
}
/* Hero section base styles */
.hero.hero-slider-layout {
    position: relative;
    height: 100vh;
    max-height: 800px; /* Prevent excessive height */
    min-height: 600px;
    margin-top: -100px; /* Adjust for header */
    overflow: hidden;
}

/* Slide container */
.hero-slide {
    position: relative;
    height: 100%;
    display: flex;
    align-items: center;
    padding: 120px 0; /* Reduced padding */
}

/* Image styles */
.hero-slider-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.hero-slider-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
}

/* Video styles */
.hero-slider-video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.hero-slider-video video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
}

/* Content positioning */
.hero-content {
    position: relative;
    z-index: 2;
    padding-top: 60px; /* Adjust based on your header height */
}

/* Text styles */
.hero-content .section-title h1 {
    font-size: 3.5rem;
    line-height: 1.2;
    margin-bottom: 1rem;
}

.hero-content .section-title p {
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 2rem;
}

/* Overlay for better text visibility */
.hero-slide::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.4);
    z-index: 1;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
    .hero-content .section-title h1 {
        font-size: 3rem;
    }
}

@media (max-width: 991px) {
    .hero.hero-slider-layout {
        max-height: 700px;
    }

    .hero-content .section-title h1 {
        font-size: 2.5rem;
    }
}

@media (max-width: 767px) {
    .hero.hero-slider-layout {
        max-height: 600px;
    }

    .hero-slide {
        padding: 100px 0;
    }

    .hero-content .section-title h1 {
        font-size: 2rem;
    }
}
    font-size: 2.5rem;
    transform: translateY(20px) scale(0.8);
    opacity: 0;
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.gallery-card:hover .gallery-icon {
    transform: translateY(0) scale(1);
    opacity: 1;
}

.gallery-caption {
    padding: 1rem;
    background: var(--white-color);
}

.gallery-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    line-height: 1.4;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
}

.gallery-desc {
    font-size: 0.875rem;
    color: var(--text-color);
    margin-bottom: 0;
    line-height: 1.5;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

/* Type Badge with Glass Effect */
.gallery-type-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: rgba(255, 255, 255, 0.45); /* Reduced opacity for glass effect */
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-size: 0.85rem;
    font-weight: 500;
    color: var(--primary-color);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.18);
    z-index: 2;
    transition: all 0.3s ease;
}

/* Optional hover effect */
.gallery-type-badge:hover {
    background: rgba(255, 255, 255, 0.55);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

/* Responsive Adjustments */
@media (max-width: 1024px) {
    .gallery-type-badge {
        padding: 0.4rem 0.9rem;
        font-size: 0.8rem;
    }
}

@media (max-width: 768px) {
    .gallery-type-badge {
        top: 0.75rem;
        right: 0.75rem;
        padding: 0.35rem 0.8rem;
        font-size: 0.75rem;
        backdrop-filter: blur(4px);
        -webkit-backdrop-filter: blur(4px);
    }
}

@media (max-width: 576px) {
    .gallery-type-badge {
        top: 0.5rem;
        right: 0.5rem;
        padding: 0.3rem 0.7rem;
        font-size: 0.7rem;
        backdrop-filter: blur(3px);
        -webkit-backdrop-filter: blur(3px);
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
}

/* For very small devices */
@media (max-width: 360px) {
    .gallery-type-badge {
        padding: 0.25rem 0.6rem;
        font-size: 0.65rem;
    }
}

/* Video Duration Badge */
.video-duration {
    position: absolute;
    bottom: 1rem;
    right: 1rem;
    background: rgba(0, 0, 0, 0.75);
    color: #fff;
    padding: 0.4rem 0.8rem;
    border-radius: 50px;
    font-size: 0.85rem;
    font-weight: 500;
    backdrop-filter: blur(5px);
    z-index: 2;
}

/* Swiper Navigation Buttons */
.swiper-button-next,
.swiper-button-prev {
    width: 50px;
    height: 50px;
    background: #fff;
    border-radius: 50%;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.swiper-button-next:after,
.swiper-button-prev:after {
    font-size: 1.25rem;
    color: var(--primary-color);
    font-weight: bold;
}

.swiper-button-next:hover,
.swiper-button-prev:hover {
    background: var(--primary-color);
}

.swiper-button-next:hover:after,
.swiper-button-prev:hover:after {
    color: #fff;
}

/* Swiper Pagination */
.swiper-pagination {
    bottom: 0 !important;
}

.swiper-pagination-bullet {
    width: 10px;
    height: 10px;
    background: var(--primary-color);
    opacity: 0.3;
    transition: all 0.3s ease;
}

.swiper-pagination-bullet-active {
    width: 30px;
    border-radius: 5px;
    opacity: 1;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .gallery-card {
        max-height: 350px;
    }

    .gallery-img-wrapper {
        max-height: 200px;
    }

    .gallery-caption {
        padding: 0.75rem;
    }

    .gallery-title {
        font-size: 0.9rem;
        margin-bottom: 0.25rem;
    }

    .gallery-desc {
        font-size: 0.8rem;
        -webkit-line-clamp: 2;
    }
}

@media (max-width: 576px) {
    .gallery-slider {
        padding: 15px 0 30px;
    }

    .gallery-card {
        max-height: 300px;
    }

    .gallery-img-wrapper {
        max-height: 180px;
    }
}

/* Swiper Carousel Adjustments */
.swiper-container {
    padding-bottom: 40px; /* Add space for pagination */
    overflow: visible; /* Allow cards to be visible when hovering */
}

.swiper-slide {
    height: auto; /* Let the content determine the height */
}

/* Loading Skeleton Animation */
@keyframes shimmer {
    0% {
        background-position: -1000px 0;
    }
    100% {
        background-position: 1000px 0;
    }
}

.gallery-skeleton {
    background: linear-gradient(
        to right,
        #f6f7f8 8%,
        #edeef1 18%,
        #f6f7f8 33%
    );
    background-size: 2000px 100%;
    animation: shimmer 2s infinite linear;
}
.page-about-content{
	padding-right: 30px;
}

.page-about-content-body{
	margin-bottom: 40px;
}

.page-about-content-item{
	position: relative;
	border: 1px solid var(--secondary-color);
	border-radius: 10px;
	display: flex;
	align-items: center;
	margin-bottom: 20px;
	padding: 20px;
	overflow: hidden;
}

.page-about-content-item:last-child{
	margin-bottom: 0;
}

.page-about-content-item::before{
	content: '';
    position: absolute;
    top: 100%;
    right: 0;
    left: 0;
    background-color: var(--secondary-color);
    transition: all 0.4s ease-in-out;
    height: 100%;
    width: 100%;
    z-index: 0;
    opacity: 0;
}

.page-about-content-item:hover:before{
    top: 0;
    opacity: 1;
}

.page-about-content-item .icon-box{
    position: relative;
    margin-right: 10px;
	padding: 0 5px 5px 0;
	z-index: 1;
}

.page-about-content-item .icon-box::before{
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    background-color: var(--secondary-color);
    border-radius: 50%;
    width: 40px;
    height: 40px;
	transition: all 0.4s ease-in-out;
}

.page-about-content-item:hover .icon-box::before{
	background-color: var(--white-color);
}

.page-about-content-item .icon-box img{
    position: relative;
    max-width: 60px;
    z-index: 1;
}

.page-about-item-content{
	position: relative;
	width: calc(100% - 70px);
	z-index: 1;
}

.page-about-item-content p{
	margin: 0;
}

.page-about-content-box{
	background-color: var(--primary-color);
	border-radius: 10px;
	padding: 15px 20px;
}

.page-about-box-title{
	margin-bottom: 20px;
}

.page-about-box-title h3{
	font-size: 20px;
	color: var(--white-color);
	text-transform: capitalize;
}

.page-about-box-list ul{
	list-style-type: circle;
	padding: 0;
	margin: 0;
	padding-left: 20px;
}

.page-about-box-list ul li{
	color: var(--white-color);
	margin-bottom: 10px;
}

.page-about-box-list ul li:last-child{
	margin-bottom: 0;
}

.page-about-body-content{
	margin-top: 30px;
}

.page-about-body-content p{
	margin: 0;
}

.page-about-img-1{
	position: relative;
}

.page-about-img-1::before{
	content: '';
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	background: radial-gradient(50.39% 43.6% at 62.48% 53.16%, rgba(255, 255, 255, 0) 46.27%, var(--white-color) 100%);
	width: 100%;
	height: 100%;
	z-index: 1;
}

.page-about-img-1 img{
	width: 100%;
	aspect-ratio: 1 / 0.8;
	opacity: 50%;
	object-fit: cover;
}

.page-about-img-2{
	position: absolute;
	bottom: 0;
	left: 50%;
	z-index: 1;
}

.about-testimonial-box{
	position: absolute;
	left: 0;
	bottom: 50px;
	width: 55%;
	background: linear-gradient(90deg, #10455F 9.97%, #023047 100%);
	border-radius: 50px 0 0 0;
	padding: 15px;
	padding-left: calc(((100vw - 1300px) / 2) - 0px);
}

.about-testimonial-item p{
	font-family: var(--accent-font);
	font-size: 22px;
	color: var(--secondary-color);
	margin: 0;
}

.about-testimonial-btn{
	position: absolute;
	top: 0;
	right: 0;
	transform: translateX(-100%);
	display: flex;
	align-items: center;
	justify-content: right;
	z-index: 1;
}

.about-testimonial-btn .about-testimonial-button-next,
.about-testimonial-btn .about-testimonial-button-prev{
	position: relative;
	width: 30px;
	height: 30px;
	background-color: var(--secondary-color);
	border-radius: 50%;
	transition: all 0.4s ease-in-out;
}

.about-testimonial-btn .about-testimonial-button-next{
	margin-left: 10px;
}

.about-testimonial-btn .about-testimonial-button-next:hover,
.about-testimonial-btn .about-testimonial-button-prev:hover{
	background-color: var(--white-color);
}

.about-testimonial-btn .about-testimonial-button-next::before,
.about-testimonial-btn .about-testimonial-button-prev::before{
	content: '';
	position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: url("../images/arrow-blue-readmore-btn.svg") no-repeat center center;
    background-size: 12px auto;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.4s ease-in-out;
}

.about-testimonial-btn .about-testimonial-button-prev::before{
	transform: rotate(180deg);
}

.about-icon-box-list{
	background-color: var(--primary-color);
	border-radius: 0 50px 0 0;
	padding: 100px 0 120px;
	z-index: 1;
}

.about-icon-list-item{
	height: calc(100% - 30px);
	margin-bottom: 30px;
	text-align: center;
}

.about-icon-list-item .icon-box{
	position: relative;
	width: 70px;
	margin: 0 auto;
	margin-bottom: 20px;
}

/* Gallery Item Styles */
.gallery-item {
    position: relative;
    overflow: hidden;
    border-radius: 20px 20px 0 0; /* Top corners rounded, bottom corners square */
    margin-bottom: 30px;
}

.gallery-item a {
    display: block;
    position: relative;
    border-radius: 20px 20px 0 0; /* Match the image border-radius */
    overflow: hidden;
}

.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
    border-radius: 20px 20px 0 0 !important; /* Top corners rounded, bottom corners square */
    will-change: transform;
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
    border-radius: 20px 20px 0 0; /* Match the image border-radius */
}

.gallery-overlay i {
    color: white;
    font-size: 24px;
    transform: scale(0.8);
    transition: transform 0.3s ease;
}

/* Hover Effects */
.gallery-item:hover img {
    transform: scale(1.05);
    border-radius: 20px 20px 0 0 !important; /* Top corners rounded, bottom corners square */
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
}

.gallery-item:hover .gallery-overlay i {
    transform: scale(1);
}

/* Ensure container maintains border radius */
.gallery-item * {
    /* border-radius: inherit; */
}

.gallery-item a {
    display: block;
    position: relative;
}

.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
    border-radius: 20px;
}

/* Hover Effects */
.gallery-item:hover img {
    transform: scale(1.05);
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
}

.gallery-item:hover .gallery-overlay i {
    transform: scale(1);
}

/* Fancybox Customization */
.fancybox__container {
    --fancybox-bg: rgba(0, 0, 0, 0.95);
}

.fancybox__toolbar {
    --fancybox-color: #fff;
}

.fancybox__nav {
    --fancybox-color: #fff;
}

/* Fancybox Autoplay Button Styles */

/************************************/
/***     Gallery Page Styles      ***/
/************************************/

/* Gallery Grid Layout */
.page-gallery-grid {
    display: flex;
    flex-wrap: wrap;
    margin: -15px;
}

.page-gallery-item {
    flex: 0 0 25%; /* 4 items per row */
    max-width: 25%;
    padding: 15px;
}

/* Gallery Card Styles */
.page-gallery-card {
    position: relative;
    height: 100%;
    background: var(--white-color);
    border-radius: 24px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    transform: translateY(0); /* Ensure card is fully visible by default */
}

.page-gallery-card:hover {
    transform: translateY(-5px); /* Reduced hover lift */
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

/* Image Container */
.gallery-link {
    display: block;
    position: relative;
    width: 100%;
    padding-top: 75%; /* 4:3 Aspect ratio - reduced from 100% */
    overflow: hidden;
}

.page-gallery-img-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background: #f8f9fa;
}

.page-gallery-img {
    width: 100%;
    height: 100%;
    object-fit: cover; /* This ensures the image covers the area without distortion */
    object-position: center;
    transition: transform 0.4s ease;
}

.page-gallery-card:hover .page-gallery-img {
    transform: scale(1.05);
}

/* Overlay Styles */
.page-gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.page-gallery-card:hover .page-gallery-overlay {
    opacity: 1;
}

.page-gallery-icon {
    color: var(--white-color);
    font-size: 2.5rem;
    transform: scale(0.8);
    transition: transform 0.3s ease;
}

.page-gallery-card:hover .page-gallery-icon {
    transform: scale(1);
}

/* Caption Styles */
.page-gallery-caption {
    padding: 1.25rem;
    background: var(--white-color);
    flex-grow: 0;
}

.page-gallery-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--heading-color);
    margin-bottom: 0.5rem;
    line-height: 1.4;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.page-gallery-desc {
    font-size: 0.9rem;
    color: var(--text-color);
    margin-bottom: 0;
    line-height: 1.6;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

/* Responsive Adjustments */
@media (max-width: 1200px) {
    .page-gallery-item {
        flex: 0 0 33.333%;
        max-width: 33.333%;
    }
}

@media (max-width: 992px) {
    .page-gallery-item {
        flex: 0 0 50%;
        max-width: 50%;
    }
}

@media (max-width: 576px) {
    .page-gallery-item {
        flex: 0 0 100%;
        max-width: 100%;
    }

    .page-gallery-caption {
        padding: 1rem;
    }

    .page-gallery-title {
        font-size: 1rem;
    }

    .page-gallery-desc {
        font-size: 0.85rem;
    }
}
.fancybox__button--autoplay {
    position: relative;
    transition: all 0.3s ease;
}

.fancybox__button--autoplay.is-playing {
    animation: pulse 2s infinite;
}
.service-catagery-list ul li a {
    display: block;
    position: relative;
    color: var(--primary-color);
    font-size: 16px;
    text-transform: capitalize;
    background-color: var(--white-color);
    border-radius: 10px;
    padding: 15px 20px;
    transition: all 0.3s ease-in-out;
}

.service-catagery-list ul li a.active {
    background-color: var(--primary-color);
    color: var(--white-color);
}

.service-catagery-list ul li a:hover {
    background-color: var(--primary-color);
    color: var(--white-color);
}

.fancybox__button--autoplay svg {
    width: 20px;
    height: 20px;
    transition: all 0.3s ease;
}

.fancybox__button--autoplay:hover svg {
    transform: scale(1.1);
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* Fancybox toolbar enhancement */
.fancybox__toolbar {
    --fancybox-color: #fff;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    border-radius: 8px;
    padding: 4px;
    margin: 12px;
}

.fancybox__button {
    color: var(--fancybox-color);
    transition: all 0.3s ease;
}

.fancybox__button:hover {
    color: #fff;
    background-color: rgba(255, 255, 255, 0.1);
}

/* Video specific styles */
.fancybox__content video {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.fancybox__content iframe {
    width: 100%;
    height: 100%;
    border: none;
}
.about-icon-list-item .icon-box::before{
    content: '';
    position: absolute;
    bottom: -10px;
    right: -10px;
    background-color: var(--accent-color);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    transition: all 0.4s ease-in-out;
}

.about-icon-list-item .icon-box img{
	position: relative;
    max-width: 70px;
    z-index: 1;
}

.about-icon-list-content h3{
	font-size: 22px;
	text-transform: capitalize;
	color: var(--secondary-color);
}

.our-rehabilitation{
	position: relative;
	background: var(--white-color);
	padding: 100px 0 120px;
	z-index: 1;
}

.our-rehabilitation .section-title{
	text-align: center;
}

.therapy-rehabilitation-item{
	position: relative;
	border-radius: 10px;
	height: calc(100% - 30px);
	margin-bottom: 30px;
	overflow: hidden;
}

.therapy-rehabilitation-image a{
	position: relative;
	display: block;
}

.therapy-rehabilitation-image a::before{
	content: '';
    position: absolute;
	display: block;
    top: 0;
    right: 0;
    left: 0;
	bottom: 0;
    background: linear-gradient(179.94deg, rgba(0, 0, 0, 0.9) 0.05%, rgba(0, 0, 0, 0) 45%);
    height: 100%;
    width: 100%;
	z-index: 1;
}

.therapy-rehabilitation-image img{
	aspect-ratio: 1 / 1.28;
	object-fit: cover;
	border-radius: 10px;
	transition: all 0.4s ease-in-out;
}

/* Skeleton Section Styles */
.skeleton-section {
    padding: 80px 0;
    background: linear-gradient(180deg, rgba(2, 48, 71, 0.05) 0%, rgba(2, 48, 71, 0) 100%);
}

.skeleton-wrapper {
    position: relative;
    padding: 40px;
    background: #FFFFFF;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(2, 48, 71, 0.1);
    transition: all 0.3s ease;
}

.skeleton-wrapper:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(2, 48, 71, 0.15);
}

.skeleton-image {
    position: relative;
    text-align: center;
}

.skeleton-svg {
    max-width: 100%;
    height: auto;
    transition: all 0.3s ease;
}

.skeleton-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

/* Pulse animation for interactive points */
.pulse-dot {
    width: 12px;
    height: 12px;
    background: var(--primary-color);
    border-radius: 50%;
    position: absolute;
    cursor: pointer;
    pointer-events: auto;
}

.pulse-dot::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background: var(--primary-color);
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 0.8;
    }
    70% {
        transform: scale(2);
        opacity: 0;
    }
    100% {
        transform: scale(2);
        opacity: 0;
    }
}

/* Responsive Styles */
@media (max-width: 1200px) {
    .skeleton-wrapper {
        padding: 30px;
    }
}

@media (max-width: 991px) {
    .skeleton-section {
        padding: 60px 0;
    }

    .skeleton-wrapper {
        padding: 25px;
    }
}

@media (max-width: 768px) {
    .skeleton-section {
        padding: 40px 0;
    }

    .skeleton-wrapper {
        padding: 20px;
        margin: 0 15px;
    }

    .section-title h2 {
        font-size: 28px;
    }
}

@media (max-width: 480px) {
    .skeleton-wrapper {
        padding: 15px;
    }

    .section-title h2 {
        font-size: 24px;
    }

    .section-title p {
        font-size: 14px;
    }
}
.therapy-rehabilitation-item:hover .therapy-rehabilitation-image img{
	transform: scale(1.1);
}

.therapy-rehabilitation-header{
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	padding: 30px;
	z-index: 1;
}

.therapy-rehabilitation-content{
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 30px;
}

.therapy-rehabilitation-btn{
	margin-right: 20px;
}

.therapy-rehabilitation-btn a{
	position: relative;
	width: 50px;
	height: 50px;
	border-radius: 10px;
	display: flex;
	align-items: center;
	justify-content: center;
	overflow: hidden;
}

.therapy-rehabilitation-btn a::before{
	content: '';
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
	bottom: 0;
    background: var(--white-color);
	opacity: 10%;
    height: 100%;
    width: 100%;
}

.therapy-rehabilitation-btn a img{
	max-width: 20px;
	transform: rotate(-45deg);
	transition: all 0.3s ease-in-out;
}

.therapy-rehabilitation-item:hover .therapy-rehabilitation-btn a img{
	transform: rotate(0deg);
}

.therapy-rehabilitation-title{
	width: calc(100% - 70px);
}

.therapy-rehabilitation-title h3{
	font-size: 26px;
	text-transform: capitalize;
	color: var(--white-color);
}

.therapy-rehabilitation-dec p{
	color: var(--white-color);
	margin: 0;
}

.therapy-rehabilitation-body{
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	padding: 30px;
	z-index: 1;
}

.therapy-rehabilitation-list{
	position: relative;
	border-radius: 10px;
	overflow: hidden;
	backdrop-filter: blur(10px);
	-webkit-backdrop-filter: blur(10px);
	padding: 20px;
}

.therapy-rehabilitation-list::before{
	content: '';
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
	bottom: 0;
    background: var(--white-color);
	opacity: 20%;
    height: 100%;
    width: 100%;
}

.therapy-rehabilitation-list ul{
	list-style: none;
	padding: 0;
	margin: 0;
}

/* Preloader */
.no-js #loader {
    display: none;
}

.js #loader {
    display: block;
    position: absolute;
    left: 100px;
    top: 0;
}

.se-pre-con {
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 999999;
    background: url('../images/preloader-1.gif') center no-repeat #ffffff;
    text-align: center;
}

.therapy-rehabilitation-list ul li{
	position: relative;
}
.service-entry-image figure {
    border-radius: 20px;
    overflow: hidden;
    margin: 0;
}

.service-entry-image img {
    width: 100%;
    height: 300px; /* Set a consistent height */
    object-fit: cover;
    border-radius: 20px;
    transition: transform 0.3s ease;
}

.service-entry-image figure:hover img {
    transform: scale(1.05);
}
    color: var(--white-color);
    margin-bottom: 10px;
    padding-left: 25px;
}

.therapy-rehabilitation-list ul li:last-child{
	margin-bottom: 0;
}

.therapy-rehabilitation-list ul li::before{
	content: '';
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    background: url(../images/icon-pricing-list.svg) no-repeat;
    background-position: left center;
    background-size: cover;
    width: 16px;
    height: 16px;
}

.therapy-process{
	background: var(--primary-color) url('../images/therapy-process-bg-circle.svg') no-repeat;
	background-position: right bottom;
	padding: 100px 0 150px;
	z-index: 1;
}

.therapy-process .section-title h3::before,
.therapy-process .section-title h2,
.therapy-process .section-title h3{
	color: var(--white-color);
}

.therapy-process .section-btn .btn-default::after{
	background-color: var(--accent-color);
}

.therapy-process-content{
	margin-right: 30px;
}

.therapy-process-item{
	position: relative;
	background-color: var(--secondary-color);
	border: 1px solid var(--divider-color);
	border-radius: 10px;
	display: flex;
	align-items: center;
	padding: 20px;
	transition: all 0.4s ease-in-out;
	overflow: hidden;
	margin-bottom: 30px;
}

.therapy-process-item:last-child{
	margin-bottom: 0;
}

.therapy-process-item:hover{
	background-color: var(--accent-color);
	border-color: var(--accent-color);
	transform: translateX(20px);
}

.therapy-process-item::before{
    content: '';
    position: absolute;
    top: 100%;
    right: 0;
    left: 0;
    background-color: var(--accent-color);
    transition: all 0.4s ease-in-out;
    height: 100%;
    width: 100%;
    z-index: 0;
    opacity: 0;
}

.therapy-process-item:hover:before{
	top: 0;
	opacity: 1;
}

.therapy-process-item .icon-box{
	position: relative;
	background-color: var(--white-color);
	border-radius: 10px;
	width: 100px;
	height: 100px;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 30px;
	z-index: 1;
}

.therapy-process-item .icon-box img{
	max-width: 50px;
}

.therapy-process-item-content{
	position: relative;
	width: calc(100% - 120px);
	z-index: 1;
}

.therapy-process-item-content h3{
	font-size: 22px;
	text-transform: capitalize;
	margin-bottom: 10px;
	transition: all 0.4s ease-in-out;
}

.therapy-process-item:hover .therapy-process-item-content h3{
	color: var(--white-color);
}

.therapy-process-item-content p{
	margin: 0;
	transition: all 0.4s ease-in-out;
}

.therapy-process-item:hover .therapy-process-item-content p{
	color: var(--white-color);
}

.therapy-process-images{
	position: relative;
	margin: 10px;
	margin-bottom: 0;
}

.therapy-process-img-1{
	text-align: center;
	width: 530px;
	margin: 0 auto;
	padding: 55px 0;
}

.therapy-process-img-2{
	position: absolute;
	top: 0;
	left: 0;
	transform: rotate(-4deg);
	z-index: 1;
}

.therapy-process-img-3{
	position: absolute;
	bottom: 0;
	right: 0;
	transform: rotate(4deg);
	z-index: 1;
}

.therapy-process-img-3 figure,
.therapy-process-img-2 figure,
.therapy-process-img-1 figure{
	border-radius: 10px;
}

.therapy-process-img-3 img,
.therapy-process-img-2 img,
.therapy-process-img-1 img{
	width: 100%;
	border-radius: 10px;
}

.therapy-process-img-1 img{
	aspect-ratio: 1 / 1.1;
	object-fit: cover;
}

.therapy-process-img-2 img{
	aspect-ratio: 1 / 1.05;
	object-fit: cover;
}

.therapy-process-img-3 img{
	aspect-ratio: 1 / 1.14;
	object-fit: cover;
}

.our-video{
	background: var(--white-color);
	padding: 100px 0 150px;
	z-index: 2;
	overflow: hidden;
}

.our-video::before{
	content: '';
	position: absolute;
	top: 0;
	bottom: 0;
	right: 0;
	background: url('../images/our-video-bg.jpg');
	background-repeat: no-repeat;
	background-position: right center;
	opacity: 50%;
	width: 60%;
	height: 100%;
}

.our-video::after{
	content: '';
	position: absolute;
	top: 0;
	bottom: 0;
	right: 0;
	background: radial-gradient(50.03% 56.03% at 50% 49.99%, rgba(255, 255, 255, 0) 57.33%, #FFFFFF 100%);
	width: 60%;
	height: 100%;
}

.our-video-content .section-btn{
	text-align: left;
}

.our-video-content{
	position: relative;
	margin-right: 40px;
	z-index: 1;
}

.video-play-button{
	position: relative;
	text-align: center;
	z-index: 1;
}

.video-play-button a{
	position: relative;
	cursor: none;
}

.video-play-button a::before{
	content: '\f04b';
	font-family: 'FontAwesome';
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	font-size: 30px;
	color: var(--primary-color);
}

.video-play-button a img{
	max-width: 168px;
	animation: rotate 30s infinite linear;
}

@keyframes rotate{
	from{
		transform: rotate(0deg);
	  }
	to{
		transform: rotate(360deg);
	}
}

.process-steps{
	background: url('../images/process-steps-line.svg'), var(--secondary-color);
	background-repeat: no-repeat;
	background-position: bottom 160px center;
	background-size: contain;
	padding: 100px 0 150px;
	z-index: 2;
}

.process-steps .section-title{
	text-align: center;
	width: 100%;
	max-width: 750px;
	margin: 0 auto;
}

.process-steps-line{
	padding-top: 100px;
}

.process-step-box{
	position: relative;
	text-align: center;
	padding-top: 70px;
}

.process-steps .col-lg-3:nth-last-child(even) .process-step-box{
	margin-top: 50px;
}

.process-step-no{
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	z-index: 0;
}

.process-step-no h2{
	font-family: var(--default-font);
	font-size: 114px;
	font-weight: 600;
	background-image: linear-gradient(180deg, #FFFFFF 0%, rgba(255, 255, 255, 0) 100%);
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

.process-step-content{
	position: relative;
	z-index: 1;
}

.process-step-content h3{
	font-size: 22px;
	text-transform: capitalize;
	color: var(--accent-color);
	margin-bottom: 10px;
}

.process-step-content p{
	margin: 0;
}

.our-testimonial.about-our-testimonial,
.therapist-team.about-therapist-team{
	z-index: 2;
}

.page-about-faqs{
	background-color: var(--white-color);
	padding: 100px 0 150px;
	z-index: 2;
	overflow: hidden;
}

.page-about-faqs::before{
	content: '';
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	background: radial-gradient(9.88% 40.69% at 18.54% 52.54%, rgba(255, 255, 255, 0) -60%, #FFFFFF 150%), url('../images/page-about-faqs-bg-1.jpg');
	background-repeat: no-repeat;
	background-position: center center;
	background-size: cover;
	width: 100%;
	height: 100%;
}

.page-about-faqs::after{
	content: '';
	position: absolute;
	top: 0;
	bottom: 0;
	right: 0;
	background: radial-gradient(53.62% 49.95% at 53.62% 50.05%, rgba(255, 255, 255, 0) -60%, #FFFFFF 100%), url('../images/page-about-faqs-bg-2.jpg');
	background-repeat: no-repeat;
	background-position: center center;
	background-size: cover;
	width: 50%;
	height: 100%;
}

.about-faqs-content{
	position: relative;
	z-index: 1;
}

.about-faq-section .accordion-item{
	border-radius: 10px;
	margin-bottom: 30px;
    padding: 0;
	transition: all 0.3s ease-in-out;
	overflow: hidden;
}

.about-faq-section .accordion-item:last-child{
	margin-bottom: 0;
}

.about-faq-section .accordion-header .accordion-button{
	font-size: 20px;
	line-height: 1.2em;
	background-color: var(--secondary-color);
	color: var(--primary-color);
	padding: 20px 50px 20px 20px;
	transition: all 0.3s ease-in-out;
}

.about-faq-section .accordion-button:not(.collapsed){
   background-color: var(--primary-color);
   color: var(--white-color);
   border-bottom: 1px solid var(--divider-color);
}

.about-faq-section .accordion-header .accordion-button.collapsed{
	background-color: var(--secondary-color);
	color: var(--primary-color);
}

.about-faq-section .accordion-item .accordion-button::after,
.about-faq-section .accordion-item .accordion-button.collapsed::after{
	content: '\f077';
    font-family: "Font Awesome 6 Free";
    position: absolute;
    right: 20px;
    top: 50%;
    bottom: auto;
    transform: translate(0px, -50%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 900;
    font-size: 20px;
    width: 20px;
    height: 20px;
    padding: 5px;
    color: var(--white-color);
    transition: all 0.3s ease-in-out;
}

.about-faq-section .accordion-item .accordion-button.collapsed::after{
	transform: translate(0px, -50%) rotate(-180deg);
    color: var(--primary-color);
}

.about-faq-section .accordion-item .accordion-body{
	background-color: var(--primary-color);
	padding: 20px 50px 20px 20px;
}

.about-faq-section .accordion-item .accordion-body p{
    color: var(--white-color);
	margin: 0;
}

.cta-box{
	background: url('../images/cta-box-bg.jpg') no-repeat;
	background-position: center center;
	background-size: cover;
	padding: 100px 0 150px;
	z-index: 2;
	overflow: hidden;
}

.cta-box::before{
	content: '';
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	background: var(--black-color);
	opacity: 30%;
	width: 100%;
	height: 100%;
}

.cta-box-content{
	position: relative;
	text-align: center;
	width: 100%;
	max-width: 900px;
	margin: 0 auto;
	z-index: 1;
}

.cta-box-content .section-title h3::before,
.cta-box-content .section-title h3,
.cta-box-content .section-title h2,
.cta-box-content .section-title p{
	color: var(--white-color);
}

.cta-box-content .section-btn{
	text-align: center;
}

/************************************/
/*** 	 17. Page service css	  ***/
/************************************/

.page-header.service-page-header::after{
	background-image: url('../images/page-header-bg-shape-2.svg');
}

.service-process{
	background: var(--secondary-color);
	padding: 100px 0 150px;
}

.service-process-list .service-process-list-item{
	display: flex;
	margin-bottom: 40px;
	padding-bottom: 40px;
	border-bottom: 1px solid var(--white-color);
}

.service-process-list .service-process-list-item:last-child{
	margin-bottom: 0;
	padding-bottom: 0;
	border-bottom: none;
}

.service-process-list-item .icon-box{
	margin-right: 20px;
}

.service-process-list-item .icon-box img{
	max-width: 17px;
}

.service-process-list-item .service-process-content{
	width: calc(100% - 37px);
}

.service-process-list-item .service-process-content-body{
	display: flex;
	justify-content: space-between;
	margin-bottom: 15px;
}

.service-process-list-item .service-process-content-body .service-process-btn{
	margin-left: 20px;
}

.service-process-list-item .service-process-title h3{
	font-size: 22px;
}

.service-process-list-item .service-process-btn a{
	background: var(--primary-color);
	width: 40px;
	height: 40px;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease-in-out;
}

.service-process-list-item .service-process-btn img{
	transform: rotate(-45deg);
	max-width: 20px;
	transition: all 0.3s ease-in-out;
}

.service-process-list-item:hover .service-process-btn a{
	background: var(--accent-color);
}

.service-process-list-item:hover .service-process-btn a img{
	transform: rotate(0deg);
}

.service-process-list-item .service-process-footer p{
	margin: 0;
}

.service-process-img img{
	aspect-ratio: 1 / 1.25;
	object-fit: cover;
	border-radius: 10px;
}

.service-process-box-content{
	text-align: right;
}

.service-process-box-content .section-btn{
	margin: 0;
}

.service-process-box-content .section-btn .btn-default{
	background: var(--white-color);
}

.page-services{
	position: relative;
	background: var(--white-color);
	border-radius: 50px 50px 0 0;
	margin-top: -50px;
	padding: 100px 0 120px;
}

.page-services .section-title{
	text-align: center;
}

/************************************/
/*** 	 18. service Single css	  ***/
/************************************/

.page-header.service-single-page-header::after{
	background-image: url('../images/page-header-bg-shape-3.svg');
}

.page-service-single{
	background-color: var(--white-color);
	padding: 100px 0 150px;
}

.service-sidebar{
	position: sticky;
	top: 20px;
}

.service-catagery-list{
	background-color: var(--secondary-color);
	border-radius: 10px;
	padding: 30px;
	margin-bottom: 40px;
}

.service-catagery-list h3{
	font-size: 30px;
	text-transform: capitalize;
	margin-bottom: 25px;
}

.service-catagery-list ul{
	list-style: none;
	margin: 0;
	padding: 0;
}

.service-catagery-list ul li{
	margin-bottom: 20px;
}

.service-catagery-list ul li:last-child{
	margin-bottom: 0;
}

.service-catagery-list ul li a{
	display: block;
    position: relative;
	font-family: var(--accent-font);
	text-transform: capitalize;
    background-color: var(--white-color);
	color: var(--text-color);
	border-radius: 10px;
	line-height: normal;
	padding: 20px 40px 20px 20px;
	transition: all 0.3s ease-in-out;
}

.service-catagery-list ul li:hover a{
	background-color: var(--primary-color);
	color: var(--white-color);
}

.service-catagery-list ul li a::after{
    content: '';
    display: block;
    position: absolute;
    top: 50%;
    right: 0px;
	transform: translate(-20px, -50%);
	background-image: url('../images/arrow-blue-readmore-btn.svg');
    background-repeat: no-repeat;
    background-position: center center;
    background-size: cover;
	width: 16px;
	height: 16px;
    transition: all 0.3s ease-in-out;
}

.service-catagery-list ul li:hover a::after{
	filter: brightness(0) invert(1);
}

.working-hours{
	background-color: var(--secondary-color);
	border-radius: 10px;
	padding: 30px;
	margin-bottom: 40px;
}

.working-hours h3{
	font-size: 30px;
	text-transform: capitalize;
	margin-bottom: 25px;
}

.working-hours ul{
	list-style: none;
	margin: 0;
	padding: 0;
}

.working-hours ul li{
	margin-bottom: 20px;
}

.working-hours ul li:last-child{
	margin-bottom: 0;
}

.working-hours ul li a{
	display: block;
    position: relative;
	font-family: var(--accent-font);
	text-transform: capitalize;
    background-color: var(--white-color);
	color: var(--text-color);
	border-radius: 10px;
	line-height: normal;
	padding: 20px 20px 20px 50px;
	transition: all 0.3s ease-in-out;
}

.working-hours ul li:hover a{
	background-color: var(--primary-color);
	color: var(--white-color);
}

.working-hours ul li a::after{
    content: '\f017';
	font-family: 'FontAwesome';
    display: block;
    position: absolute;
    top: 50%;
    left: 0px;
	transform: translate(20px, -50%);
	font-size: 20px;
	color: var(--primary-color);
	width: 20px;
	height: 20px;
    transition: all 0.3s ease-in-out;
}

.working-hours ul li:hover a::after{
	color: var(--white-color);
}

.working-hours-btn{
	margin-top: 20px;
}

.working-hours-btn .btn-default{
	background: var(--white-color);
	width: 100%;
}

.working-hours-btn .btn-default span{
	text-align: center;
	width: 100%;
}

.sidebar-cta-box{
	position: relative;
	border-radius: 10px;
	overflow: hidden;
}

.sidebar-cta-image{
	position: relative;
}

.sidebar-cta-image::before{
	content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: var(--primary-color);
    opacity: 70%;
    width: 100%;
    height: 100%;
}

.sidebar-cta-image img{
	width: 100%;
	aspect-ratio: 1 / 1.01;
	object-fit: cover;
	border-radius: 10px;
}

.sidebar-cta-item{
	position: absolute;
	top: 50%;
	left: 0;
	right: 0;
	transform: translateY(-50%);
	z-index: 1;
}

.sidebar-cta-item .icon-box{
	border: 1px solid var(--secondary-color);
	border-radius: 50%;
	width: 100px;
	height: 100px;
	margin: 0 auto;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 30px;
}

.sidebar-cta-item .icon-box figure{
	position: relative;
	background-color: var(--secondary-color);
	border-radius: 50%;
	width: 80px;
	height: 80px;
	display: flex;
	align-items: center;
	justify-content: center;
	overflow: hidden;
}

.sidebar-cta-item .icon-box figure::before{
	content: '';
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    background: var(--white-color);
    border-radius: 50%;
    width: 100%;
    height: 100%;
    transform: scale(0);
    transition: all 0.4s ease-in-out;
}

.sidebar-cta-item:hover .icon-box figure::before{
	transform: scale(1);
}

.sidebar-cta-item .icon-box figure img{
	position: relative;
	max-width: 36px;
	z-index: 1;
}

.sidebar-cta-content{
	text-align: center;
}

.sidebar-cta-content h3{
	font-size: 30px;
	text-transform: capitalize;
	color:  var(--white-color);
	margin-bottom: 15px;
}

.sidebar-cta-content p{
	font-size: 22px;
	font-weight: 600;
	color: var(--white-color);
	margin: 0;
}

.service-single-image{
	margin-bottom: 30px;
}

.service-single-image img{
	border-radius: 10px;
	aspect-ratio: 1 / 0.55;
    object-fit: cover;
}

.service-entry{
	margin-bottom: 40px;
}

.service-entry h2{
	font-size: 36px;
	margin-bottom: 25px;
}

.service-entry h3{
	font-size: 20px;
	margin-bottom: 25px;
}

.service-entry p{
	margin-bottom: 20px;
}

.service-entry p:last-child{
	margin-bottom: 0px;
}

.service-entry ul{
	list-style: none;
	margin: 0;
	padding: 0;
	display: flex;
	flex-wrap: wrap;
	gap: 20px;
	margin-bottom: 30px;
}

.service-entry ul li{
	position: relative;
    width: calc(50% - 10px);
	color: var(--primary-color);
	font-size: 16px;
	text-transform: capitalize;
	padding-left: 30px;
}

.service-entry ul li:before{
	content: '';
    position: absolute;
    top: 50%;
    left: 0;
	transform: translateY(-50%);
    background: var(--primary-color) url(../images/arrow-white.svg) no-repeat;
    background-position: center center;
    background-size: 8px auto;
	border-radius: 50%;
    width: 20px;
    height: 20px;
}

.service-entry-img-1 figure,
.service-entry-img-2 figure{
    display: block;
}

.service-entry-img-1 img,
.service-entry-img-2 img{
    aspect-ratio: 1 / 1.01;
    object-fit: cover;
    border-radius: 10px;
}

.about-faq-section.our-faqs-section .accordion-item .accordion-body{
	background-color: var(--secondary-color);
}

.about-faq-section.our-faqs-section .accordion-item .accordion-body p{
	color: var(--text-color);
}

.about-faq-section.our-faqs-section .accordion-item .accordion-button::after,
.about-faq-section.our-faqs-section .accordion-item .accordion-button.collapsed::after{
	content: '\f062';
}

/************************************/
/***     19. Blog Archive css     ***/
/************************************/

.page-header.blog-page-header::after{
	background-image: url('../images/page-header-bg-shape-4.svg');
}

.page-blog{
	background-color: var(--white-color);
	padding: 100px 0 150px;
}

.page-blog .blog-item{
	height: calc(100% - 40px);
	margin-bottom: 40px;
}

.page-pagination{
    margin-top: 30px;
    text-align: center;
}

.page-pagination ul{
    justify-content: center;
    padding: 0;
    margin: 0;
}

.page-pagination ul li a,
.page-pagination ul li span{
    display: flex;
    text-decoration: none;
    justify-content: center;
    align-items: center;
    background: var(--secondary-color);
    color: var(--primary-color);
	border-radius: 10px;
    width: 40px;
    height: 40px;
    margin: 0 5px;
    font-weight: 700;
	line-height: 1em;
    transition: all 0.3s ease-in-out;
}

.page-pagination ul li.active a,
.page-pagination ul li a:hover{
    background: var(--primary-color);
	color: var(--white-color);
}

/************************************/
/***      20. Blog Single css	  ***/
/************************************/

.page-header.blog-single-page-header::after{
	background-image: url('../images/page-header-bg-shape-5.svg');
}

.page-single-post{
	background-color: var(--white-color);
	padding: 100px 0 150px;
}

.post-single-meta{
	margin-top: 10px;
}

.post-single-meta ol li.breadcrumb-item{
	font-size: 18px;
}

.post-single-meta ol li i{
    font-size: 18px;
    color: var(--white-color);
    margin-right: 5px;
}

.post-image{
	position: relative;
	margin-bottom: 30px;
}

.post-image figure{
	display: block;
}

.post-image figure,
.post-image img{
	aspect-ratio: 1 / 0.50;
	object-fit: cover;
	border-radius: 10px;
}

.post-content{
	width: 100%;
	max-width: 1100px;
	margin: 0 auto;
}

.post-entry{
	border-bottom: 1px solid var(--secondary-color);
	padding-bottom: 30px;
    margin-bottom: 30px;
}

.post-entry:after{
    content: '';
    display: block;
    clear: both;
}

.post-entry a{
    color: var(--accent-color);
}

.post-entry h1,
.post-entry h2,
.post-entry h3,
.post-entry h4,
.post-entry h5,
.post-entry h6{
	margin: 0 0 0.6em;
}

.post-entry h1 span,
.post-entry h2 span{
	font-weight: 400;
}

.post-entry h1{
	font-size: 66px;
}

.post-entry h2{
	font-size: 54px;
}

.post-entry h3{
	font-size: 40px;
}

.post-entry h4{
	font-size: 30px;
}

.post-entry h5{
	font-size: 24px;
}

.post-entry h6{
	font-size: 18px;
}

.post-entry p{
	margin-bottom: 20px;
}

.post-entry p:last-child{
	margin-bottom: 0;
}

.post-entry p strong{
	color: var(--primary-color);
	font-size: 18px;
	font-weight: 600;
}

.post-entry ol{
    margin: 0 0 30px;
}

.post-entry ol li{
    margin-bottom: 20px;
    font-size: 18px;
    font-weight: 600;
    color: var(--text-color);
}

.post-entry ul{
	padding: 0;
	margin: 20px 0 20px;
	padding-left: 20px;
}

.post-entry ul li{
	font-size: 18px;
    font-weight: 500;
    color: var(--primary-color);
    position: relative;
    margin-bottom: 15px;
}

.post-entry ul li:last-child{
	margin-bottom: 0;
}

.post-entry ul ul,
.post-entry ul ol,
.post-entry ol ol,
.post-entry ol ul{
    margin-top: 20px;
    margin-bottom: 0;
}

.post-entry ul ul li:last-child,
.post-entry ul ol li:last-child,
.post-entry ol ol li:last-child,
.post-entry ol ul li:last-child{
    margin-bottom: 0;
}

.post-entry blockquote{
	background-color: var(--secondary-color);
	background-image: url(../images/icon-blockquote.svg);
	background-repeat: no-repeat;
	background-position: left 35px top 40px;
    background-size: 45px;
    border-radius: 10px;
    padding: 30px 30px 30px 100px;
    margin-bottom: 30px;
}

.post-entry blockquote p{
	color: var(--primary-color);
	font-size: 20px;
	font-weight: 600;
	line-height: 1.4em;
}

.post-entry blockquote p:last-child{
	margin-bottom: 0;
}

.tag-links{
	font-size: 22px;
	font-weight: 600;
	color: var(--primary-color);
	display: inline-block;
}

.post-tags .tag-links a{
    display: inline-block;
    font-size: 16px;
    font-weight: 500;
    text-transform: capitalize;
    background-color: var(--primary-color);
    color: var(--white-color);
	border-radius: 10px;
    padding: 8px 20px;
    margin-left: 10px;
    margin-bottom: 10px;
	transition: all 0.3s ease-in-out;
}

.post-tags .tag-links a:hover{
	background: var(--text-color);
}

.post-social-sharing{
    text-align: right;
}

.post-social-sharing ul{
    list-style: none;
    padding: 0;
    margin: 0;
}

.post-social-sharing ul li{
    display: inline-block;
    margin-right: 10px;
}

.post-social-sharing ul li:last-child{
	margin-right: 0;
}

.post-social-sharing ul li a{
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
	background-color: var(--primary-color);
    color: var(--white-color);
	border-radius: 10px;
    width: 38px;
    height: 38px;
    transition: all 0.3s ease-in-out;
}

.post-social-sharing ul li:hover a{
	background-color: var(--text-color);
}

.post-social-sharing ul li a i{
    font-size: 18px;
    color: inherit;
    transition: all 0.3s ease-in-out;
}

.post-social-sharing ul li:hover a i{
    color: var(--secondry-color);
}

/************************************/
/***      21. Page Team css 	  ***/
/************************************/

.page-header.team-page-header::after{
	background-image: url('../images/page-header-bg-shape-6.svg');
}

.page-team{
	background-color: var(--white-color);
	padding: 100px 0 120px;
}

/************************************/
/***      22. Team Single css 	  ***/
/************************************/

.page-header.team-page-single-header::after{
	background-image: url('../images/page-header-bg-shape-1.svg');
}

.page-team-single{
	background: var(--white-color);
	padding: 100px 0 150px;
}

.page-team-single .team-member-image figure{
	width: 100%;
}

.page-team-single .team-member-image img{
	width: 100%;
    border-radius: 10px;
    aspect-ratio: 1 / 1.15;
    object-fit: cover;
}

.team-member-details{
	margin-left: 50px;
}

.member-detail-header{
	display: flex;
	align-items: center;
	justify-content: space-between;
	width: 100%;
	margin-bottom: 30px;
	padding-bottom: 30px;
	border-bottom: 1px solid var(--secondary-color);
}

.member-detail-header .member-detail-title h2{
	font-size: 30px;
	margin-bottom: 10px;
}

.member-detail-header .member-detail-title p{
	text-transform: capitalize;
	margin: 0;
}

.member-social-list ul{
	margin: 0;
	padding: 0;
}

.member-social-list ul li{
	display: inline-flex;
	margin-right: 10px;
}

.member-social-list ul li:last-child{
	margin-right: 0;
}

.member-social-list ul li a i{
	color: var(--primary-color);
	font-size: 20px;
	transition: all 0.3s ease-in-out;
}

.member-social-list ul li:hover a i{
	color: var(--text-color);
}

.member-detail-content{
	margin-bottom: 30px;
}

.member-detail-content p{
	margin: 0;
}

.member-detail-body{
	display: flex;
	flex-wrap: wrap;
	gap: 40px;
}

.member-detail-body .member-detail-list-item{
	width: calc(50% - 20px);
	display: flex;
}

.member-detail-list-item .icon-box{
	position: relative;
	margin-right: 20px;
	padding: 0 10px 10px 0;
}

.member-detail-list-item .icon-box::before{
	content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    background-color: var(--secondary-color);
    border-radius: 50%;
    width: 50px;
    height: 50px;
}

.member-detail-list-item .icon-box img{
	max-width: 45px;
	position: relative;
	top: 4px;
}

.member-detail-list-content{
	width: calc(100% - 75px);
}

.member-detail-list-content h3{
	font-size: 24px;
	text-transform: capitalize;
	margin-bottom: 10px;
}

.member-detail-list-content p{
	font-size: 18px;
	margin: 0;
}

.about-icon-box-list.member-expertise{
	border-radius: 50px 50px 0 0;
	z-index: 0;
}

.about-icon-box-list.member-expertise .section-title{
	text-align: center;
}

.about-icon-box-list.member-expertise .section-title h3::before{
	color: var(--white-color);
}

.about-icon-box-list.member-expertise .section-title h3,
.about-icon-box-list.member-expertise .section-title h2{
	color: var(--white-color);
}

.member-personal-info{
	background: var(--white-color);
	padding: 100px 0 150px;
}

.member-info-list ul{
	padding: 0;
	margin: 0;
	list-style: none;
}

.member-info-list ul li{
	position: relative;
	color: var(--primary-color);
	padding-left: 30px;
	margin-bottom: 15px;
}

.member-info-list ul li:last-child{
	margin-bottom: 0;
}

.member-info-list ul li:before{
	position: absolute;
	content: '\f058';
    font-family: 'Font Awesome 6 Free';
	font-size: 20px;
    font-weight: 900;
    color: var(--primary-color);
    display: inline-block;
    line-height: normal;
    top: 3px;
    left: 0;
}

.member-working-hour{
	position: relative;
	background: url('../images/working-hour-bg.jpg') no-repeat;
	background-position: center center;
	background-size: cover;
	border-radius: 10px;
	margin-left: 50px;
	overflow: hidden;
}

.member-working-hour-box{
	backdrop-filter: blur(6px);
	-webkit-backdrop-filter: blur(6px);
	padding: 30px;
}

.member-working-hour::before{
	content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: var(--white-color);
    opacity: 20%;
    width: 100%;
    height: 100%;
	z-index: 1;
}

.member-working-hour .section-title{
	position: relative;
	z-index: 1;
}

.member-working-hour .section-title h3::before{
	color: var(--white-color);
}

.member-working-hour .section-title h3,
.member-working-hour .section-title h2,
.member-working-hour .section-title p{
	color: var(--white-color);
}

.member-working-hour .section-title h2{
	font-size: 36px;
}

.member-working-hour-list{
	position: relative;
	z-index: 1;
}

.member-working-hour-list ul{
	list-style: none;
	margin: 0;
	padding: 0;
}

.member-working-hour-list ul li{
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 15px;
}

.member-working-hour-list ul li:last-child{
	margin-bottom: 0;
}

.member-working-hour-list ul li h3{
	font-size: 18px;
	text-transform: capitalize;
	color: var(--white-color);
}

.member-working-hour-list ul li span{
	border: 1px solid var(--white-color);
	width: 40%;
}

.member-working-hour-list ul li p{
	font-size: 18px;
	color: var(--white-color);
	margin: 0;
}

.member-working-history{
	background: var(--secondary-color);
	padding: 100px 0 150px;
}

.member-working-history .member-contact-form{
	background: var(--white-color);
	padding: 30px;
	border-radius: 10px;
	margin-right: 100px;
}

.member-contact-form .section-title{
	margin-bottom: 30px;
}

.member-contact-form .form-control{
	padding: 13px 20px;
	background-color: var(--secondary-color);
	color: var(--primary-color);
	box-shadow: none;
	border: none;
	border-radius: 10px;
}

.member-contact-form .form-control::placeholder{
	color: var(--text-color);
	text-transform: capitalize;
}

.member-contact-form form button{
	text-align: center;
}

.member-working-history-content .working-history-item{
	margin-bottom: 30px;
	padding-bottom: 30px;
	border-bottom: 1px solid var(--white-color);
}

.member-working-history-content .working-history-item:last-child{
	margin-bottom: 0px;
	padding-bottom: 0px;
	border-bottom: none;
}

.working-history-item .working-history-content h3{
	font-size: 22px;
	margin-bottom: 15px;
}

.working-history-item .working-history-content h3 span{
	color: var(--text-color);
}

.working-history-item .working-history-content p{
	margin: 0;
}

.member-winning-award{
	background: var(--white-color);
	padding: 100px 0 150px;
}

.member-winning-award .section-title{
	text-align: center;
}

.winning-award-image img{
	width: 100%;
	max-width: 230px;
}

/************************************/
/***     23. Video Gallery css    ***/
/************************************/

.page-header.Video-page-header::after{
	background-image: url('../images/page-header-bg-shape-2.svg');
}

.page-video-gallery{
	background: var(--white-color);
	padding: 100px 0 120px;
}

.video-gallery-image{
	border-radius: 10px;
	overflow: hidden;
	height: calc(100% - 30px);
	margin-bottom: 30px;
}

.video-gallery-image a{
	position: relative;
	cursor: none;
}

.video-gallery-image a::before{
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--black-color);
    border-radius: 10px;
    opacity: 0%;
    visibility: hidden;
    width: 100%;
    height: 100%;
    z-index: 1;
    transform: scale(0);
    transition: all 0.4s ease-in-out;
}

.video-gallery-image:hover a::before{
    opacity: 50%;
    visibility: visible;
    transform: scale(1);
}

.video-gallery-image a::after{
    content: '\f04b';
	font-family: "Font Awesome 6 Free";
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    bottom: 0;
    transform: translateY(-50%);
    width: 50px;
    height: 50px;
    margin: 0 auto;
	font-size: 20px;
	font-weight: 900;
    background: var(--white-color);
	color: var(--primary-color);
    border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
    z-index: 1;
    opacity: 0;
    visibility: hidden;
    transition: all 0.5s ease-in-out;
}

.video-gallery-image:hover a::after{
    opacity: 1;
    visibility: visible;
}

.video-gallery-image img{
	aspect-ratio: 1 / 0.86;
	object-fit: cover;
	border-radius: 10px;
}

/************************************/
/***      24. Page FAQs css       ***/
/************************************/

.page-header.faqs-page-header::after{
	background-image: url('../images/page-header-bg-shape-3.svg');
}

.page-faqs{
	background: var(--white-color);
	padding: 100px 0 150px;
}

.page-faqs-images figure{
	display: block;
}

.page-faqs-images img{
	width: 100%;
	aspect-ratio: 1 / 1.31;
	object-fit: cover;
	border-radius: 10px;
}

/************************************/
/***    25. Contact Us Page css   ***/
/************************************/

.page-header.contact-page-header::after{
	background-image: url('../images/page-header-bg-shape-4.svg');
}

.page-contact-us{
	background: var(--white-color);
	padding: 100px 0 150px;
}

.contact-information{
	margin-right: 30px;
}

.contact-information .section-title{
	margin-bottom: 20px;
}

.contact-info-list{
	margin-bottom: 40px;
}

.contact-info-list p{
	margin-bottom: 15px;
}

.contact-info-list p:last-child{
	margin-bottom: 0;
}

.contact-info-list .support-btn{
	background: url('../images/arrow-blue-readmore-btn.svg');
	background-repeat: no-repeat;
	background-position: right center;
	background-size: 14px auto;
	text-transform: capitalize;
	color: var(--primary-color);
	padding-right: 20px;
	transition: all 0.3s ease-in-out;
}

.contact-info-list .support-btn:hover{
	color: var(--text-color);
	padding-right: 22px;
}

.contact-information-box{
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	gap: 30px;
}

.contact-info-item{
	width: calc(50% - 15px);
}

.contact-info-item .icon-box{
	margin-bottom: 20px;
}

.contact-info-item .icon-box img{
	max-width: 30px;
}

.contact-info-content h3{
	font-size: 22px;
	text-transform: capitalize;
	margin-bottom: 10px;
}

.contact-info-content p{
	margin: 0;
}

.contact-us-form{
	background: var(--secondary-color);
	border-radius: 10px;
	height: 100%;
	padding: 30px;
}

.contact-us-form .section-title h2{
	font-size: 30px;
}

.contact-us-form .form-control{
	padding: 20px;
	font-size: 16px;
	background-color: var(--white-color);
	border: none;
	border-radius: 10px;
	color: var(--text-color);
	line-height: normal;
	box-shadow: none;
	outline: none;
}

.contact-us-form .form-control::placeholder{
	color: var(--text-color);
	text-transform: capitalize;
}

.contact-form-btn{
	text-align: center;
}

.contact-form-btn .btn-default{
	background: var(--white-color);
}

.google-map{
	overflow: hidden;
}

.google-map .container-fluid{
	padding: 0;
}

.google-map-iframe{
	width: 100%;
	height: 700px;
	overflow: hidden;
}

.google-map-iframe iframe{
	width: 100%;
	height: 700px;
	filter: grayscale(100%);
	transition: all 0.3s ease-in-out;
}

.google-map-iframe iframe:hover{
	filter: grayscale(0);
}

.contact-faqs{
	background: var(--white-color);
	padding: 100px 0 150px;
}

.contact-faqs-content{
	margin-right: 50px;
}

.contact-faqs-content .section-btn{
	text-align: left;
}

/*************************************/
/*** 26. Make Appointment Page css ***/
/*************************************/

.page-header.appointment-page-header::after{
	background-image: url('../images/page-header-bg-shape-5.svg');
}

.page-book-appointment{
	background: var(--white-color);
	padding: 100px 0 150px;
}

.page-book-appointment .section-title{
	width: 100%;
	max-width: 650px;
	margin: 0 auto;
	text-align: center;
}

.contact-us-form.appointment-form{
	width: 100%;
	max-width: 1080px;
	margin: 0 auto;
	background: transparent;
	padding: 0;
}

.contact-us-form.appointment-form .form-control{
	background-color: var(--secondary-color);
}

.contact-us-form.appointment-form .btn-default.btn-highlighted{
    background: var(--primary-color);
}

.contact-us-form.appointment-form .btn-default.btn-highlighted:hover{
	background-color: var(--secondary-color);
}

.booking-process{
	position: relative;
	background-color: var(--white-color);
	border-radius: 50px 50px 0 0;
	margin-top: -50px;
	padding: 100px 0 150px;
}

.booking-process .section-title{
	text-align: center;
	width: 100%;
	max-width: 750px;
	margin: 0 auto;
}

.booking-process-box{
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	gap: 30px;
}

.booking-process-item{
	position: relative;
	width: calc(25% - 22.5px);
	text-align: center;
	z-index: 1;
}

.booking-process-item::before{
	content: '';
	position: absolute;
	top: calc(50% - 30px);
	left: 0;
	right: 0;
	transform: translateY(-50%);
	border: 1px solid var(--secondary-color);
	width: 115%;
	z-index: -1;
}

.booking-process-item:nth-child(4n + 4):before{
	width: 100%;
}

.booking-process-no{
	margin-bottom: 10px;
}

.booking-process-no h3{
	font-family: var(--default-font);
	font-size: 26px;
	font-weight: 600;
	color: var(--secondary-color);
}

.booking-process-item .icon-box{
	background-color: var(--primary-color);
	border-radius: 50%;
	width: 60px;
	height: 60px;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 auto;
	margin-bottom: 20px;
}

.booking-process-item:nth-last-of-type(odd) .icon-box{
	background-color: var(--secondary-color);
}

.booking-process-item .icon-box img{
	max-width: 26px;
}

.booking-process-item:nth-last-of-type(even) .icon-box img{
	filter: brightness(0) invert(1);
}

.booking-process-content h3{
	font-size: 20px;
	text-transform: capitalize;
	margin-bottom: 15px;
}

.booking-process-content p{
	margin: 0;
}

/************************************/
/*** 	   27. 404 Page css       ***/
/************************************/

.page-header.error-page-header::after{
	background-image: url('../images/page-header-bg-shape-6.svg');
}

.error-page{
	background-color: var(--white-color);
	padding: 100px 0 150px;
}

.error-page-image{
	text-align: center;
	margin-bottom: 30px;
}

.error-page-image img{
	max-width: 50%;
}

.error-page .error-page-content{
	text-align: center;
}

.error-page-content-heading{
	margin-bottom: 30px;
}

.error-page-content-heading h2{
	font-size: 54px;
}

.error-page-content-body p{
	margin-bottom: 30px;
}

/************************************/
/***     28. responsive css       ***/
/************************************/

@media only screen and (max-width: 1366px){

	.our-services::before{
		top: -270px;
	}

	.page-about-us{
		padding-left: 0;
	}

	.page-about-us .container-fluid{
		padding: 0 15px;
	}
}

@media only screen and (max-width: 1024px){

	.navbar{
        padding: 20px 0px;
    }

	.main-menu ul li{
		margin-left: 0;
	}

	.our-services::before{
        top: -200px;
    }
}

@media only screen and (max-width: 991px){

	#magic-cursor{
        display: none !important;
    }

    .slicknav_nav li, .slicknav_nav ul{
        display: block;
    }

	.responsive-menu,
    .navbar-toggle{
        display: block;
    }

	.btn-default span{
		font-size: 16px;
		padding: 14px 18px;
	}

	.bg-radius-section{
		border-radius: 30px 30px 0 0;
	}

	.section-row{
		margin-bottom: 40px;
	}

	.section-title{
		margin-bottom: 30px;
	}

	.section-title h1{
		font-size: 56px;
	}

	.section-title h2{
		font-size: 42px;
	}

	.section-title p{
		margin-top: 15px;
	}

	.section-btn{
        text-align: left;
        margin-top: 15px;
    }

	.section-title-content{
		text-align: left;
      	margin-top: 20px;
    }

	.topbar-contact-info ul{
		justify-content: center;
		gap: 20px;
	}

	.topbar-contact-info ul li a img{
		max-width: 18px;
		margin-right: 5px;
	}

	.topbar-social-links{
		display: none;
	}

	.hero{
		padding: 150px 0 100px;
	}

	.hero.hero-slider-layout .hero-slide{
		padding: 150px 0;
	}

	.hero.hero-slider-layout .hero-pagination{
		bottom: 80px;
	}

	.hero-content{
		max-width: 100%;
	}

	.hero-content .section-title p{
		font-size: 16px;
	}

	.our-benefits{
		padding: 50px 0 80px;
	}

	.benefits-item{
		margin-bottom: 20px;
	}

	.our-benefits .col-lg-4:nth-child(2n + 2) .benefits-item{
		border: none;
		padding-right: 0;
	}

	.benefits-content h3{
		font-size: 20px;
	}

	.about-us{
		padding: 50px 0 100px;
	}

	.about-content{
		margin-bottom: 30px;
	}

	.about-content-body{
		margin-bottom: 30px;
	}

	.about-list-item .icon-box{
		margin-right: 15px;
	}

	.about-list-item .icon-box img{
		max-width: 34px;
		padding: 0 5px 5px 0;
	}

	.about-list-content{
		width: calc(100% - 49px);
	}

	.about-us-images{
		max-width: 630px;
		margin: 0 auto;
	}

	.about-circle-logo{
		width: 180px;
		height: 180px;
	}

	.about-us-images .about-circle-logo img{
		max-width: 70px;
	}

	.our-services{
		padding: 50px 0;
	}

	.our-services::before{
		top: -155px;
		background-size: 100% auto;
	}

	.our-services::after{
		background-size: 50% auto;
	}

	.service-item .icon-box{
		left: 20px;
		width: 70px;
		height: 70px;
	}

	.service-item .icon-box img{
		max-width: 46px;
	}

	.service-body{
		bottom: 20px;
		left: 20px;
	}

	.service-content h3{
		font-size: 20px;
		margin-bottom: 15px;
	}

	.service-btn a{
		width: 30px;
		height: 30px;
	}

	.service-btn a img{
		max-width: 14px;
	}

	.more-service-btn{
		margin-top: 10px;
	}

	.more-service-btn a{
		font-size: 22px;
	}

	.more-service-btn a i{
		font-size: 18px;
		margin-left: 15px;
	}

	.bg-section{
		margin-top: -250px;
	}

	.care-rehabilitation{
		padding: 50px 0 70px;
	}

	.rehab-benefits-item{
		padding: 30px 15px;
	}

	.rehab-benefits-item .icon-box img{
		max-width: 44px;
	}

	.rehab-benefits-content h3{
		font-size: 16px;
	}

	.quality-treatment{
		padding: 50px 0 100px;
		overflow: hidden;
	}

	.quality-treatment::before{
		background-size: 20% auto;
	}

	.quality-treatment::after{
		width: 130px;
	}

	.quality-treatment-body{
		margin-bottom: 30px;
	}

	.how-it-work{
		padding: 50px 0 100px;
	}

	.how-work-images{
		background-position: right -30px bottom;
		padding: 0 0px 40px 0;
	}

	.how-work-img-1 img{
		aspect-ratio: 1 / 0.95;
	}

	.how-work-img-2 img{
		aspect-ratio: 1 / 0.78;
	}

	.how-work-img-3 img{
		aspect-ratio: 1 / 1.61;
	}

	.how-work-img-4 img{
		aspect-ratio: 1 / 0.5;
	}

	.how-work-content{
		margin-bottom: 30px;
	}

	.how-work-accordion .accordion-item .accordion-header .icon-box{
		left: 20px;
		width: 50px;
		height: 50px;
	}

	.how-work-accordion .accordion-item .accordion-header .icon-box img{
		max-width: 30px;
	}

	.how-work-accordion .accordion-header .accordion-button{
		font-size: 20px;
		padding: 15px 20px 15px 90px;
	}

	.how-work-accordion .accordion-button:not(.collapsed){
		padding: 30px 20px 30px 90px;
	}

	.how-work-accordion .accordion-item .accordion-body{
		background-color: var(--primary-color);
		padding: 0px 20px 20px 20px;
	}

	.how-work-accordion .accordion-item .accordion-button::after,
	.how-work-accordion .accordion-item .accordion-button.collapsed::after{
		font-size: 18px;
		width: 18px;
    	height: 18px;
		transform: translate(-20px, -50%);
	}

	.how-work-accordion .accordion-item .accordion-button.collapsed::after{
		transform: translate(-20px, -50%) rotate(-180deg);
	}

	.our-pricing{
		padding: 50px 0 70px;
	}

	.our-pricing-content{
		margin-bottom: 60px;
	}

	.pricing-item{
		padding: 20px;
	}

	.our-pricing::before{
		background: linear-gradient(0deg, rgba(2, 48, 71, 0) 30.13%, rgba(0, 0, 0, 0.9) 100.06%, #021C29 100.7%);
	}

	.pricing-title h3{
		font-size: 24px;
	}

	.pricing-btn a{
		font-size: 18px;
		padding-right: 40px;
	}

	.pricing-btn a::before{
		background-size: 14px auto;
		width: 30px;
		height: 30px;
	}

	.therapist-team{
		padding: 50px 0 70px;
	}

	.team-image img{
		aspect-ratio: 1 / 1.1;
	}

	.team-content h3{
		font-size: 20px;
	}

	.our-testimonial{
		padding: 50px 0 100px;
		background-position: bottom right;
	}

	.testimonial-content{
		margin-bottom: 30px;
	}

	.testimonial-item{
		padding: 20px;
	}

	.testimonial-header .testimonial-rating{
		margin-bottom: 15px;
	}

	.testimonial-content{
        margin-bottom: 20px;
    }

	.testimonial-btn{
		justify-content: center;
		margin-top: 30px;
	}

	.our-blog{
		padding: 50px 0 70px;
	}

	.post-featured-image{
		margin-bottom: 20px;
	}

	.article-meta{
		margin-bottom: 15px;
	}

	.post-item-body h2{
		font-size: 20px;
	}

	.post-item-body{
		margin-bottom: 15px;
	}

	footer.main-footer{
		padding: 50px 0;
		background-size: auto;
	}

	.footer-logo{
		margin-bottom: 30px;
	}

	.about-footer{
		padding-bottom: 30px;
		margin-bottom: 30px;
	}

	.about-footer-content p{
		margin-right: 0px;
	}

	.about-footer-list{
		display: flex;
		flex-wrap: wrap;
		row-gap: 40px;
		column-gap: 30px;
	}

	.footer-links{
		width: calc(45% - 15px);
	}

	.footer-links ul li{
		margin-right: 12px;
	}

	.footer-links.service-links{
		width: calc(55% - 15px);
	}

	.footer-links.social-links{
		width: calc(25% - 15px);
		text-align: left;
	}

	.footer-links.working-links{
		width: calc(75% - 15px);
	}

	.footer-links.footer-contact-details{
		width: 100%;
	}

	.page-header{
		padding: 150px 0;
	}

	.page-header::after{
		background-size: 50% auto;
	}

	.page-header-box h1{
		font-size: 56px;
	}

	.page-header-box ol li.breadcrumb-item{
		font-size: 16px;
	}

	.page-about-us{
		padding: 50px 0 100px;
	}

	.page-about-content{
		padding-right: 0px;
		margin-bottom: 30px;
	}

	.page-about-content-body{
		margin-bottom: 30px;
	}

	.page-about-content-item{
		padding: 15px;
	}

	.page-about-content-box{
		padding: 20px;
	}

	.page-about-box-title{
		margin-bottom: 15px;
	}

	.page-about-img-1 img{
		aspect-ratio: 1 / 0.6;
	}

	.page-about-img-2{
		left: auto;
    	right: 0;
		text-align: right;
		z-index: 2;
		max-width: 30%;
	}

	.about-testimonial-box{
		width: 75%;
		padding: 15px 15px 15px 15px;
		border-radius: 30px 0 0 0;
		z-index: 1;
	}

	.about-testimonial-item p{
		font-size: 20px;
	}

	.about-testimonial-btn{
		transform: translateX(-20%);
	}

	.about-icon-box-list{
		padding: 50px 0 70px;
		border-radius: 0 30px 0 0;
		z-index: 2;
	}

	.about-icon-list-item .icon-box{
		margin-bottom: 15px;
	}

	.about-icon-list-item .icon-box img{
		max-width: 60px;
	}

	.about-icon-list-content h3{
		font-size: 20px;
	}

	.our-rehabilitation{
		padding: 50px 0 70px;
		z-index: 2;
	}

	.therapy-rehabilitation-header{
		padding: 20px;
	}

	.therapy-rehabilitation-content{
		margin-bottom: 20px;
	}

	.therapy-rehabilitation-btn a{
		width: 40px;
		height: 40px;
		backdrop-filter: blur(10px);
		-webkit-backdrop-filter: blur(10px);
	}

	.therapy-rehabilitation-title{
		width: calc(100% - 60px);
	}

	.therapy-rehabilitation-title h3{
		font-size: 24px;
	}

	.therapy-rehabilitation-body{
		padding: 20px;
	}

	.therapy-rehabilitation-list{
		padding: 15px;
	}

	.therapy-process{
		padding: 50px 0 100px;
		background-size: 50% auto;
		z-index: 2;
	}

	.therapy-process-item:hover{
		transform: translateX(0px);
	}

	.therapy-process-item .icon-box{
		width: 80px;
		height: 80px;
		margin-right: 20px;
	}

	.therapy-process-item .icon-box img{
		max-width: 40px;
	}

	.therapy-process-item-content{
		width: calc(100% - 100px);
	}

	.therapy-process-content{
		margin-right: 0px;
		margin-bottom: 30px;
	}

	.our-video{
		padding: 50px 0 100px;
		z-index: 2;
	}

	.our-video-content{
		margin-right: 0;
		margin-bottom: 30px;
	}

	.video-play-button img{
		max-width: 130px;
	}

	.process-steps{
		background-position: center center;
		background-size: 140% auto;
		padding: 50px 0 100px;
	}

	.process-steps-line{
		padding-top: 70px;
	}

	.process-step-box{
		padding-top: 60px;
	}

	.process-step-no h2{
		font-size: 94px;
	}

	.process-step-content h3{
		font-size: 20px;
	}

	.process-step-content p{
		font-size: 14px;
	}

	.page-about-faqs{
		padding: 50px 0 100px;
	}

	.about-faq-section .accordion-header .accordion-button{
		padding: 15px 45px 15px 15px;
	}

	.about-faq-section .accordion-item .accordion-body{
		padding: 15px 45px 15px 15px;
	}

	.about-faq-section .accordion-item .accordion-button::after,
	.about-faq-section .accordion-item .accordion-button.collapsed::after{
		right: 15px;
	}

	.cta-box{
		padding: 50px 0 100px;
	}

	.service-process{
		padding: 50px 0 100px;
	}

	.service-process-list{
		margin-bottom: 30px;
	}

	.service-process-list .service-process-list-item{
		margin-bottom: 20px;
		padding-bottom: 20px;
	}

	.service-process-list-item .service-process-title h3{
		font-size: 20px;
	}

	.service-process-img{
		text-align: center;
	}

	.service-process-box-content{
		text-align: left;
		margin-bottom: 30px;
	}

	.page-services{
		padding: 50px 0 70px;
	}

	.page-service-single{
		padding: 50px 0 100px;
	}

	.service-sidebar{
		position: initial;
		margin-bottom: 30px;
	}

	.service-catagery-list{
		padding: 20px;
		margin-bottom: 30px;
	}

	.service-catagery-list h3{
		font-size: 26px;
		margin-bottom: 20px;
	}

	.service-catagery-list ul li a{
		padding: 15px 35px 15px 15px;
	}

	.service-catagery-list ul li a::after{
		transform: translate(-15px, -50%);
	}

	.working-hours{
		padding: 20px;
		margin-bottom: 30px;
	}

	.working-hours h3{
		font-size: 26px;
		margin-bottom: 20px;
	}

	.working-hours ul li a{
		padding: 15px 15px 15px 45px;
	}

	.working-hours ul li a::after{
		transform: translate(15px, -50%);
		font-size: 18px;
		width: 18px;
    	height: 18px;
	}

	.sidebar-cta-image img{
		aspect-ratio: 1 / 0.5;
	}

	.sidebar-cta-item .icon-box{
		margin-bottom: 20px;
	}

	.sidebar-cta-content h3{
		font-size: 26px;
		margin-bottom: 10px;
	}

	.sidebar-cta-content p{
		font-size: 20px;
	}

	.service-entry{
		margin-bottom: 30px;
	}

	.service-entry h2{
		font-size: 32px;
	}

	.service-entry ul li{
		padding-left: 25px;
	}

	.service-entry ul li:before{
		background-size: 6px auto;
		width: 18px;
		height: 18px;
	}

	.page-blog{
		padding: 50px 0 100px;
	}


	.page-blog .blog-item{
		height: calc(100% - 30px);
		margin-bottom: 30px;
	}

	.page-pagination{
		margin-top: 10px;
	}

	.page-single-post{
		padding: 50px 0 100px;
	}

	.post-image{
		margin-bottom: 20px;
	}

	.post-entry blockquote{
		background-position: left 30px top 35px;
        padding: 25px 25px 25px 90px;
        margin-bottom: 20px;
	}

	.post-entry blockquote p{
		font-size: 18px;
	}

	.post-entry h2{
		font-size: 42px;
	}

	.post-entry ul li{
		font-size: 16px;
	}

	.post-tag-links{
		padding: 0 0px;
	}

	.post-tags{
		margin-bottom: 10px;
	}

	.post-social-sharing ul{
		text-align: left;
	}

	.post-tags .tag-links a{
		font-size: 16px;
		padding: 8px 15px;
	}

	.page-team{
		padding: 50px 0 70px;
	}

	.page-team-single{
        padding: 50px 0 100px;
    }

	.team-member-image{
		margin-bottom: 30px;
	}

	.team-member-details{
		margin-left: 0px;
	}

	.member-detail-header{
		margin-bottom: 20px;
		padding-bottom: 20px;
	}

	.member-detail-header .member-detail-title h2{
		font-size: 26px;
	}

	.member-detail-content{
		margin-bottom: 20px;
	}

	.page-team-single .team-member-image img{
		aspect-ratio: 1 / 0.85;
	}

	.member-detail-body{
		gap: 30px;
	}

	.member-detail-body .member-detail-list-item{
		width: calc(50% - 15px);
	}

	.member-detail-list-item .icon-box{
		margin-right: 15px;
	}

	.member-detail-list-item .icon-box::before{
		width: 40px;
		height: 40px;
	}

	.member-detail-list-item .icon-box img{
		max-width: 40px;
	}

	.member-detail-list-content{
		width: calc(100% - 65px);
	}

	.member-detail-list-content h3{
		font-size: 22px;
		margin-bottom: 5px;
	}

	.member-detail-list-content p{
		font-size: 16px;
	}

	.about-icon-box-list.member-expertise{
		border-radius: 30px 30px 0 0;
	}

	.member-personal-info{
		padding: 50px 0 100px;
	}

	.member-info-content{
		margin-bottom: 30px;
	}

	.member-working-hour{
		margin-left: 0;
	}

	.member-working-hour-box{
		padding: 20px;
	}

	.member-working-hour .section-title h2{
		font-size: 32px;
	}

	.member-working-hour-list ul li span{
		width: 55%;
	}

	.member-working-history{
		padding: 50px 0 100px;
	}

	.member-working-history .member-contact-form{
		padding: 20px;
		margin-right: 0;
		margin-bottom: 30px;
	}

	.member-contact-form .form-control{
		padding: 10px 20px;
	}

	.member-working-history-content .working-history-item{
		margin-bottom: 20px;
		padding-bottom: 20px;
	}

	.working-history-item .working-history-content h3{
		font-size: 20px;
		margin-bottom: 10px;
	}

	.member-winning-award{
		padding: 50px 0 100px;
	}

	.page-video-gallery{
		padding: 50px 0 70px;
	}

	.video-gallery-image img{
		aspect-ratio: 1 / 0.8;
	}

	.page-faqs{
		padding: 50px 0 100px;
	}

	.page-faqs-images{
		margin-bottom: 30px;
	}

	.page-faqs-images img{
		aspect-ratio: 1 / 0.95;
	}

	.page-contact-us{
		padding: 50px 0 100px;
	}

	.contact-information{
		margin-right: 0px;
		margin-bottom: 30px;
	}

	.contact-info-list{
		margin-bottom: 30px;
	}

	.contact-info-item .icon-box img{
		max-width: 26px;
	}

	.contact-info-item .icon-box{
		margin-bottom: 15px;
	}

	.contact-info-content h3{
		font-size: 20px;
		margin-bottom: 5px;
	}

	.contact-us-form{
		padding: 20px;
	}

	.contact-us-form .section-title h2{
		font-size: 26px;
	}

	.contact-us-form .form-control{
		padding: 15px;
	}

	.google-map-iframe iframe,
	.google-map-iframe{
		height: 500px;
	}

	.contact-faqs{
		padding: 50px 0 100px;
	}

	.contact-faqs-content{
		margin-right: 0px;
		margin-bottom: 30px;
	}

	.page-book-appointment{
		padding: 50px 0 100px;
	}

	.booking-process{
		padding: 50px 0 100px;
	}

	.booking-process-item::before{
		top: calc(50% - 65px);
		width: 120%;
	}

	.booking-process-no h3{
		font-size: 24px;
	}

	.booking-process-item .icon-box{
		margin-bottom: 15px;
	}

	.error-page{
		padding: 50px 0 100px;
	}

	.error-page-image{
		margin-bottom: 20px;
	}

	.error-page-content-heading{
		margin-bottom: 20px;
	}

	.error-page-content-heading h2{
		font-size: 42px;
	}
}

@media only screen and (max-width: 767px){

	.section-row{
        margin-bottom: 30px;
    }

	.section-title{
		margin-bottom: 20px;
	}

	.section-title h1{
		font-size: 40px;
	}

	.section-title h2{
		font-size: 30px;
	}

	.topbar-contact-info ul li.hide-mobile{
		display: none;
    }

	.topbar-contact-info ul li a img{
        max-width: 16px;
    }

	.topbar-contact-info ul li:last-child{
		margin-bottom: 0;
	}

	.hero-content-body .btn-default.btn-highlighted{
		margin: 15px 0 0 0;
	}

	.benefits-item{
        border: none;
		padding-right: 0;
    }

	.benefits-content h3{
        font-size: 18px;
    }

	.about-content-list{
		margin-bottom: 30px;
	}

	.about-circle-logo{
        width: 140px;
        height: 140px;
    }

	.about-us-images .about-circle-logo img{
        max-width: 60px;
    }

	.our-services::before{
        display: none;
    }

	.service-item .icon-box{
        width: 60px;
        height: 60px;
    }

	.service-item .icon-box img{
        max-width: 40px;
    }

	.service-content h3{
        font-size: 18px;
		margin-bottom: 10px;
    }

	.more-service-btn a{
        font-size: 18px;
    }

	.bg-section{
		padding: 100px 0;
		margin-top: -160px;
	}

	.rehab-benefits-item{
        padding: 20px 15px;
    }

	.rehab-benefits-item .icon-box{
		margin-bottom: 15px;
	}

	.quality-treatment{
		background: var(--secondary-color);
	}

	.quality-treatment::before{
		display: none;
	}

	.quality-treatment::after{
		display: none;
	}

	.how-work-image{
        max-width: 100%;
		padding: 0;
        margin-bottom: 30px;
    }

	.how-work-image::before{
        background-size: 90% auto;
		margin: 0;
    }

	.how-work-accordion .accordion-item .accordion-header .icon-box{
		left: 15px;
        width: 40px;
        height: 40px;
    }

	.how-work-accordion .accordion-item .accordion-header .icon-box img{
        max-width: 22px;
    }

	.how-work-accordion .accordion-header .accordion-button{
		font-size: 18px;
		padding: 15px 15px 15px 70px;
	}

	.how-work-accordion .accordion-button:not(.collapsed){
		padding: 20px 15px 20px 70px;
	}

	.how-work-accordion .accordion-item .accordion-body{
		padding: 0px 15px 15px 15px;
	}

	.how-work-accordion .accordion-item .accordion-button::after,
	.how-work-accordion .accordion-item .accordion-button.collapsed::after{
		font-size: 16px;
		width: 16px;
    	height: 16px;
		transform: translate(-15px, -50%);
	}

	.how-work-accordion .accordion-item .accordion-button.collapsed::after{
		transform: translate(-15px, -50%) rotate(-180deg);
	}

	.how-work-accordion .accordion-item .accordion-body p{
		font-size: 14px;
	}

	footer.main-footer{
        padding: 50px 0 50px;
    }

	.about-footer-content h3{
		font-size: 20px;
		margin-bottom: 20px;
	}

	.footer-links ul li{
		display: block;
        margin-right: 0px;
		margin-bottom: 10px;
    }

	.footer-links ul li:last-child{
		margin-bottom: 0;
	}

	.footer-links.social-links{
        width: 100%;
    }

	.footer-links.working-links{
        width: 100%;
    }

	.footer-links.social-links ul li{
		display: inline-block;
		margin-right: 10px;
		margin-bottom: 0;
	}

	.footer-links.social-links ul li:last-child{
		margin-right: 0;
	}

	.footer-contact-box{
		display: block;
	}

	.footer-info-box{
		margin-right: 0;
		margin-bottom: 10px
	}

	.footer-info-box:last-child{
		margin-bottom: 0;
	}

	.footer-terms-condition ul li{
        display: inline-block;
        margin-right: 10px;
        margin-bottom: 0px;
    }

	.page-header-box h1{
		font-size: 40px;
	}

	.page-about-content-list{
		margin-bottom: 30px;
	}

	.page-about-box-title h3{
		font-size: 18px;
	}

	.page-about-body-content{
		margin-top: 20px;
	}

	.page-about-img-2{
       position: relative;
	   max-width: 100%;
	   margin-top: -100px;
	   z-index: 1;
    }

	.about-testimonial-box{
        width: 100%;
		background: linear-gradient(0deg, #10455F 9.97%, #023047 100%);
		border-radius: 30px 30px 0 0;
		text-align: center;
		padding: 20px 20px 70px;
    }

	.about-testimonial-item p{
        font-size: 18px;
    }

	.about-testimonial-btn{
		position: relative;
		transform: translateX(0%);
		justify-content: center;
		margin-top: 15px;
	}

	.about-icon-box-list{
        padding: 50px 0 70px;
		border-radius: 30px 30px 0 0;
		margin-top: -95px;
        z-index: 2;
    }

	.about-icon-list-item .icon-box::before{
		bottom: -5px;
   		right: -5px;
	}

	.about-icon-list-item .icon-box img{
        max-width: 50px;
    }

	.about-icon-list-content h3{
        font-size: 18px;
    }

	.therapy-rehabilitation-title h3{
        font-size: 22px;
    }

	.therapy-process-item{
		display: block;
		text-align: center;
		padding: 15px;
	}

	.therapy-process-item .icon-box{
        width: 80px;
        height: 80px;
		margin: 0 auto;
		margin-bottom: 20px;
    }

	.therapy-process-item-content{
        width: 100%;
    }

	.therapy-process-item-content h3{
		font-size: 20px;
	}

	.therapy-process-img-1{
		width: 100%;
		padding: 55px 0;
	}

	.therapy-process-img-2 img{
		max-width: 200px;
		aspect-ratio: 1 / 1.01;
	}

	.therapy-process-img-3 img{
		max-width: 200px;
		aspect-ratio: 1 / 0.9;
	}

	.our-video::after{
		width: 100%;
	}

	.our-video::before{
		background-position: center center;
		width: 100%;
	}

	.video-play-button a img{
		max-width: 140px;
	}

	.process-steps{
		background-image: none;
	}

	.process-steps-line{
        padding-top: 0px;
    }

	.process-steps .col-lg-3:nth-last-child(even) .process-step-box{
		margin-top: 0px;
	}

	.process-step-box{
        padding-top: 50px;
		margin-bottom: 30px;
    }

	.process-steps .col-lg-3:last-child .process-step-box{
		margin-bottom: 0;
	}

	.process-step-no h2{
        font-size: 74px;
    }

	.process-step-content h3{
        font-size: 18px;
		margin-bottom: 5px;
    }

	.about-faq-section .accordion-header .accordion-button{
		font-size: 18px;
	}

	.about-faq-section .accordion-item .accordion-button::after,
	.about-faq-section .accordion-item .accordion-button.collapsed::after{
		font-size: 18px;
	}

	.service-process-list{
        margin-bottom: 0px;
    }

	.about-faq-section .accordion-item .accordion-body{
        padding: 15px;
    }

	.service-process-list-item .service-process-content-body{
		margin-bottom: 10px;
	}

	.service-process-list-item .service-process-title h3{
        font-size: 18px;
    }

	.service-process-list-item .service-process-btn a{
		width: 30px;
		height: 30px;
	}

	.service-process-list-item .service-process-btn img{
		max-width: 16px;
	}

	.service-process-img{
		margin-bottom: 30px;
	}

	.working-hours h3,
	.service-catagery-list h3{
        font-size: 22px;
    }

	.sidebar-cta-image img{
        aspect-ratio: 1 / 0.95;
    }

	.sidebar-cta-item .icon-box{
		width: 90px;
		height: 90px;
	}

	.sidebar-cta-item .icon-box figure{
		width: 70px;
		height: 70px;
	}

	.sidebar-cta-item .icon-box figure img{
		max-width: 30px;
	}

	.sidebar-cta-content h3{
        font-size: 22px;
    }

	.service-single-image img{
		aspect-ratio: 1 / 0.7;
	}

	.service-entry h2{
        font-size: 28px;
		margin-bottom: 15px;
    }

	.service-entry p{
		margin-bottom: 15px;
	}

	.service-entry ul{
		gap: 15px;
	}

	.service-entry ul li{
		width: 100%;
	}

	.post-image figure,
	.post-image img{
		aspect-ratio: 1 / 0.80;
	}

	.post-entry blockquote{
		background-position: left 20px top 20px;
        background-size: 35px;
        padding: 55px 20px 20px 20px;
	}

	.post-entry h2{
		font-size: 30px;
	}

	.tag-links{
		font-size: 20px;
	}

	.page-team-single .team-member-image img{
		aspect-ratio: 1 / 1.15;
	}

	.member-detail-header .member-detail-title h2{
        font-size: 22px;
    }

	.member-detail-body{
        gap: 20px;
    }

	.member-detail-body .member-detail-list-item{
        width: 100%;
    }

	.about-icon-box-list.member-expertise{
		margin-top: -50px;
	}

	.member-working-hour .section-title h2{
        font-size: 28px;
    }

	.member-working-hour-list ul li{
		justify-content: start;
	}

	.member-working-hour-list ul li span{
        display: none;
    }

	.member-working-hour-list ul li h3{
		font-size: 16px;
		margin-right: 20px;
	}

	.member-working-hour-list ul li p{
		font-size: 16px;
	}

	.contact-information-box{
		gap: 20px;
	}

	.contact-info-item{
		width: calc(50% - 10px);
	}

	.contact-info-item .icon-box img{
        max-width: 22px;
    }

	.contact-info-content p{
		font-size: 14px;
	}

	.contact-us-form .section-title h2{
        font-size: 22px;
    }

	.google-map-iframe iframe,
	.google-map-iframe{
		height: 400px;
	}

	.booking-process-item{
		width: calc(50% - 15px);
	}

	.booking-process-item::before{
		display: none;
	}

	.booking-process-content h3{
		margin-bottom: 10px;
	}

	.error-page-image img{
		max-width: 100%;
	}

	.error-page-content-heading h2{
        font-size: 30px;
	}
}























































































