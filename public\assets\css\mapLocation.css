     /* Floating Maps Button Container */
    .maps-container {
        position: fixed;
        bottom: 100px; /* Position above WhatsApp button */
        left: 20px;
        z-index: 9999;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    /* Main Button Styling */
    .maps-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 60px;
        height: 60px;
        background-color: rgba(255, 255, 255, 0.9);
        border-radius: 50%;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        border: 1px solid rgba(0, 0, 0, 0.1);
    }

    /* Google Maps Icon */
    .maps-btn img {
        width: 30px;
        height: 30px;
        transition: transform 0.3s ease;
    }

    /* Hover Effects */
    .maps-btn:hover {
        transform: scale(1.1);
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
        background-color: white;
    }

    .maps-btn:hover img {
        transform: scale(1.1);
    }

    /* <PERSON><PERSON><PERSON> - Now positioned to the right */
    .maps-tooltip {
        position: absolute;
        left: calc(100% + 10px);
        top: 50%;
        transform: translateY(-50%);
        background: #333;
        color: white;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
        white-space: nowrap;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .maps-btn:hover .maps-tooltip {
        opacity: 1;
        visibility: visible;
        left: calc(100% + 15px);
    }

    /* Mobile Responsiveness */
    @media (max-width: 768px) {
        .maps-container {
            bottom: 90px;
            left: 15px;
        }

        .maps-btn {
            width: 56px;
            height: 56px;
        }

        .maps-btn img {
            width: 28px;
            height: 28px;
        }
    }

    @media (max-width: 480px) {
        .maps-container {
            bottom: 80px;
            left: 10px;
        }

        .maps-btn {
            width: 50px;
            height: 50px;
        }

        .maps-btn img {
            width: 25px;
            height: 25px;
        }

        .maps-tooltip {
            font-size: 11px;
            padding: 5px 10px;
        }
    }
