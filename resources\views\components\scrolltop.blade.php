<div class="back-to-top">
    <svg class="back-to-top-circle" width="100%" height="100%" viewBox="-1 -1 102 102">
        <path class="back-to-top-path" d="M50,1 a49,49 0 0,1 0,98 a49,49 0 0,1 0,-98" />
    </svg>
</div>

<script>
    (function($) {
        "use strict";

        $(document).ready(function() {
            var $backToTop = $('.back-to-top');
            var $progressPath = $('.back-to-top-path');
            var pathLength = $progressPath[0].getTotalLength();

            // Initialize progress path
            $progressPath.css({
                transition: 'none',
                strokeDasharray: pathLength + ' ' + pathLength,
                strokeDashoffset: pathLength
            });

            $(window).on('scroll', function() {
                var scroll = $(this).scrollTop();
                var height = $(document).height() - $(window).height();
                var progress = pathLength - (scroll * pathLength / height);
                $progressPath.css('strokeDashoffset', progress);

                // Toggle 'active' class for back-to-top button
                if (scroll > 50) {
                    $backToTop.addClass('active');
                } else {
                    $backToTop.removeClass('active');
                }
            });

            // Scroll to top when clicking the button
            $backToTop.on('click', function(event) {
                event.preventDefault();
                $('html, body').animate({ scrollTop: 0 }, 550);
            });
        });

    })(jQuery);
</script>
