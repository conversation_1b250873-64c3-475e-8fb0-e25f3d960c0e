<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run()
    {
        $this->call([
            AboutUsSeeder::class,
            BodyPartsSeeder::class,
            CommonCausesSeeder::class,
            DepartmentSeeder::class,
            GallerySeeder::class,
            HeroSectionSeeder::class,
            StaffSeeder::class,
            ServiceSeeder::class,
            InsuranceSeeder::class,
        ]);

        User::factory()->create([
            'name' => 'Super Admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('123456789'),
        ]);
    }
}





